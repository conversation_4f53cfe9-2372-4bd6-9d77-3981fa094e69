<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

return [
    'middleware' => [
        // IP限制中间件 - 安全防护第一层
        app\adminapi\http\middleware\AdminIpMiddleware::class,
        // 初始化中间件 - 系统基础初始化
        app\adminapi\http\middleware\InitMiddleware::class,
        // 登录验证中间件 - 重新启用进行调试
        app\adminapi\http\middleware\LoginMiddleware::class,
        // 权限认证中间件 - 重新启用
        app\adminapi\http\middleware\AuthMiddleware::class,
        // 演示模式限制 - 可选（非演示环境可禁用）
        // app\adminapi\http\middleware\CheckDemoMiddleware::class,
        // 敏感数据加密 - 可选（非演示环境可禁用）
        // app\adminapi\http\middleware\EncryDemoDataMiddleware::class,
    ],
];
