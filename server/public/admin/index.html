<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>后台管理系统</title>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      .preload {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100vw;
      }

      .circular {
        height: 42px;
        width: 42px;
        animation: loading-rotate 2s linear infinite;
      }

      .circular .path {
        animation: loading-dash 1.5s ease-in-out infinite;
        stroke-dasharray: 90, 150;
        stroke-dashoffset: 0;
        stroke-width: 2;
        stroke: #4073fa;
        stroke-linecap: round;
      }

      @keyframes loading-rotate {
        100% {
          transform: rotate(1turn);
        }
      }

      @keyframes loading-dash {
        0% {
          stroke-dasharray: 90, 150;
          stroke-dashoffset: -40px;
        }

        100% {
          stroke-dasharray: 90, 150;
          stroke-dashoffset: -120px;
        }
      }
    </style>
    <script type="module" crossorigin src="/admin/assets/index.850efb0d.js"></script>
    <link rel="modulepreload" crossorigin href="/admin/assets/@vue.a11433a6.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/lodash-es.c9433054.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@vueuse.a2407f20.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@element-plus.1e23f767.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@popperjs.36402333.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@ctrl.b082b0c1.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/katex.2ad1a25a.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@iktakahiro.479df58a.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/dayjs.54121183.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/async-validator.fb49d0f5.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/memoize-one.4ee5c96d.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/escape-html.e5dfadb9.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/normalize-wheel-es.8aeb3683.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@floating-ui.cba15af0.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/element-plus.5bcb7c8a.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/lodash.9ffd80b1.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/axios.c3b81d20.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/vue-router.919c7bec.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/vue-demi.b3a9cad9.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/pinia.0d658f08.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/clone.105d8a55.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/color-name.e7a4e1d3.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/color-convert.755d189f.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/color-string.e356f5de.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/color.1cffd92e.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/css-color-function.062f6923.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/vue-drag-resize.527c6620.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/nprogress.28f32054.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/clipboard.591cd017.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/vue-clipboard3.ba321cef.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/tslib.60310f1a.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/zrender.3eba8991.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/echarts.8535e5a6.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/highlight.js.4f6161a5.js">
    <link rel="modulepreload" crossorigin href="/admin/assets/@highlightjs.f8bb3178.js">
    <link rel="stylesheet" href="/admin/assets/element-plus.25577e63.css">
    <link rel="stylesheet" href="/admin/assets/vue-drag-resize.ccfde60c.css">
    <link rel="stylesheet" href="/admin/assets/nprogress.f5128a35.css">
    <link rel="stylesheet" href="/admin/assets/highlight.b334430f.css">
    <link rel="stylesheet" href="/admin/assets/index.45fb50c0.css">
  </head>
  <body>
    <div id="app">
      <div class="preload">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
    </div>
    
  </body>
</html>
