import{P as y}from"./index.324d704f.js";import{I as B,J as L,D as P,F as R}from"./element-plus.5bcb7c8a.js";import{d as V,s as D,r as l,o as n,P as c,Q as a,W as r,$ as I,u as p,j as h,c as j,F as G,a7 as O}from"./@vue.a11433a6.js";import{u as S,b as $}from"./consumer.e39aaadf.js";import{u as J}from"./useDictOptions.583d6eb9.js";const A=V({__name:"adjust-group",emits:["close"],setup(M,{expose:_,emit:d}){const f=d,u=D(),i=l(1),m=l([]),e=l([]),{optionsData:v,refresh:N}=J({dataList:{api:S}}),E=o=>{u.value.open(),m.value=o.id,e.value=o.groupIds,i.value=o.type},g=async()=>{await $({ids:m.value,group_ids:e.value}),f("close"),u.value.close()};return _({open:E}),(o,t)=>{const C=B,F=L,b=P,w=R,k=y;return n(),c(k,{ref_key:"popRef",ref:u,width:"auto",title:`${p(i)==1?"\u8C03\u6574":"\u6279\u91CF"}\u8BBE\u7F6E\u5206\u7EC4`,onConfirm:g},{default:a(()=>[r(w,{onSubmit:t[1]||(t[1]=I(()=>{},["prevent"]))},{default:a(()=>[r(b,{label:"\u7528\u6237\u5206\u7EC4"},{default:a(()=>[r(F,{modelValue:p(e),"onUpdate:modelValue":t[0]||(t[0]=s=>h(e)?e.value=s:null),multiple:"",class:"w-[280px]"},{default:a(()=>[(n(!0),j(G,null,O(p(v).dataList,(s,x)=>(n(),c(C,{key:x,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["title"])}}});export{A as _};
