import{r as a}from"./index.850efb0d.js";function r(t){return a.get({url:"/digital.decals/lists",params:t})}function i(t){return a.post({url:"/digital.decals/add",params:t})}function s(t){return a.post({url:"/digital.decals/edit",params:t})}function l(t){return a.post({url:"/digital.decals/status",params:t})}function d(t){return a.post({url:"/digital.decals/del",params:t})}function u(t){return a.get({url:"/digital.decals/getCategoryLists",params:t})}function n(t){return a.post({url:"/digital.decals/batchEdit",params:t})}function c(t){return a.post({url:"/digital.decals/batchDel",params:t})}function o(t){return a.get({url:"/digital.decalsCategory/lists",params:t})}function g(t){return a.post({url:"/digital.decalsCategory/add",params:t})}function f(t){return a.post({url:"/digital.decalsCategory/edit",params:t})}function p(t){return a.post({url:"/digital.decalsCategory/status",params:t})}function y(t){return a.post({url:"/digital.decalsCategory/del",params:t})}export{g as a,s as b,p as c,y as d,f as e,i as f,o as g,u as h,n as i,r as j,c as k,l,d as m};
