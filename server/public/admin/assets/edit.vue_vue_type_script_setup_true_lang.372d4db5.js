import{D as z,C as T,I as j,J,t as L,F as O}from"./element-plus.5bcb7c8a.js";import{_ as Q}from"./picker.9a1dad65.js";import{g as W,j as G,k as H,l as K}from"./ai_square.bf3e05a5.js";import{P as M}from"./index.324d704f.js";import{d as X,s as w,r as Y,b as E,a0 as B,o as m,c as V,W as s,Q as r,u,a as y,F as Z,a7 as $,P as f,T as v}from"./@vue.a11433a6.js";const ee={class:"edit-popup"},ue=y("div",{class:"form-tips"},"\u5982\u4E0D\u9009\u62E9\u5206\u7C7B\uFF0C\u5219\u9ED8\u8BA4\u663E\u793A\u5728\u524D\u53F0\u7684\u3010\u5168\u90E8\u3011\u4E2D",-1),ne=X({__name:"edit",emits:["success","close"],setup(ae,{expose:C,emit:k}){const g=k,D=w(),c=w(),i=Y("add"),x=E(()=>i.value=="edit"?"\u7F16\u8F91\u7ED8\u753B":"\u65B0\u589E\u7ED8\u753B"),a=B({id:"",prompts:"",prompts_cn:"",sort:0,is_show:1,category_id:"",avatar:"",nickname:"",image:"",source:1}),p=B({loading:!0,lists:[]}),h={image:[{required:!0,message:"\u8BF7\u9009\u62E9\u56FE\u7247",trigger:["blur"]}],prompts:[{required:!0,message:"\u8BF7\u8F93\u5165\u63D0\u793A\u8BCD",trigger:["blur"]}]},d=E(()=>a.source==2),F=async()=>{p.loading=!0;try{const l=await W({type:1});p.lists=l,p.loading=!1}catch(l){p.loading=!1,console.log("\u83B7\u53D6\u7ED8\u753B\u5206\u7C7B\u5931\u8D25=>",l)}},A=async()=>{var l,e;await((l=D.value)==null?void 0:l.validate()),i.value=="edit"?await G(a):await H(a),(e=c.value)==null||e.close(),g("success")},R=()=>{g("close")},S=(l="add",e)=>{var n;i.value=l,(n=c.value)==null||n.open(),e&&U(e)},U=async l=>{var e,n;try{const t=await K({id:l});F(),b(t),a.image=t.thumbnail||t.image,a.prompts=(e=t.original_prompts)==null?void 0:e.prompt_en,a.prompts_cn=(n=t.original_prompts)==null?void 0:n.prompt}catch(t){console.log("\u83B7\u53D6\u8BE6\u60C5\u5931\u8D25=>",t)}},b=async l=>{for(const e in a)l[e]!=null&&l[e]!=null&&(a[e]=l[e])};return C({open:S,setFormData:b}),F(),(l,e)=>{const n=Q,t=z,_=T,q=j,P=J,I=L,N=O;return m(),V("div",ee,[s(M,{ref_key:"popupRef",ref:c,title:u(x),async:!0,width:"550px",onConfirm:A,onClose:R},{default:r(()=>[s(N,{class:"ls-form",ref_key:"formRef",ref:D,rules:h,model:u(a),"label-width":"100px"},{default:r(()=>[s(t,{label:"\u56FE\u7247",prop:"image"},{default:r(()=>[s(n,{modelValue:u(a).image,"onUpdate:modelValue":e[0]||(e[0]=o=>u(a).image=o),disabled:u(d)},null,8,["modelValue","disabled"])]),_:1}),s(t,{label:u(d)?"\u7528\u6237\u8F93\u5165":"\u4E2D\u6587\u63D0\u793A\u8BCD"},{default:r(()=>[s(_,{disabled:u(d),class:"w-[380px]",modelValue:u(a).prompts_cn,"onUpdate:modelValue":e[1]||(e[1]=o=>u(a).prompts_cn=o),type:"textarea",autosize:{minRows:4,maxRows:20},placeholder:u(d)?"":"\u8BF7\u8F93\u5165\u4E2D\u6587\u63D0\u793A\u8BCD"},null,8,["disabled","modelValue","placeholder"])]),_:1},8,["label"]),s(t,{label:"\u82F1\u6587\u63D0\u793A\u8BCD",prop:"prompts"},{default:r(()=>[s(_,{disabled:u(d),class:"w-[380px]",modelValue:u(a).prompts,"onUpdate:modelValue":e[2]||(e[2]=o=>u(a).prompts=o),type:"textarea",autosize:{minRows:4,maxRows:20},placeholder:"\u8BF7\u8F93\u5165\u82F1\u6587\u63D0\u793A\u8BCD"},null,8,["disabled","modelValue"])]),_:1}),s(t,{label:"\u6240\u5C5E\u5206\u7C7B",prop:"category_id"},{default:r(()=>[y("div",null,[s(P,{class:"w-[380px]",modelValue:u(a).category_id,"onUpdate:modelValue":e[3]||(e[3]=o=>u(a).category_id=o),placeholder:"\u5168\u90E8"},{default:r(()=>[(m(!0),V(Z,null,$(u(p).lists,o=>(m(),f(q,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),ue])]),_:1}),u(i)!="edit"||u(a).source==1?(m(),f(t,{key:0,label:"\u7528\u6237\u5934\u50CF",prop:"avatar"},{default:r(()=>[s(n,{modelValue:u(a).avatar,"onUpdate:modelValue":e[4]||(e[4]=o=>u(a).avatar=o),disabled:u(d)},null,8,["modelValue","disabled"])]),_:1})):v("",!0),u(i)!="edit"||u(a).source==1?(m(),f(t,{key:1,label:"\u7528\u6237\u6635\u79F0"},{default:r(()=>[s(_,{disabled:u(d),class:"w-[380px]",modelValue:u(a).nickname,"onUpdate:modelValue":e[5]||(e[5]=o=>u(a).nickname=o),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0"},null,8,["disabled","modelValue"])]),_:1})):v("",!0),s(t,{label:"\u662F\u5426\u663E\u793A",prop:"is_show"},{default:r(()=>[s(I,{modelValue:u(a).is_show,"onUpdate:modelValue":e[6]||(e[6]=o=>u(a).is_show=o),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{ne as _};
