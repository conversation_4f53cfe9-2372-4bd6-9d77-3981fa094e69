import{C as E,D as b,t as y,F as g}from"./element-plus.5bcb7c8a.js";import{p as h,a as x}from"./ai_creation.a70c015c.js";import{P as k}from"./index.324d704f.js";import{d as R,s as c,r as A,b as U,a0 as I,o as N,c as P,W as a,Q as l,u,a as f}from"./@vue.a11433a6.js";const S={class:"edit-popup"},q=f("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u503C\u8D8A\u5927\u6392\u8D8A\u524D\u9762",-1),J=R({__name:"edit",emits:["success","close"],setup(Q,{expose:_,emit:F}){const p=F,i=c(),n=c(),r=A("add"),v=U(()=>r.value=="edit"?"\u7F16\u8F91\u7C7B\u522B":"\u65B0\u589E\u7C7B\u522B"),o=I({id:"",name:"",image:"",sort:0,status:1}),C={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:["blur"]}]},B=async()=>{var t,e;await((t=i.value)==null?void 0:t.validate()),r.value=="edit"?await h(o):await x(o),(e=n.value)==null||e.close(),p("success")},D=()=>{p("close")};return _({open:(t="add")=>{var e;r.value=t,(e=n.value)==null||e.open()},setFormData:async t=>{for(const e in o)t[e]!=null&&t[e]!=null&&(o[e]=t[e])}}),(t,e)=>{const d=E,m=b,V=y,w=g;return N(),P("div",S,[a(k,{ref_key:"popupRef",ref:n,title:u(v),async:!0,width:"550px",onConfirm:B,onClose:D},{default:l(()=>[a(w,{class:"ls-form",ref_key:"formRef",ref:i,rules:C,model:u(o),"label-width":"90px"},{default:l(()=>[a(m,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name"},{default:l(()=>[a(d,{class:"ls-input",modelValue:u(o).name,"onUpdate:modelValue":e[0]||(e[0]=s=>u(o).name=s),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:""},null,8,["modelValue"])]),_:1}),a(m,{label:"\u6392\u5E8F",prop:"sort"},{default:l(()=>[f("div",null,[a(d,{class:"ls-input",modelValue:u(o).sort,"onUpdate:modelValue":e[1]||(e[1]=s=>u(o).sort=s)},null,8,["modelValue"]),q])]),_:1}),a(m,{label:"\u72B6\u6001",prop:"sort"},{default:l(()=>[a(V,{modelValue:u(o).status,"onUpdate:modelValue":e[2]||(e[2]=s=>u(o).status=s),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{J as _};
