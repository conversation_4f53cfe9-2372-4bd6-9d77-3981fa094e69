import{P as y}from"./index.324d704f.js";import{_ as g}from"./oa-menu-form.vue_vue_type_script_setup_true_lang.609c965e.js";import{d as F,s as m,k,o as v,P,Q as u,J as R,W as c}from"./@vue.a11433a6.js";const U=F({__name:"oa-menu-form-edit",props:{modular:{default:"edit"},subItem:{default:{}}},emits:["add","edit"],setup(d,{emit:i}){const s=i,p=d,n=m(),r=m(),e={name:"",type:"view",url:"",appid:"",pagepath:""};k(()=>{if(Object.keys(p.subItem).length!=0)for(const o in e)e[o]=p.subItem[o]});const l=async()=>{await n.value.menuFormRef.validate(),p.modular==="edit"?s("edit",{...e}):s("add",{...e}),r.value.close(),n.value.menuFormRef.resetFields()};return(o,t)=>{const f=y;return v(),P(f,{ref_key:"menuFromPopupRef",ref:r,async:"",clickModalClose:!1,title:`${o.modular==="add"?"\u65B0\u589E":"\u7F16\u8F91"}\u5B50\u83DC\u5355`,onConfirm:l},{trigger:u(()=>[R(o.$slots,"default")]),default:u(()=>[c(g,{ref_key:"menuFormEditRef",ref:n,modular:"secondary",name:e.name,"onUpdate:name":t[0]||(t[0]=a=>e.name=a),visitType:e.type,"onUpdate:visitType":t[1]||(t[1]=a=>e.type=a),url:e.url,"onUpdate:url":t[2]||(t[2]=a=>e.url=a),appId:e.appid,"onUpdate:appId":t[3]||(t[3]=a=>e.appid=a),pagePath:e.pagepath,"onUpdate:pagePath":t[4]||(t[4]=a=>e.pagepath=a)},null,8,["name","visitType","url","appId","pagePath"])]),_:3},8,["title"])}}});export{U as _};
