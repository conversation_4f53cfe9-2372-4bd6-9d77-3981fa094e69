import{Q as h,R as y,C as A,D as S,F as k}from"./element-plus.5bcb7c8a.js";import{P as N}from"./index.324d704f.js";import{r as l}from"./index.850efb0d.js";import{d as U,r as F,s as v,b as q,o as I,c as P,W as s,Q as n,u as a,a as C,U as B}from"./@vue.a11433a6.js";function M(t){return l.get({url:"/setting.sensitiveWord/lists",params:t})}function Q(t){return l.post({url:"/setting.sensitiveWord/add",data:t})}function T(t){return l.post({url:"/setting.sensitiveWord/edit",data:t})}function O(t){return l.post({url:"/setting.sensitiveWord/del",data:t})}function X(t){return l.post({url:"/setting.sensitiveWord/setConfig",data:t})}function Y(){return l.get({url:"/setting.sensitiveWord/getConfig"})}const j={class:"edit-popup"},z=C("div",{class:"form-tips"},"\u8F93\u5165\u654F\u611F\u8BCD\u8BED\uFF0C\u6DFB\u52A0\u591A\u4E2A\u6309enter\u56DE\u8F66\u6362\u884C",-1),Z=U({__name:"edit",emits:["success","close"],setup(t,{expose:w,emit:g}){const o=F({id:"",word:"",status:1}),E={word:[{required:!0,message:"\u8BF7\u8F93\u5165\u654F\u611F\u8BCD\u8BED"}]},m=g,c=v(),i=v(),d=F("add"),D=q(()=>d.value=="edit"?"\u7F16\u8F91\u654F\u611F\u8BCD":"\u81EA\u5B9A\u4E49\u654F\u611F\u8BCD"),R=(u="add")=>{var e;d.value=u,(e=i.value)==null||e.open()},x=async()=>{var e,r;await((e=c.value)==null?void 0:e.validate());const u={...o.value,word:o.value.word.split(`
`).filter(Boolean)};d.value=="add"?await Q(u):await T(u),(r=i.value)==null||r.close(),m("close")},V=()=>{m("close")};return w({open:R,setFormData:u=>{for(const e in o.value)u[e]!=null&&u[e]!=null&&(o.value[e]=u[e]);o.value.word=u.word_arr.join(`
`)}}),(u,e)=>{const r=A,f=S,_=h,b=y,W=k;return I(),P("div",j,[s(N,{ref_key:"popupRef",ref:i,title:a(D),async:!0,width:"550px",onConfirm:x,onClose:V},{default:n(()=>[s(W,{ref_key:"formRef",ref:c,model:a(o),rules:E,"label-width":"80px"},{default:n(()=>[s(f,{label:"\u654F\u611F\u8BCD\u8BED",prop:"word"},{default:n(()=>[C("div",null,[s(r,{class:"w-[340px]",type:"textarea",autosize:{minRows:6,maxRows:4},modelValue:a(o).word,"onUpdate:modelValue":e[0]||(e[0]=p=>a(o).word=p),placeholder:`\u5982\uFF1A\u9886\u8896
\u653F\u6CBB\u4F53\u7CFB
\u653F\u6CBB\u4E8B\u4EF6`},null,8,["modelValue"]),z])]),_:1}),s(f,{label:"\u654F\u611F\u72B6\u6001"},{default:n(()=>[s(b,{modelValue:a(o).status,"onUpdate:modelValue":e[1]||(e[1]=p=>a(o).status=p)},{default:n(()=>[s(_,{label:1},{default:n(()=>[B("\u542F\u7528")]),_:1}),s(_,{label:0},{default:n(()=>[B("\u505C\u7528")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{Z as _,M as a,O as d,Y as g,X as s};
