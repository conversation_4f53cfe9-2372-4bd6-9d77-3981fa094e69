import{E as v}from"./element-plus.5bcb7c8a.js";import{b}from"./index.850efb0d.js";import{d as x,b as y,o,c as r,W as m,Q as S,F as k,a7 as C,K as n,a as l,T as I,J as z,L as F,u as N,bk as B,bj as D}from"./@vue.a11433a6.js";import{_ as E}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const V=t=>(B("data-v-ee869fd7"),t=t(),D(),t),$=["onClick"],j={key:0,class:"flex text-white text-[20px] items-center"},L=V(()=>l("span",{class:"ml-[8px]"},"\u5DF2\u9690\u85CF",-1)),A=x({__name:"preview",props:{pageData:{type:Array,default:()=>[]},index:{type:Number,default:0},width:{type:String,default:"100%"},height:{type:String,default:"auto"}},emits:["update:index"],setup(t,{emit:c}){const a=t,d=y(()=>({width:a.width,height:a.height})),u=c,_=(s,i)=>{s.disabled||u("update:index",i)};return(s,i)=>{const f=b,h=v;return o(),r("div",{class:"pages-preview",style:F(N(d))},[m(h,null,{default:S(()=>[(o(!0),r(k,null,C(t.pageData,(e,p)=>(o(),r("div",{key:e.id,class:n(["relative widget-item",{"cursor-pointer":!(e!=null&&e.disabled)}]),onClick:H=>_(e,p)},[l("div",{class:n(["absolute w-full h-full z-[100] border-dashed widget-place flex items-center justify-center",{select:p==t.index,"border-[#dcdfe6] border-2":!(e!=null&&e.disabled),"is-hidden":!(e!=null&&e.isShow)}])},[e!=null&&e.isShow?I("",!0):(o(),r("div",j,[m(f,{name:"el-icon-Hide",size:30}),L]))],2),z(s.$slots,"default",{widget:e},void 0,!0)],10,$))),128))]),_:3})],4)}}});const xe=E(A,[["__scopeId","data-v-ee869fd7"]]);export{xe as default};
