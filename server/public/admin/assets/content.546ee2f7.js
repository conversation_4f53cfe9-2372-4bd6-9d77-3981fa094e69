import{b as h}from"./index.850efb0d.js";import{d as x,r as f,b,o as i,c as r,a as t,V as c,R as v,ak as g,u as p,j as y,W as m,F as w,a7 as I,bk as j,bj as k}from"./@vue.a11433a6.js";import S from"./decoration-img.16e6b284.js";import{_ as D}from"./vue-drag-resize.527c6620.js";import"./element-plus.5bcb7c8a.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";const F="/admin/assets/app_center_robot.6c84b9bb.png",V=e=>(j("data-v-0e3b92b0"),e=e(),k(),e),B={class:"app-center"},C={class:"header"},E={class:"flex items-center"},L=V(()=>t("img",{src:F},null,-1)),N={class:"title"},O={class:"flex items-center search-container"},R={class:"flex justify-center items-center w-[40px] h-[40px]"},A={class:"main"},M={class:"grid grid-cols-2",style:{gap:"12px"}},Q={class:"flex-none"},T={class:"ml-[10px] py-[10px]"},U={class:"font-medium text-tx-primary text-lg line-clamp-1"},W={class:"text-tx-secondary text-xs mt-1 line-clamp-1"},$=x({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(e){const l=e,o=f(""),d=b(()=>l.content.data.filter(a=>a.is_show==1)||[]);return(a,n)=>{const _=h;return i(),r("div",B,[t("div",C,[t("div",E,[L,t("span",N,c(e.content.mobile_title),1)]),t("div",O,[v(t("input",{"onUpdate:modelValue":n[0]||(n[0]=s=>y(o)?o.value=s:null),class:"search flex-1",placeholder:"\u8F93\u5165\u60A8\u60F3\u641C\u7D22\u7684\u5E94\u7528",placeholderClass:"search-placeholder"},null,512),[[g,p(o)]]),t("div",R,[m(_,{name:"el-icon-Search"})])])]),t("div",A,[t("div",M,[(i(!0),r(w,null,I(p(d),(s,u)=>(i(),r("div",{class:"flex items-center p-[10px] bg-white rounded-lg",key:u},[t("div",Q,[m(S,{src:s.image,width:"44",height:"44","border-radius":"18"},null,8,["src"])]),t("div",T,[t("div",U,c(s.title),1),t("span",W,c(s.desc),1)])]))),128))])])])}}});const Ft=D($,[["__scopeId","data-v-0e3b92b0"]]);export{Ft as default};
