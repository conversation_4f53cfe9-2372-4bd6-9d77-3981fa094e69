import{Q as x,R as w,D as R,C as b,F as h}from"./element-plus.5bcb7c8a.js";import{P as y}from"./index.324d704f.js";import{n as B}from"./distribution.4cae9c42.js";import{d as v,s as N,r as _,o as d,c as P,W as t,Q as a,u as s,U as f,P as U,T as I}from"./@vue.a11433a6.js";const Q={class:"edit-popup"},j=v({__name:"transfer",emits:["success","close"],setup(T,{expose:c,emit:i}){const l=i,u=N(),e=_({id:"",transfer_status:1,transfer_remark:""}),F=n=>{var o;(o=u.value)==null||o.open(),e.value.id=n},C=()=>{l("close")},D=async()=>{await B(e.value),l("close")};return c({open:F}),_(1),(n,o)=>{const m=x,E=w,p=R,V=b,k=h;return d(),P("div",Q,[t(y,{ref_key:"popupRef",ref:u,title:"\u8F6C\u8D26",async:!0,width:"550px",onConfirm:D,onClose:C},{default:a(()=>[t(k,{ref:"formRef",class:"mb-[-16px]",model:s(e)},{default:a(()=>[t(p,{label:"\u8F6C\u8D26\u72B6\u6001"},{default:a(()=>[t(E,{modelValue:s(e).transfer_status,"onUpdate:modelValue":o[0]||(o[0]=r=>s(e).transfer_status=r),class:"ml-4"},{default:a(()=>[t(m,{label:1},{default:a(()=>[f("\u8F6C\u8D26\u6210\u529F")]),_:1}),t(m,{label:2},{default:a(()=>[f("\u8F6C\u8D26\u5931\u8D25")]),_:1})]),_:1},8,["modelValue"])]),_:1}),s(e).transfer_status==2?(d(),U(p,{key:0,label:"\u62D2\u7EDD\u539F\u56E0"},{default:a(()=>[t(V,{type:"textarea",rows:5,placeholder:"\u8BF7\u8F93\u5165\u5931\u8D25\u539F\u56E0",modelValue:s(e).transfer_remark,"onUpdate:modelValue":o[1]||(o[1]=r=>s(e).transfer_remark=r)},null,8,["modelValue"])]),_:1})):I("",!0)]),_:1},8,["model"])]),_:1},512)])}}});export{j as _};
