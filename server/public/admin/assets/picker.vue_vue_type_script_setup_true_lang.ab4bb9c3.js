import{X as W,C as $,w as M,E as I,a as L}from"./element-plus.5bcb7c8a.js";import{w as z,x as T,b as P}from"./index.850efb0d.js";import{d as R,r as U,s as D,a0 as j,b as K,w as O,i as Q,o as i,c as r,W as n,Q as a,u as b,P as X,F as _,U as q,a as u,$ as h,a7 as k,K as A,V as G,n as w}from"./@vue.a11433a6.js";import{e as H}from"./@vueuse.a2407f20.js";const J={class:"icon-select"},Y={class:"flex justify-between"},Z=u("div",{class:"mb-3"},"\u8BF7\u9009\u62E9\u56FE\u6807",-1),ee=["onClick"],oe={class:"h-[280px]"},se={class:"flex flex-wrap"},te={key:0,class:"flex items-center"},ce=R({__name:"picker",props:{modelValue:{default:""},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(le,{emit:C}){const p=C,v=U(0),V=[{name:"element\u56FE\u6807",icons:z()},{name:"\u672C\u5730\u56FE\u6807",icons:T()}],d=D(),e=j({inputValue:"",popoverVisible:!1,popoverWidth:0,mouseoverSelect:!1,inputFocus:!1}),E=()=>{e.inputFocus=e.popoverVisible=!0},F=()=>{e.inputFocus=!1,e.popoverVisible=e.mouseoverSelect},y=o=>{e.mouseoverSelect=e.popoverVisible=!1,p("update:modelValue",o),p("change",o)},g=()=>{p("update:modelValue",""),p("change","")},x=K(()=>{var l,c;const o=(c=(l=V[v.value])==null?void 0:l.icons)!=null?c:[];if(!e.inputValue)return o;const s=e.inputValue.toLowerCase();return o.filter(m=>{if(m.toLowerCase().indexOf(s)!==-1)return m})}),B=()=>{w(()=>{var s;const o=(s=d.value)==null?void 0:s.$el.offsetWidth;e.popoverWidth=o<300?300:o})};return H(document.body,"click",()=>{e.popoverVisible=!!(e.inputFocus||e.mouseoverSelect)}),O(()=>e.popoverVisible,async o=>{var s,l;await w(),o?(s=d.value)==null||s.focus():(l=d.value)==null||l.blur()}),Q(()=>{B()}),(o,s)=>{const l=P,c=M,m=I,S=L,N=W;return i(),r("div",J,[n(N,{trigger:"contextmenu",visible:e.popoverVisible,"onUpdate:visible":s[3]||(s[3]=t=>e.popoverVisible=t),width:e.popoverWidth},{reference:a(()=>[n(b($),{ref_key:"inputRef",ref:d,modelValue:e.inputValue,"onUpdate:modelValue":s[2]||(s[2]=t=>e.inputValue=t),modelModifiers:{trim:!0},placeholder:"\u641C\u7D22\u56FE\u6807",autofocus:!1,disabled:o.disabled,onFocus:E,onBlur:F,clearable:""},{prepend:a(()=>[o.modelValue?(i(),r("div",te,[n(S,{class:"flex-1 w-20",content:o.modelValue,placement:"top"},{default:a(()=>[(i(),X(l,{class:"mr-1",key:o.modelValue,name:o.modelValue,size:16},null,8,["name"]))]),_:1},8,["content"])])):(i(),r(_,{key:1},[q("\u65E0")],64))]),append:a(()=>[n(c,null,{default:a(()=>[n(l,{name:"el-icon-Close",size:18,onClick:g})]),_:1})]),_:1},8,["modelValue","disabled"])]),default:a(()=>[u("div",{onMouseover:s[0]||(s[0]=h(t=>e.mouseoverSelect=!0,["stop"])),onMouseout:s[1]||(s[1]=h(t=>e.mouseoverSelect=!1,["stop"]))},[u("div",null,[u("div",Y,[Z,u("div",null,[(i(),r(_,null,k(V,(t,f)=>u("span",{key:f,class:A(["cursor-pointer text-sm ml-2",{"text-primary":f==b(v)}]),onClick:ne=>v.value=f},G(t.name),11,ee)),64))])]),u("div",oe,[n(m,null,{default:a(()=>[u("div",se,[(i(!0),r(_,null,k(x.value,t=>(i(),r("div",{key:t,class:"m-1"},[n(c,{onClick:f=>y(t)},{default:a(()=>[n(l,{name:t,size:18},null,8,["name"])]),_:2},1032,["onClick"])]))),128))])]),_:1})])])],32)]),_:1},8,["visible","width"])])}}});export{ce as _};
