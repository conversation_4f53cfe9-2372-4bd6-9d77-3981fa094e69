import{Q as C,R as w,D as B,C as F,F as b}from"./element-plus.5bcb7c8a.js";import{z as k}from"./ai_square.bf3e05a5.js";import{P as h}from"./index.324d704f.js";import{d as A,s as _,a0 as g,o as d,c as z,W as t,Q as a,u as s,U as c,P as N,T as P}from"./@vue.a11433a6.js";const U={class:"audit-popup"},W=A({__name:"audit",emits:["success","close"],setup(I,{expose:f,emit:v}){const n=v,i=_(),l=_(),o=g({id:[],verify_status:1,verify_result:""}),y={},D=async()=>{var u,e;await((u=i.value)==null?void 0:u.validate()),await k(o),(e=l.value)==null||e.close(),n("success")},E=()=>{n("close")};return f({open:u=>{var e;o.id=u,(e=l.value)==null||e.open()}}),(u,e)=>{const m=C,R=w,p=B,V=F,x=b;return d(),z("div",U,[t(h,{ref_key:"popupRef",ref:l,title:"\u5BA1\u6838",async:!0,width:"550px",onConfirm:D,onClose:E},{default:a(()=>[t(x,{class:"ls-form",ref_key:"formRef",ref:i,rules:y,model:s(o),"label-width":"90px"},{default:a(()=>[t(p,{label:"\u5BA1\u6838\u7ED3\u679C",prop:"image"},{default:a(()=>[t(R,{modelValue:s(o).verify_status,"onUpdate:modelValue":e[0]||(e[0]=r=>s(o).verify_status=r)},{default:a(()=>[t(m,{label:1,size:"large"},{default:a(()=>[c("\u5BA1\u6838\u901A\u8FC7")]),_:1}),t(m,{label:2,size:"large"},{default:a(()=>[c("\u5BA1\u6838\u62D2\u7EDD")]),_:1})]),_:1},8,["modelValue"])]),_:1}),s(o).verify_status==2?(d(),N(p,{key:0,label:"\u62D2\u7EDD\u539F\u56E0",prop:"verify_result"},{default:a(()=>[t(V,{modelValue:s(o).verify_result,"onUpdate:modelValue":e[1]||(e[1]=r=>s(o).verify_result=r),type:"textarea",autosize:{minRows:8,maxRows:20},placeholder:"\u8BF7\u8F93\u5165\u62D2\u7EDD\u539F\u56E0"},null,8,["modelValue"])]),_:1})):P("",!0)]),_:1},8,["model"])]),_:1},512)])}}});export{W as _};
