import{x as Z,y as ee,t as te,D as ae,F as le,G as oe,I as ue,J as ne,w as se,K as ie,L as me,M as re}from"./element-plus.5bcb7c8a.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{_ as pe}from"./index.vue_vue_type_script_setup_true_lang.67cfcc66.js";import{u as ce}from"./usePaging.b48cb079.js";import{b as _e}from"./ai_key.2945027e.js";import{g as U,_ as fe,s as ye,d as be,a as ve,b as Ce}from"./edit.vue_vue_type_script_setup_true_lang.56cfa49c.js";import{f as D}from"./index.850efb0d.js";import{d as Fe,r as h,s as ge,a0 as he,b as we,aj as Ee,o as n,c as w,W as e,Q as l,u as a,a as C,F as V,a7 as x,P as m,U as b,j as $,R as v,T as Be}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const ke=C("div",{class:"text-xl font-medium mb-[20px]"},"\u529F\u80FD\u72B6\u6001",-1),De=C("div",{class:"form-tips"},"\u5F00\u542F\u65F6\uFF0C\u5982\u679CKey\u5DF2\u5931\u6548\u7684\u8BDD\u4F1A\u81EA\u52A8\u4E0B\u67B6Key",-1),Ve={class:"mb-[10px]"},xe={class:"flex justify-end mt-4"},Ct=Fe({__name:"index",setup(Ae){const L=h(!0),A=ge(),E=h([]),s=he({type:1,channel:"",model_id:"",keyword:"",start_time:"",end_time:"",status:""}),N=we(()=>[1,2].includes(Number(s.type))),B=h(1),p=h({key_auto_down:1}),z=[{name:"AI\u5BF9\u8BDD",type:1},{name:"\u5411\u91CF\u8BAD\u7EC3",type:2},{name:"\u8BED\u97F3\u64AD\u62A5",type:3},{name:"\u8BED\u97F3\u8F93\u5165",type:4}],{pager:d,getLists:c,resetPage:M,resetParams:j}=ce({fetchFun:U,params:s}),I=u=>{s.type=u,c(),K()},K=async()=>{try{const u=await _e({type:s.type});E.value=u}catch(u){console.log("\u83B7\u53D6ai\u6A21\u578B\u5931\u8D25=>",u)}},q=u=>{ye({id:u}),D.msgSuccess("\u64CD\u4F5C\u6210\u529F")},R=(u,o)=>{var _;(_=A.value)==null||_.open(s.type,u,o)},G=async u=>{await D.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await be({id:u}),D.msgSuccess("\u64CD\u4F5C\u6210\u529F"),c()},P=async()=>{p.value=await ve()},J=async()=>{try{await Ce(p.value)}finally{P()}};return P(),c(),K(),(u,o)=>{const _=te,F=ae,T=le,k=oe,f=ue,S=ne,y=se,O=pe,Q=Z,W=ee,r=ie,H=me,X=de,g=Ee("perms"),Y=re;return n(),w("div",null,[e(k,{shadow:"never",class:"!border-none"},{default:l(()=>[e(T,{class:"ls-form",model:a(p),"label-width":"120px"},{default:l(()=>[ke,e(F,{label:"key\u6C60\u89C4\u5219",prop:"key_auto_down"},{default:l(()=>[C("div",null,[e(_,{"active-value":1,"inactive-value":0,modelValue:a(p).key_auto_down,"onUpdate:modelValue":o[0]||(o[0]=t=>a(p).key_auto_down=t),onChange:J},null,8,["modelValue"]),De])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,{class:"!border-none mt-4",shadow:"never"},{default:l(()=>[e(T,{ref:"formRef",class:"mb-[-16px]",model:a(s),inline:!0},{default:l(()=>[e(F,{label:"\u63A5\u53E3\u7C7B\u578B"},{default:l(()=>[e(S,{class:"w-[280px] mr-3",modelValue:a(s).channel,"onUpdate:modelValue":o[1]||(o[1]=t=>a(s).channel=t)},{default:l(()=>[a(N)?(n(!0),w(V,{key:0},x(a(E),t=>(n(),m(f,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128)):(n(!0),w(V,{key:1},x(a(E),(t,i)=>(n(),m(f,{key:i,label:t,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(F,{label:"\u72B6\u6001"},{default:l(()=>[e(S,{class:"w-[280px]",modelValue:a(s).status,"onUpdate:modelValue":o[2]||(o[2]=t=>a(s).status=t)},{default:l(()=>[e(f,{label:"\u5168\u90E8",value:""}),e(f,{label:"\u5F00\u542F",value:1}),e(f,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(F,null,{default:l(()=>[e(y,{type:"primary",onClick:a(M)},{default:l(()=>[b("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(y,{onClick:a(j)},{default:l(()=>[b("\u91CD\u7F6E")]),_:1},8,["onClick"]),e(O,{class:"ml-2.5","fetch-fun":a(U),params:a(s),"page-size":a(d).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,{shadow:"never",class:"!border-none mt-4"},{default:l(()=>[e(W,{modelValue:a(B),"onUpdate:modelValue":o[3]||(o[3]=t=>$(B)?B.value=t:null),onTabChange:I},{default:l(()=>[(n(),w(V,null,x(z,(t,i)=>e(Q,{label:`${t.name}`,name:t.type,key:i},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),C("div",Ve,[v((n(),m(y,{type:"primary",onClick:o[4]||(o[4]=t=>R("add"))},{default:l(()=>[b(" + \u65B0\u589E\u89C4\u5219 ")]),_:1})),[[g,["setting.KeyRule/add"]]])]),v((n(),m(H,{size:"large",data:a(d).lists},{default:l(()=>[e(r,{label:"\u63A5\u53E3\u7C7B\u578B",prop:"channel","min-width":"100"}),e(r,{label:"\u505C\u7528\u89C4\u5219",prop:"rule","min-width":"200"}),e(r,{label:"\u505C\u7528\u63D0\u793A",prop:"prompt","min-width":"140"}),v((n(),m(r,{label:"\u72B6\u6001","min-width":"100"},{default:l(({row:t})=>[e(_,{modelValue:t.status,"onUpdate:modelValue":i=>t.status=i,"active-value":1,"inactive-value":0,onChange:i=>q(t.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})),[[g,["setting.KeyRule/status"]]]),e(r,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"180"}),e(r,{label:"\u66F4\u65B0\u65F6\u95F4",prop:"update_time","min-width":"180"}),e(r,{label:"\u64CD\u4F5C",fixed:"right","min-width":"180"},{default:l(({row:t})=>[v((n(),m(y,{type:"primary",link:"",onClick:i=>R("edit",t)},{default:l(()=>[b(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[g,["setting.KeyRule/edit"]]]),v((n(),m(y,{type:"danger",link:!0,onClick:i=>G(t.id)},{default:l(()=>[b(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["setting.KeyRule/del"]]])]),_:1})]),_:1},8,["data"])),[[Y,a(d).loading]]),C("div",xe,[e(X,{modelValue:a(d),"onUpdate:modelValue":o[5]||(o[5]=t=>$(d)?d.value=t:null),onChange:a(c)},null,8,["modelValue","onChange"])])]),_:1}),a(L)?(n(),m(fe,{key:0,ref_key:"editRef",ref:A,onSuccess:a(c)},null,8,["onSuccess"])):Be("",!0)])}}});export{Ct as default};
