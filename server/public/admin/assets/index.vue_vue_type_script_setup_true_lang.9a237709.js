import{_ as F,x as v,y as C,G as w,F as y}from"./element-plus.5bcb7c8a.js";import{_ as D}from"./base-config.vue_vue_type_script_setup_true_lang.60f4f266.js";import{_ as R}from"./search-config.vue_vue_type_script_setup_true_lang.4cd21630.js";import{_ as $}from"./interface-config.vue_vue_type_script_setup_true_lang.3480a434.js";import{_ as g}from"./digital-config.vue_vue_type_script_setup_true_lang.2a4004a5.js";import"./index.850efb0d.js";import{d as k,s as z,at as B,b as T,o as U,c as x,W as e,Q as u,u as n,j as s,F as A}from"./@vue.a11433a6.js";const Q=k({__name:"index",props:{headerTitle:{},modelValue:{}},emits:["update:modelValue","submit"],setup(r,{emit:f}){const _=r,p=f,i=z(),V=B({}),l=T({get(){return _.modelValue},set(t){p("update:modelValue",t)}});return(t,a)=>{const b=F,d=w,m=v,c=C,E=y;return U(),x(A,null,[e(d,{class:"!border-none",shadow:"never"},{default:u(()=>[e(b,{content:t.headerTitle,onBack:a[0]||(a[0]=o=>t.$router.back())},null,8,["content"])]),_:1}),e(d,{class:"!border-none mt-4",shadow:"never"},{default:u(()=>[e(E,{ref_key:"formRef",ref:i,model:t.modelValue,"label-width":"120px",rules:n(V),disabled:!0},{default:u(()=>[e(c,{"model-value":"base"},{default:u(()=>[e(m,{lazy:"",label:"\u57FA\u672C\u914D\u7F6E",name:"base"},{default:u(()=>[e(D,{modelValue:n(l),"onUpdate:modelValue":a[1]||(a[1]=o=>s(l)?l.value=o:null)},null,8,["modelValue"])]),_:1}),e(m,{lazy:"",label:"AI\u6A21\u578B/\u641C\u7D22\u914D\u7F6E",name:"search"},{default:u(()=>[e(R,{modelValue:n(l),"onUpdate:modelValue":a[2]||(a[2]=o=>s(l)?l.value=o:null)},null,8,["modelValue"])]),_:1}),e(m,{lazy:"",label:"\u754C\u9762\u914D\u7F6E",name:"interface"},{default:u(()=>[e($,{modelValue:n(l),"onUpdate:modelValue":a[3]||(a[3]=o=>s(l)?l.value=o:null)},null,8,["modelValue"])]),_:1}),e(m,{lazy:"",label:"\u5F62\u8C61\u914D\u7F6E",name:"digital"},{default:u(()=>[e(g,{modelValue:n(l),"onUpdate:modelValue":a[4]||(a[4]=o=>s(l)?l.value=o:null)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})],64)}}});export{Q as _};
