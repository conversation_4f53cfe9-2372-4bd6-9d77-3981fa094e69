import{H as W,C as X,D as Y,I as Z,J as ee,w as te,F as le,G as ae,K as oe,L as ue,M as ne}from"./element-plus.5bcb7c8a.js";import{_ as se}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as T,_ as ie}from"./index.850efb0d.js";import{_ as re}from"./index.vue_vue_type_script_setup_true_lang.1e869df8.js";import{d as P,a0 as me,s as A,r as $,o as n,c as b,W as e,Q as l,u as a,a8 as U,F as E,a7 as L,P as d,U as r,a as _,R as pe,T as K,V as g,j as de}from"./@vue.a11433a6.js";import{c as ce,a as _e}from"./chat_records.88bb94a9.js";import{u as fe}from"./usePaging.b48cb079.js";import{_ as ye}from"./replyPop.vue_vue_type_script_setup_true_lang.2a8591a1.js";import{_ as be}from"./auditPop.vue_vue_type_script_setup_true_lang.88658f0d.js";import{_ as ge}from"./askPop.vue_vue_type_script_setup_true_lang.60263a60.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.vue_vue_type_style_index_0_lang.612c3870.js";import"./markdown-it.77c882ab.js";import"./uc.micro.5b97656f.js";import"./mdurl.f884a969.js";import"./linkify-it.11d25e6c.js";import"./punycode.d2e2b7b3.js";/* empty css                            */import"./index.324d704f.js";const ve={class:"mb-4"},Ce={class:"flex items-center"},Fe={class:"ml-4"},ke=["onClick"],Ee={class:"flex justify-end mt-4"},he=P({name:"dialogueRecord"}),Et=P({...he,setup(we){const u=me({type:"3",user_info:"",keyword:"",censor_status:"",start_time:"",end_time:"",category_id:""}),h=A(),w=A(),C=$([]),S=$([]),I=m=>{h.value.open(m)},N=m=>{w.value.open(m)},j=m=>{C.value=m},{pager:f,getLists:F,resetPage:k,resetParams:q}=fe({fetchFun:_e,params:u}),z=async()=>{},B=async m=>{await T.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await ce({id:m}),T.msgSuccess("\u64CD\u4F5C\u6210\u529F"),F()};return F(),z(),(m,o)=>{const D=X,c=Y,p=Z,V=ee,G=re,y=te,H=le,x=ae,s=oe,R=ie,v=W,J=ue,M=se,O=ne;return n(),b("div",null,[e(x,{class:"!border-none",shadow:"never"},{default:l(()=>[e(H,{ref:"formRef",class:"mb-[-16px]",model:a(u),inline:!0},{default:l(()=>[e(c,{label:"\u7528\u6237\u4FE1\u606F"},{default:l(()=>[e(D,{class:"w-[280px]",modelValue:a(u).user_info,"onUpdate:modelValue":o[0]||(o[0]=t=>a(u).user_info=t),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237ID/\u7528\u6237\u6635\u79F0",clearable:"",onKeyup:U(a(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(c,{label:"\u5173\u952E\u8BCD"},{default:l(()=>[e(D,{class:"w-[280px]",modelValue:a(u).keyword,"onUpdate:modelValue":o[1]||(o[1]=t=>a(u).keyword=t),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD",clearable:"",onKeyup:U(a(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(c,{label:"\u5BA1\u6838\u72B6\u6001"},{default:l(()=>[e(V,{class:"w-[280px]",modelValue:a(u).censor_status,"onUpdate:modelValue":o[2]||(o[2]=t=>a(u).censor_status=t)},{default:l(()=>[e(p,{label:"\u5168\u90E8",value:""}),e(p,{label:"\u672A\u5BA1\u6838",value:0}),e(p,{label:"\u5408\u89C4",value:1}),e(p,{label:"\u4E0D\u5408\u89C4",value:2}),e(p,{label:"\u7591\u4F3C",value:3}),e(p,{label:"\u5BA1\u6838\u5931\u8D25",value:4})]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"\u89D2\u8272\u6A21\u578B"},{default:l(()=>[e(V,{class:"w-[280px]",modelValue:a(u).category_id,"onUpdate:modelValue":o[3]||(o[3]=t=>a(u).category_id=t)},{default:l(()=>[e(p,{label:"\u5168\u90E8",value:""}),(n(!0),b(E,null,L(a(S),(t,i)=>(n(),d(p,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"\u63D0\u95EE\u65F6\u95F4"},{default:l(()=>[e(G,{startTime:a(u).start_time,"onUpdate:startTime":o[4]||(o[4]=t=>a(u).start_time=t),endTime:a(u).end_time,"onUpdate:endTime":o[5]||(o[5]=t=>a(u).end_time=t)},null,8,["startTime","endTime"])]),_:1}),e(c,null,{default:l(()=>[e(y,{type:"primary",onClick:a(k)},{default:l(()=>[r("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(y,{onClick:a(q)},{default:l(()=>[r("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(x,{class:"!border-none mt-4",shadow:"never"},{default:l(()=>[_("div",ve,[e(y,{type:"default",plain:!0,disabled:!a(C).length,onClick:o[6]||(o[6]=t=>B(a(C).map(i=>i.id)))},{default:l(()=>[r(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])]),pe((n(),d(J,{size:"large",data:a(f).lists,onSelectionChange:j},{default:l(()=>[e(s,{type:"selection",width:"55"}),e(s,{label:"ID",prop:"id","min-width":"80"}),e(s,{label:"\u7528\u6237\u4FE1\u606F","min-width":"180"},{default:l(({row:t})=>[_("div",Ce,[t.avatar?(n(),d(R,{key:0,class:"flex-none",src:t.avatar,width:48,height:48,"preview-src-list":[t.avatar],"preview-teleported":"",fit:"contain"},null,8,["src","preview-src-list"])):K("",!0),_("span",Fe,g(t.nickname),1)])]),_:1}),e(s,{label:"\u63D0\u95EE\u65F6\u95F4",prop:"create_time",sortable:"","min-width":"180","show-tooltip-when-overflow":""}),e(s,{label:"\u7528\u6237\u8F93\u5165",prop:"ask","min-width":"180"},{default:l(({row:t})=>[(n(!0),b(E,null,L(t.files_plugin,(i,Q)=>(n(),b("div",{class:"flex flex-wrap mb-2",key:Q},[i.type=="image"?(n(),d(R,{key:0,class:"flex-none mr-2",src:i.url,width:70,height:"auto","preview-src-list":[i.url],"preview-teleported":"",fit:"contain"},null,8,["src","preview-src-list"])):K("",!0)]))),128)),e(ge,{content:t.ask,title:"\u7528\u6237\u8F93\u5165"},null,8,["content"])]),_:1}),e(s,{label:"\u4F7F\u7528\u89D2\u8272",prop:"other_desc","min-width":"180"}),e(s,{label:"\u5BF9\u8BDD\u6A21\u578B",prop:"model","min-width":"120"}),e(s,{label:"\u5BA1\u6838\u72B6\u6001","min-width":"180"},{default:l(({row:t})=>[_("div",null,[t.censor_status==1?(n(),d(v,{key:0,class:"mr-2",type:"success"},{default:l(()=>[r(g(t.censor_status_desc),1)]),_:2},1024)):t.censor_status>=2?(n(),b(E,{key:1},[e(v,{class:"mr-2 cursor-pointer",type:"danger"},{default:l(()=>[r(g(t.censor_status_desc),1)]),_:2},1024),_("span",{class:"text-error text-sm cursor-pointer",onClick:i=>N(t)}," \u67E5\u770B\u539F\u56E0 ",8,ke)],64)):t.censor_status==0?(n(),d(v,{key:2,class:"mr-2",type:"warning"},{default:l(()=>[r(g(t.censor_status_desc),1)]),_:2},1024)):(n(),d(v,{key:3,class:"mr-2",type:"danger"},{default:l(()=>[r(g(t.censor_status_desc),1)]),_:2},1024))])]),_:1}),e(s,{label:"\u6D88\u8017\u7535\u529B\u503C",prop:"price","min-width":"180"}),e(s,{label:"\u8BF7\u6C42ip",prop:"ip","min-width":"140"}),e(s,{label:"\u64CD\u4F5C","min-width":"180",fixed:"right"},{default:l(({row:t})=>[e(y,{type:"primary",link:"",onClick:i=>I(t.reply)},{default:l(()=>[r(" \u67E5\u770B\u56DE\u590D ")]),_:2},1032,["onClick"]),e(y,{type:"danger",link:"",onClick:i=>B([t.id])},{default:l(()=>[r(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[O,a(f).loading]]),_("div",Ee,[e(M,{modelValue:a(f),"onUpdate:modelValue":o[7]||(o[7]=t=>de(f)?f.value=t:null),onChange:a(F)},null,8,["modelValue","onChange"])])]),_:1}),e(be,{ref_key:"auditRef",ref:w},null,512),e(ye,{ref_key:"popRef",ref:h},null,512)])}}});export{Et as default};
