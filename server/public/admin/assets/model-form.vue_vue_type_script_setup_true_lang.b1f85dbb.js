import{_ as Re}from"./index.88e852a7.js";import{P as Te}from"./index.324d704f.js";import{t as pe,I as ce,J as _e,C as fe,v as Fe,a0 as Oe,Q as je,R as Ue,D as ve,F as Be,_ as Ie,a1 as qe,X as Pe,G as Ne,w as oe,c as ze,a as Me,K as We,L as Ge}from"./element-plus.5bcb7c8a.js";import{h as Le,j as be,k as ae}from"./lodash-es.c9433054.js";import{d as w,b as g,o as F,P as D,u as l,Q as n,c as A,F as O,a7 as q,S as Qe,U as I,V as Z,x as Je,q as Ve,A as ne,h as Ke,a0 as Ze,s as T,r as H,i as Ee,w as He,W as t,a as c,K as Xe,T as se,j as Ye,n as Y,a4 as et}from"./@vue.a11433a6.js";import{l as tt}from"./lodash.9ffd80b1.js";import{f as re,d as ut,b as lt}from"./index.850efb0d.js";import{_ as ot}from"./picker.9a1dad65.js";import{g as at}from"./ai_creation.a70c015c.js";import{S as nt}from"./sortablejs.98edf555.js";import{u as st}from"./useDictOptions.583d6eb9.js";import{g as ie}from"./@vueuse.a2407f20.js";const z=()=>({name:"field",label:"\u5B57\u6BB5\u503C",type:"string",tip:"\u5B57\u6BB5\u503C\u5141\u8BB8\u5305\u542B\u6570\u5B57\u3001\u5B57\u6BCD\u3001\u4E0B\u5212\u7EBF_\u3001\u7F8E\u5143\u7B26\u53F7$\uFF0C\u4F46\u4E0D\u80FD\u4EE5\u6570\u5B57\u5F00\u5934\uFF0C\u4E14\u5728\u540C\u4E00\u4E2A\u8868\u5355\u4E2D\u5FC5\u987B\u552F\u4E00",setter:{name:"String",props:{placeholder:"\u8BF7\u8F93\u5165\u5B57\u6BB5\u503C"}},rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u5B57\u6BB5\u503C"},{pattern:/^[a-zA-Z_$][a-zA-Z0-9_$]*$/,message:"\u5B57\u6BB5\u503C\u683C\u5F0F\u4E0D\u6B63\u786E"}]}),M=()=>({name:"title",label:"\u5B57\u6BB5\u6807\u9898",type:"string",setter:{name:"String",props:{placeholder:"\u8BF7\u8F93\u5165\u5B57\u6BB5\u6807\u9898"}},rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u5B57\u6BB5\u6807\u9898"}]}),ee=()=>({name:"options",label:"\u9009\u9879",type:"array",tip:"\u6BCF\u4E00\u884C\u4E00\u4E2A\u9009\u9879\uFF0C\u6BCF\u884C\u6700\u591A\u4E0D\u8D85\u8FC750\u4E2A\u5B57\uFF0C\u6700\u591A50\u884C",setter:{name:"String",props:{placeholder:`\u793A\u4F8B\uFF1AA
B
C`,type:"textarea",rows:4,onChange(e,i){i.options=Array.from(new Set(i.options)).filter(Boolean)}}},getValue(e){return e.options.join(`
`)},setValue(e,i){e.options=i.split(`
`)},defaultValue:[]}),W=()=>({name:"isRequired",label:"\u662F\u5426\u5FC5\u586B",type:"boolean",setter:{name:"Bool"},defaultValue:!1}),rt={name:"WidgetCheckbox",title:"\u591A\u9009",props:[z(),M(),ee(),{name:"defaultValue",label:"\u9ED8\u8BA4\u503C",type:"array",setter:{name:"Select",props:{"get:options"(e){return e.options},clearable:!0,multiple:!0}}},W()],sort:5},it=Object.freeze(Object.defineProperty({__proto__:null,default:rt},Symbol.toStringTag,{value:"Module"})),dt={name:"WidgetInput",title:"\u5355\u884C\u6587\u672C",props:[z(),M(),{name:"defaultValue",label:"\u9ED8\u8BA4\u503C",type:"string",condition:()=>!1,setter:"String",defaultValue:""},{name:"placeholder",label:"\u793A\u4F8B\u6587\u5B57",type:"string",setter:{name:"String",props:{placeholder:"\u8BF7\u8F93\u5165"}}},{name:"maxlength",label:"\u6700\u5927\u8F93\u5165\u957F\u5EA6",type:"number",setter:{name:"Number",props:{min:0}},defaultValue:200},W()],sort:1},mt=Object.freeze(Object.defineProperty({__proto__:null,default:dt},Symbol.toStringTag,{value:"Module"})),pt={name:"WidgetRadio",title:"\u5355\u9009",props:[z(),M(),ee(),{name:"defaultValue",label:"\u9ED8\u8BA4\u503C",type:"string",setter:{name:"Select",props:{"get:options"(e){return e.options},clearable:!0}}},W()],sort:4},ct=Object.freeze(Object.defineProperty({__proto__:null,default:pt},Symbol.toStringTag,{value:"Module"})),_t={name:"WidgetSelect",title:"\u4E0B\u62C9\u9009\u9879",props:[z(),M(),ee(),{name:"defaultValue",label:"\u9ED8\u8BA4\u503C",type:"string",setter:{name:"Select",props:{"get:options"(e){return e.options},clearable:!0}}},W()],sort:3},ft=Object.freeze(Object.defineProperty({__proto__:null,default:_t},Symbol.toStringTag,{value:"Module"})),Ft={name:"WidgetTextarea",title:"\u591A\u884C\u6587\u672C",props:[z(),M(),{name:"placeholder",label:"\u793A\u4F8B\u6587\u5B57",type:"string",setter:{name:"String",props:{placeholder:"\u8BF7\u8F93\u5165"}}},{name:"rows",label:"\u9ED8\u8BA4\u884C\u6570",type:"number",setter:{name:"Number",props:{min:0}},defaultValue:4},{name:"defaultValue",label:"\u9ED8\u8BA4\u503C",type:"string",condition:()=>!1,setter:"String",defaultValue:""},{name:"maxlength",label:"\u6700\u5927\u8F93\u5165\u957F\u5EA6",type:"number",setter:{name:"Number",props:{min:0}},defaultValue:200},{name:"autosize",label:"\u9AD8\u5EA6\u81EA\u9002\u5E94",type:"boolean",setter:{name:"Bool"},defaultValue:!1},W()],sort:2},vt=Object.freeze(Object.defineProperty({__proto__:null,default:Ft},Symbol.toStringTag,{value:"Module"})),Bt={string:"",boolean:!1,number:0,array:()=>[],object:()=>({})};function bt(e,i={}){var p;if(!Le(e))return e;for(const _ of e)if(_.name){let m=null;_.defaultValue!==void 0?m=_.defaultValue:m=(p=Bt[_.type])!=null?p:null,i[_.name]=be(m)?m():m}return i}const Vt=e=>{if(typeof e!="string")return e;let i;const p=`return function() {
    const self = this
    try {
      return (${e}).apply(self, arguments)
    } catch(e) {
      console.log(e)
    }
  }`;try{i=new Function(p)()}catch(_){console.error(_)}return i},Et=w({__name:"bool",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:i}){const p=e,_=i,m=g({get(){return p.modelValue},set(u){_("update:modelValue",u)}});return(u,o)=>(F(),D(l(pe),{modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=a=>m.value=a)},null,8,["modelValue"]))}}),gt=w({__name:"select",props:{modelValue:{},options:{}},emits:["update:modelValue"],setup(e,{emit:i}){const p=e,_=i,m=g({get(){return p.modelValue},set(o){_("update:modelValue",o)}}),u=g(()=>p.options.map(o=>tt.exports.isObject(o)?o:{label:o,value:o}));return(o,a)=>(F(),D(l(_e),{modelValue:m.value,"onUpdate:modelValue":a[0]||(a[0]=B=>m.value=B),teleported:!0,style:{width:"100%"}},{default:n(()=>[(F(!0),A(O,null,q(u.value,(B,C)=>(F(),D(l(ce),{key:C,label:B.label,value:B.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]))}}),Ct=w({__name:"string",props:{modelValue:{}},emits:["update:modelValue"],setup(e,{emit:i}){const p=e,_=i,m=g({get(){return p.modelValue},set(u){_("update:modelValue",u)}});return(u,o)=>(F(),D(l(fe),{modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=a=>m.value=a)},null,8,["modelValue"]))}}),yt=w({__name:"number",props:{modelValue:{}},emits:["update:modelValue"],setup(e,{emit:i}){const p=e,_=i,m=g({get(){return p.modelValue},set(u){_("update:modelValue",u)}});return(u,o)=>(F(),D(l(Fe),{modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=a=>m.value=a)},null,8,["modelValue"]))}}),xt=w({__name:"radio",props:{modelValue:{type:[String,Number,Boolean]},options:{},type:{}},emits:["update:modelValue"],setup(e,{emit:i}){const p=e,_=i,m=g({get(){return p.modelValue},set(u){_("update:modelValue",u)}});return(u,o)=>(F(),D(l(Ue),{modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=a=>m.value=a)},{default:n(()=>[(F(!0),A(O,null,q(u.options,a=>(F(),D(Qe(p.type=="button"?l(Oe):l(je)),{key:a.value,label:a.value,name:a.label},{default:n(()=>[I(Z(a.label),1)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"]))}}),At=Object.freeze(Object.defineProperty({__proto__:null,Bool:Et,Select:gt,String:Ct,Number:yt,Radio:xt},Symbol.toStringTag,{value:"Module"})),Dt=new Map(Object.entries(At));function ht(e){return Dt.get(e)||null}function wt(e){const i=ge(e),p=ht(i);return p||null}function de(e,i){return Je(e)?null:Ve(e)?i?e.name===i?e.props:null:e.props:null}function ge(e){return Ve(e)?e.name:e}const St="on",me="get:",kt=w({props:{modelValue:{type:Object,required:!0},setterName:{type:String},propsItem:{type:Object,required:!0}},emits:["update:modelValue"],setup(e,{emit:i}){const p=g(()=>{var a;const o=e.modelValue[e.propsItem.name];return e.propsItem.getValue&&(a=e.propsItem.getValue(e.modelValue))!=null?a:o}),_=o=>{const a=e.modelValue;a[e.propsItem.name]=o,e.propsItem.setValue&&e.propsItem.setValue(e.modelValue,o),i("update:modelValue",e.modelValue)},m=g(()=>{const o=de(e.propsItem.setter,e.setterName);for(const a in o)ne(o[a])&&a.startsWith(me)&&(o[a.replace(me,"")]=o[a](e.modelValue));return o}),u=g(()=>{const o={},a=de(e.propsItem.setter,e.setterName);for(const B in a){const C=a[B];B.startsWith(St)&&ne(C)&&(o[B]=function(k){C(k,e.modelValue)})}return o});return()=>{if(!e.setterName)return null;const o=wt(e.setterName);return o?Ke(o,{modelValue:p.value,"onUpdate:modelValue":_,...m.value,...u.value}):null}}});let $t=Date.now();function Rt(e=""){return e=e?`${e}_`:"",`${e}${($t++).toString(36).toLowerCase()}`}const Tt={class:"flex flex-wrap mx-[-5px]"},Ot=["onClick"],jt={class:"flex-1"},Ut={class:"form-tips"},It=w({__name:"container",props:{modelValue:{}},emits:["update:modelValue"],setup(e,{expose:i,emit:p}){const _=Object.values(Object.assign({"./material/checkbox.ts":it,"./material/input.ts":mt,"./material/radio.ts":ct,"./material/select.ts":ft,"./material/textarea.ts":vt})).map(s=>(s==null?void 0:s.default)||(s==null?void 0:s.meta)).sort((s,E)=>s.sort-E.sort),m=e,u=p,o=Ze({}),a=g({get(){return m.modelValue},set(s){u("update:modelValue",s)}}),B=g(()=>!!Object.keys(a.value.props).length),C=T(),k=g({get(){const s=_.findIndex(E=>E.name===a.value.name);return b.value=_[s],s!==-1?s:0},set(s){var $,j,R,v;b.value=_[s];const E=Rt();a.value=o[($=b.value)==null?void 0:$.name]||{name:(j=b.value)==null?void 0:j.name,title:(R=b.value)==null?void 0:R.title,id:E,props:bt(((v=b.value)==null?void 0:v.props)||{})}}}),b=H(),P=s=>{o[m.modelValue.name]=m.modelValue,k.value=s},V=s=>s?be(s)?s(a):s.type=="JSFunction"?Vt(s.value)(a):!0:!0;return Ee(()=>{a.value.name||P(0)}),He(a,s=>{console.log(s)}),i({async validate(){var E;return await((E=C.value)==null?void 0:E.validate())}}),(s,E)=>{const $=ve,j=Be;return l(B)?(F(),D(j,{key:0,ref_key:"formRef",ref:C,"label-width":"105px",model:l(a).props},{default:n(()=>{var R;return[t($,{label:"\u7C7B\u578B",prop:"name"},{default:n(()=>[c("div",Tt,[(F(!0),A(O,null,q(l(_),(v,f)=>(F(),A("div",{class:Xe(["cursor-pointer px-4 border border-br border-solid rounded m-[5px]",{"border-primary text-primary":l(k)===f}]),key:f,onClick:r=>P(f)},Z(v.title),11,Ot))),128))])]),_:1}),(F(!0),A(O,null,q((R=l(b))==null?void 0:R.props,v=>(F(),A(O,{key:v.name+l(a).id},[V(v.condition)?(F(),D($,{key:0,label:v.label,prop:v.name,rules:v.rules},{default:n(()=>[c("div",jt,[(F(),D(l(kt),{key:v.name+l(a).id,modelValue:l(a).props,"onUpdate:modelValue":E[0]||(E[0]=f=>l(a).props=f),"setter-name":l(ge)(v.setter),"props-item":v},null,8,["modelValue","setter-name","props-item"])),c("div",Ut,Z(v.tip),1)])]),_:2},1032,["label","prop","rules"])):se("",!0)],64))),128))]}),_:1},8,["model"])):se("",!0)}}}),qt={class:"edit-popup"},Pt=w({__name:"popup",emits:["add","edit"],setup(e,{expose:i,emit:p}){const _=p,m=H({name:"",id:"",title:"",props:{}}),u=T(),o=T(),a=H("add"),B=g(()=>a.value=="edit"?"\u7F16\u8F91\u8868\u5355\u9879":"\u6DFB\u52A0\u8868\u5355\u9879"),C=async()=>{var s;await((s=u.value)==null?void 0:s.validate());const V=ae(m.value);a.value=="add"?_("add",V):_("edit",V)};return i({open:(V="add")=>{var s;a.value=V,V==="add"&&(m.value={name:"",id:"",title:"",props:{}}),(s=o.value)==null||s.open()},close:()=>{var V;(V=o.value)==null||V.close()},setFormData:V=>{m.value=ae(V)}}),(V,s)=>(F(),A("div",qt,[t(Te,{ref_key:"popupRef",ref:o,title:l(B),async:!0,width:"550px","destroy-on-close":"",onConfirm:C},{default:n(()=>[t(It,{ref_key:"formRef",ref:u,modelValue:l(m),"onUpdate:modelValue":s[0]||(s[0]=E=>Ye(m)?m.value=E:null)},null,8,["modelValue"])]),_:1},8,["title"])]))}}),Nt="/admin/assets/example.5f35833a.png";function zt(e,i){if(e.focus(),e.selectionStart!==void 0){const p=e.selectionStart,_=e.selectionEnd;typeof e.setRangeText!==void 0?e.setRangeText(i):e.value=e.value.substring(0,p)+i+e.value.substring(_,e.value.length),e.selectionStart=p+i.length,e.selectionEnd=p+i.length}else e.value+=i;return e.value}const Mt={class:"w-[380px]"},Wt={class:"w-[380px]"},Gt={class:"w-[380px]"},Lt={class:"w-[380px]"},Qt={class:"w-[380px]"},Jt={class:"flex flex-wrap mx-[-5px]"},Kt={class:"max-w-[100px]"},Zt={class:"flex flex-wrap max-w-[700px]"},Ht={class:"w-[200px] mr-[20px] mb-[20px]"},Xt={class:"flex items-center text-tx-regular text-xs"},Yt=c("span",{class:"mr-[4px] mt-[2px]"},"\u56DE\u590D\u6761\u6570",-1),eu={class:"w-[200px] mr-[20px] mb-[20px]"},tu={class:"flex items-center text-tx-regular text-xs"},uu=c("span",{class:"mr-[4px] mt-[2px]"},"\u8BCD\u6C47\u5C5E\u6027",-1),lu={class:"w-[200px] mr-[20px] mb-[20px]"},ou={class:"flex items-center text-tx-regular text-xs"},au=c("span",{class:"mr-[4px] mt-[2px]"},"\u968F\u673A\u5C5E\u6027",-1),nu={class:"w-[200px] mr-[20px] mb-[20px]"},su={class:"flex items-center text-tx-regular text-xs"},ru=c("span",{class:"mr-[4px] mt-[2px]"},"\u8BDD\u9898\u5C5E\u6027",-1),iu={class:"flex-1 min-w-0 max-w-[1000px]"},du={class:"flex"},mu=c("img",{src:Nt},null,-1),pu={class:"mt-4"},cu={class:"move-icon cursor-move"},_u=c("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u503C\u8D8A\u5927\u8D8A\u6392\u524D\u9762",-1),Du=w({__name:"model-form",props:{headerTitle:{},modelValue:{}},emits:["update:modelValue","submit"],setup(e,{emit:i}){const p=T(),_=e,m=i,u=g({get(){return _.modelValue},set(f){m("update:modelValue",f)}}),o=T(),a=()=>{const f=o.value.$el.querySelector(".el-table__body tbody");nt.create(f,{animation:150,handle:".move-icon",onEnd:({newIndex:r,oldIndex:h})=>{const y=u.value.form,X=y.splice(h,1)[0];y.splice(r,0,X),u.value.form=[],Y(()=>{u.value.form=y})}})},B=T(),C=f=>{var r;u.value.content=zt((r=B.value)==null?void 0:r.textarea,`\${${f}}`)},k={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0"}],tips:[{required:!0,message:"\u8BF7\u8F93\u5165\u6A21\u578B\u63CF\u8FF0"}],content:[{required:!0,message:"\u8BF7\u8F93\u5165\u8C03\u6559\u6587\u6848"}],category_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7C7B\u76EE"}],image:[{required:!0,message:"\u8BF7\u9009\u62E9\u6A21\u578B\u56FE\u6807"}],form:[{type:"array",required:!0,message:"\u8BF7\u6DFB\u52A0\u8868\u5355\u5185\u5BB9"}]},b=T(),P=async()=>{var f;await Y(),(f=b.value)==null||f.open("add")},V=ie(f=>{var h;if(!!u.value.form.find(y=>y.props.field===f.props.field))return re.msgError("\u5B57\u6BB5\u503C\u91CD\u590D\uFF0C\u8BF7\u4FEE\u6539\u5B57\u6BB5\u503C");u.value.form.push(f),(h=b.value)==null||h.close()}),s=H(0),E=(f,r)=>{var h,y;s.value=r,(h=b.value)==null||h.open("edit"),(y=b.value)==null||y.setFormData(f)},$=async f=>{await re.confirm("\u786E\u5B9A\u5220\u9664\u5F53\u524D\u9879\uFF1F"),u.value.form.splice(f,1)},j=ie(f=>{var r;u.value.form[s.value]=f,(r=b.value)==null||r.close()}),{optionsData:R}=st({categoryList:{api:at,params:{page_type:0},transformData(f){return f.lists}}}),v=async()=>{var f;await((f=p.value)==null?void 0:f.validate()),m("submit")};return Ee(()=>{Y(()=>{a()})}),(f,r)=>{const h=Ie,y=Ne,X=ot,x=ve,G=fe,Ce=ce,ye=_e,xe=ut,Ae=oe,L=et("QuestionFilled"),Q=ze,J=Me,K=qe,N=oe,De=Pe,he=lt,U=We,te=pe,we=Ge,ue=Fe,Se=Be,ke=Pt,$e=Re;return F(),A("div",null,[t(y,{class:"!border-none",shadow:"never"},{default:n(()=>[t(h,{content:f.headerTitle,onBack:r[0]||(r[0]=d=>f.$router.back())},null,8,["content"])]),_:1}),t(y,{class:"!border-none mt-4",shadow:"never"},{default:n(()=>[t(Se,{class:"ls-form",ref_key:"formRef",ref:p,rules:k,model:l(u),"label-width":"120px"},{default:n(()=>[t(x,{label:"\u56FE\u6807",prop:"image"},{default:n(()=>[t(X,{modelValue:l(u).image,"onUpdate:modelValue":r[1]||(r[1]=d=>l(u).image=d),limit:1},null,8,["modelValue"])]),_:1}),t(x,{label:"\u6A21\u578B\u540D\u79F0",prop:"name"},{default:n(()=>[c("div",Mt,[t(G,{placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u540D\u79F0",modelValue:l(u).name,"onUpdate:modelValue":r[2]||(r[2]=d=>l(u).name=d)},null,8,["modelValue"])])]),_:1}),t(x,{label:"\u6A21\u578B\u63CF\u8FF0",prop:"tips"},{default:n(()=>[c("div",Wt,[t(G,{placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u63CF\u8FF0",type:"textarea",rows:2,modelValue:l(u).tips,"onUpdate:modelValue":r[3]||(r[3]=d=>l(u).tips=d)},null,8,["modelValue"])])]),_:1}),t(x,{label:"\u6240\u5C5E\u7C7B\u76EE",prop:"category_id"},{default:n(()=>[c("div",Gt,[t(ye,{modelValue:l(u).category_id,"onUpdate:modelValue":r[4]||(r[4]=d=>l(u).category_id=d),placeholder:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7C7B\u76EE",class:"w-full"},{default:n(()=>[(F(!0),A(O,null,q(l(R).categoryList,(d,S)=>(F(),D(Ce,{key:S,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(x,{label:"\u5168\u5C40\u6307\u4EE4",prop:"system"},{default:n(()=>[c("div",Lt,[t(G,{placeholder:"\u5982\u679C\u586B\u5199\u4E86\u8BE5\u5168\u5C40\u6307\u4EE4\u5219\u4F1A\u66FF\u6362\u5BF9\u8BDD\u8BBE\u7F6E\u7684\u5168\u5C40\u6307\u4EE4",rows:3,type:"textarea",modelValue:l(u).system,"onUpdate:modelValue":r[5]||(r[5]=d=>l(u).system=d)},null,8,["modelValue"])])]),_:1}),t(x,{label:"\u8C03\u6559\u6587\u6848",prop:"content"},{default:n(()=>[c("div",null,[c("div",Qt,[t(G,{ref_key:"elInputRef",ref:B,placeholder:"\u8BF7\u8F93\u5165\u8C03\u6559\u6587\u6848",type:"textarea",rows:4,modelValue:l(u).content,"onUpdate:modelValue":r[6]||(r[6]=d=>l(u).content=d)},null,8,["modelValue"])]),c("div",Jt,[(F(!0),A(O,null,q(l(u).form,(d,S)=>(F(),A("div",{class:"mx-[5px] mt-[10px]",key:S},[t(Ae,{onClick:le=>C(d.props.field)},{default:n(()=>[c("span",Kt,[t(xe,{content:d.props.title},null,8,["content"])])]),_:2},1032,["onClick"])]))),128))])])]),_:1}),t(x,{label:"\u6A21\u578B\u53C2\u6570"},{default:n(()=>[c("div",Zt,[c("div",Ht,[c("div",Xt,[Yt,t(J,{class:"box-item",effect:"dark",content:"\u4E3A\u6BCF\u4E2A\u8F93\u5165\u6D88\u606F\u751F\u6210\u591A\u4E2A\u56DE\u590D\uFF0C\u53D6\u503C\u8303\u56F4\u4E3A1~5\u4E4B\u95F4\u7684\u6574\u6570\u3002",placement:"top"},{default:n(()=>[t(Q,{size:"16px"},{default:n(()=>[t(L)]),_:1})]),_:1})]),t(K,{modelValue:l(u).n,"onUpdate:modelValue":r[7]||(r[7]=d=>l(u).n=d),min:1,max:5},null,8,["modelValue"])]),c("div",eu,[c("div",tu,[uu,t(J,{class:"box-item",effect:"dark",content:"\u7528\u4E8E\u63A7\u5236\u751F\u6210\u6587\u672C\u7684\u968F\u673A\u6027\uFF0C\u53D6\u503C\u8303\u56F4\u4E3A0~1\u4E4B\u95F4\u7684\u6D6E\u70B9\u6570\uFF0C\u5EFA\u8BAE\u53D6\u503C0.7\u5DE6\u53F3\u3002",placement:"top"},{default:n(()=>[t(Q,{size:"16px"},{default:n(()=>[t(L)]),_:1})]),_:1})]),t(K,{modelValue:l(u).temperature,"onUpdate:modelValue":r[8]||(r[8]=d=>l(u).temperature=d),min:0,max:1,step:.1},null,8,["modelValue"])]),c("div",lu,[c("div",ou,[au,t(J,{class:"box-item",effect:"dark",content:"\u7528\u4E8E\u63A7\u5236\u751F\u6210\u6587\u672C\u7684\u591A\u6837\u6027\uFF0C\u53D6\u503C\u8303\u56F4\u4E3A0~1\u4E4B\u95F4\u7684\u6D6E\u70B9\u6570\uFF0C\u5EFA\u8BAE\u53D6\u503C0.9\u5DE6\u53F3\u3002",placement:"top"},{default:n(()=>[t(Q,{size:"16px"},{default:n(()=>[t(L)]),_:1})]),_:1})]),t(K,{modelValue:l(u).top_p,"onUpdate:modelValue":r[9]||(r[9]=d=>l(u).top_p=d),min:0,max:1,step:.1},null,8,["modelValue"])]),c("div",nu,[c("div",su,[ru,t(J,{class:"box-item",effect:"dark",content:"\u7528\u4E8E\u63A7\u5236\u751F\u6210\u6587\u672C\u4E2D\u662F\u5426\u51FA\u73B0\u7ED9\u5B9A\u7684\u5173\u952E\u8BCD\uFF0C\u53D6\u503C\u8303\u56F4\u4E3A0~1\u4E4B\u95F4\u7684\u6D6E\u70B9\u6570\uFF0C\u5EFA\u8BAE\u53D6\u503C0.5\u5DE6\u53F3\u3002",placement:"top"},{default:n(()=>[t(Q,{size:"16px"},{default:n(()=>[t(L)]),_:1})]),_:1})]),t(K,{modelValue:l(u).presence_penalty,"onUpdate:modelValue":r[10]||(r[10]=d=>l(u).presence_penalty=d),step:.1,min:0,max:1},null,8,["modelValue"])])])]),_:1}),t(x,{label:"\u8868\u5355\u5185\u5BB9",prop:"form"},{default:n(()=>[c("div",iu,[c("div",du,[t(N,{type:"primary",onClick:P},{default:n(()=>[I("\u6DFB\u52A0")]),_:1}),t(De,{placement:"right",width:300,trigger:"hover"},{reference:n(()=>[t(N,{type:"primary",link:""},{default:n(()=>[I("\u67E5\u770B\u793A\u4F8B")]),_:1})]),default:n(()=>[mu]),_:1})]),c("div",pu,[t(we,{ref_key:"tableRef",ref:o,size:"large",class:"mt-4","row-key":"id",data:l(u).form},{default:n(()=>[t(U,{width:"50"},{default:n(()=>[c("div",cu,[t(he,{name:"el-icon-Rank"})])]),_:1}),t(U,{label:"\u5B57\u6BB5\u503C","min-width":"120"},{default:n(({row:d})=>[c("span",null,Z(d.props.field),1)]),_:1}),t(U,{label:"\u5B57\u6BB5\u6807\u9898",prop:"props[title]","min-width":"200"}),t(U,{label:"\u7C7B\u578B",prop:"title","min-width":"100"}),t(U,{label:"\u662F\u5426\u5FC5\u586B",prop:"props[isRequired]","min-width":"100"},{default:n(({row:d})=>[t(te,{modelValue:d.props.isRequired,"onUpdate:modelValue":S=>d.props.isRequired=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(U,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:n(({row:d,$index:S})=>[t(N,{type:"primary",link:"",onClick:le=>E(d,S)},{default:n(()=>[I(" \u7F16\u8F91 ")]),_:2},1032,["onClick"]),t(N,{type:"primary",link:"",onClick:le=>$(S)},{default:n(()=>[I(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])])]),_:1}),t(x,{label:"\u865A\u62DF\u4F7F\u7528\u4EBA\u6570",prop:"virtual_use_num"},{default:n(()=>[t(ue,{modelValue:l(u).virtual_use_num,"onUpdate:modelValue":r[11]||(r[11]=d=>l(u).virtual_use_num=d)},null,8,["modelValue"])]),_:1}),t(x,{label:"\u6392\u5E8F",prop:"sort"},{default:n(()=>[c("div",null,[t(ue,{modelValue:l(u).sort,"onUpdate:modelValue":r[12]||(r[12]=d=>l(u).sort=d)},null,8,["modelValue"]),_u])]),_:1}),t(x,{label:"\u72B6\u6001",prop:"sort"},{default:n(()=>[t(te,{modelValue:l(u).status,"onUpdate:modelValue":r[13]||(r[13]=d=>l(u).status=d),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),t(ke,{ref_key:"formDesignerRef",ref:b,onAdd:l(V),onEdit:l(j)},null,8,["onAdd","onEdit"]),t($e,null,{default:n(()=>[t(N,{type:"primary",onClick:v},{default:n(()=>[I("\u4FDD\u5B58")]),_:1})]),_:1})])}}});export{Du as _};
