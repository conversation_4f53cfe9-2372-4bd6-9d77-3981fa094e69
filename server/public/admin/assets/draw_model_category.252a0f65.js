import{r}from"./index.850efb0d.js";function o(e){return r.get({url:"/draw.draw_model_category/lists",params:e},{ignoreCancelToken:!0})}function a(e){return r.post({url:"/draw.draw_model_category/add",params:e})}function d(e){return r.post({url:"/draw.draw_model_category/edit",params:e})}function l(e){return r.post({url:"/draw.draw_model_category/delete",params:e})}function u(e){return r.post({url:"/draw.draw_model_category/status",params:e})}export{a,u as b,l as d,d as e,o as g};
