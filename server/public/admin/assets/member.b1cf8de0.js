import{r}from"./index.850efb0d.js";function t(){return r.get({url:"/member.MemberPackage/getConfig"})}function a(e){return r.post({url:"/member.MemberPackage/setConfig",params:e})}function u(e){return r.get({url:"/member.memberPackage/lists",params:e})}function n(e){return r.post({url:"/member.memberPackage/add",params:e})}function s(e){return r.post({url:"/member.memberPackage/del",params:e})}function b(e){return r.get({url:"/member.memberPackage/detail",params:e})}function o(e){return r.post({url:"/member.memberPackage/edit",params:e})}function c(e){return r.post({url:"/member.memberPackage/status",params:e})}function g(e){return r.post({url:"/member.memberPackage/sort",params:e})}function i(e){return r.post({url:"/member.memberPackage/recommend",params:e})}function l(){return r.get({url:"/member.memberPackage/getModels"})}export{l as a,b,o as c,n as d,s as e,c as f,t as g,i as h,u as m,a as s,g as u};
