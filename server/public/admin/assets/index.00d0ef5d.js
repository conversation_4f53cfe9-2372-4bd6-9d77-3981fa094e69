import{C as G,D as J,I as O,J as Q,w as W,F as H,G as X,K as Y,t as Z,L as ee,M as te}from"./element-plus.5bcb7c8a.js";import{_ as ae}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as x,b as le}from"./index.850efb0d.js";import{d as U,s as oe,r as h,a0 as ue,i as ne,aj as se,o as s,c as A,W as e,Q as a,a8 as ie,u as i,F as re,a7 as me,P as r,U as p,a as C,R as c,V as D,j as de,T as pe,n as S}from"./@vue.a11433a6.js";import{u as ce}from"./usePaging.b48cb079.js";import{_ as _e,d as fe,u as ve,g as Be}from"./edit.vue_vue_type_script_setup_true_lang.5689733b.js";import{_ as be}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const Ce={class:"line-clamp-2"},Ee={class:"line-clamp-2"},ge={class:"flex justify-end mt-4"},Fe=U({name:"ExampleContent"}),ye=U({...Fe,setup(he){const E=oe(),_=h(!1),g=h(""),F=h([]),u=ue({title:"",category_id:"",status:""}),{pager:m,getLists:f,resetPage:k,resetParams:$}=ce({fetchFun:Be,params:u});ne(()=>{f().then(l=>{l&&l.category_lists&&(F.value=l.category_lists)})});const L=async()=>{var l;_.value=!0,g.value="\u65B0\u589E\u793A\u4F8B\u5E93\u5185\u5BB9",await S(),(l=E.value)==null||l.open()},P=async l=>{var o;_.value=!0,g.value="\u7F16\u8F91\u793A\u4F8B\u5E93\u5185\u5BB9",await S(),(o=E.value)==null||o.open(l)},R=async l=>{await x.confirm("\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u793A\u4F8B\u5E93\u5185\u5BB9\u5417\uFF1F"),await fe({id:l.id}),x.msgSuccess("\u5220\u9664\u6210\u529F"),f()},T=async l=>{await ve({id:l.id,status:l.status})};return(l,o)=>{const I=G,v=J,y=O,V=Q,d=W,K=H,w=X,N=le,n=Y,j=Z,q=ee,M=ae,B=se("perms"),z=te;return s(),A("div",null,[e(w,{class:"!border-none",shadow:"never"},{default:a(()=>[e(K,{ref:"formRef",class:"mb-[-16px]",model:u,inline:!0},{default:a(()=>[e(v,{label:"\u793A\u4F8B\u6807\u9898"},{default:a(()=>[e(I,{modelValue:u.title,"onUpdate:modelValue":o[0]||(o[0]=t=>u.title=t),placeholder:"\u8BF7\u8F93\u5165\u793A\u4F8B\u6807\u9898",clearable:"",onKeyup:ie(i(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(v,{label:"\u6240\u5C5E\u7C7B\u522B"},{default:a(()=>[e(V,{modelValue:u.category_id,"onUpdate:modelValue":o[1]||(o[1]=t=>u.category_id=t),placeholder:"\u8BF7\u9009\u62E9\u7C7B\u522B",clearable:""},{default:a(()=>[(s(!0),A(re,null,me(F.value,t=>(s(),r(y,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(v,{label:"\u72B6\u6001"},{default:a(()=>[e(V,{modelValue:u.status,"onUpdate:modelValue":o[2]||(o[2]=t=>u.status=t),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:""},{default:a(()=>[e(y,{label:"\u5F00\u542F",value:"1"}),e(y,{label:"\u5173\u95ED",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:a(()=>[e(d,{type:"primary",onClick:i(k)},{default:a(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(d,{onClick:i($)},{default:a(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(w,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[C("div",null,[c((s(),r(d,{type:"primary",onClick:L},{icon:a(()=>[e(N,{name:"el-icon-Plus"})]),default:a(()=>[p(" \u65B0\u589E ")]),_:1})),[[B,["kb.example/add"]]])]),c((s(),r(q,{class:"mt-4",data:i(m).lists,size:"large"},{default:a(()=>[e(n,{label:"\u793A\u4F8B\u6807\u9898",prop:"title","min-width":"150"}),e(n,{label:"\u6240\u5C5E\u7C7B\u522B",prop:"category_name","min-width":"120"}),e(n,{label:"\u95EE\u9898\u5185\u5BB9","min-width":"200"},{default:a(({row:t})=>[C("div",Ce,D(t.question_brief),1)]),_:1}),e(n,{label:"\u7B54\u6848\u5185\u5BB9","min-width":"200"},{default:a(({row:t})=>[C("div",Ee,D(t.answer_brief),1)]),_:1}),e(n,{label:"\u72B6\u6001","min-width":"100"},{default:a(({row:t})=>[c(e(j,{modelValue:t.status,"onUpdate:modelValue":b=>t.status=b,"active-value":1,"inactive-value":0,onChange:b=>T(t)},null,8,["modelValue","onUpdate:modelValue","onChange"]),[[B,["kb.example/status"]]])]),_:1}),e(n,{label:"\u6392\u5E8F",prop:"sort","min-width":"80"}),e(n,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"180"}),e(n,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:a(({row:t})=>[c((s(),r(d,{type:"primary",link:"",onClick:b=>P(t)},{default:a(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[B,["kb.example/edit"]]]),c((s(),r(d,{type:"danger",link:"",onClick:b=>R(t)},{default:a(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[B,["kb.example/del"]]])]),_:1})]),_:1},8,["data"])),[[z,i(m).loading]]),C("div",ge,[e(M,{modelValue:i(m),"onUpdate:modelValue":o[3]||(o[3]=t=>de(m)?m.value=t:null),onChange:i(f)},null,8,["modelValue","onChange"])])]),_:1}),_.value?(s(),r(_e,{key:0,ref_key:"editRef",ref:E,title:g.value,"category-list":F.value,onSuccess:i(f),onClose:o[4]||(o[4]=t=>_.value=!1)},null,8,["title","category-list","onSuccess"])):pe("",!0)])}}});const dt=be(ye,[["__scopeId","data-v-232f3723"]]);export{dt as default};
