import{K as U,I as R,J as M,C as T,t as I,w as N,L as $,D as L,F as O}from"./element-plus.5bcb7c8a.js";import{f as j,b as q}from"./index.850efb0d.js";import{f as z}from"./model.63e41af8.js";import{v as J}from"./@vueuse.a2407f20.js";import{S as K}from"./sortablejs.98edf555.js";import{k as P}from"./lodash-es.c9433054.js";import{d as Q,s as W,b as G,i as H,n as X,o as r,c as E,W as e,Q as t,u as m,a as B,U as _,V as Y,F as Z,a7 as ee,P as le}from"./@vue.a11433a6.js";const ae={class:"move-icon cursor-move"},oe=B("div",{class:"flex items-center"},"1000\u5B57\u7B26\u6D88\u8017\u7535\u529B\u503C",-1),re=Q({__name:"model-lists",props:{data:{},models:{}},emits:["update:data"],setup(F,{emit:C}){const p=F,V=C,{data:d}=J(p,V),f=W(),A=()=>{const n=f.value.$el.querySelector(".el-table__body tbody");K.create(n,{animation:150,handle:".move-icon",onEnd:({newIndex:u,oldIndex:s})=>{console.log(u,s);const a=d.value,i=a.splice(s,1)[0];a.splice(u,0,i),d.value=a}})},c=G(()=>{const n=Object.values(P(p.models))||[];return n.forEach(u=>{d.value.findIndex(a=>a.name===u.model)!==-1&&(u.disabled=!0)}),n}),D=(n,u)=>{const s=c.value.find(a=>a.model===n);u.channel=s.channel,u.name=s.model,u.alias=s.name},k=()=>{const n=c.value.find(u=>!u.disabled);console.log(c),n&&d.value.push({channel:n.channel,name:n.model,alias:n.name,price:"",status:0})},g=async(n,u)=>{if((await z({model:n.name})).use_count>0)return j.msgError("\u8BE5\u6A21\u578B\u5DF2\u88AB\u524D\u53F0\u7528\u6237\u4F7F\u7528\uFF0C\u65E0\u6CD5\u5220\u9664");d.value.splice(u,1)};return H(async()=>{X(()=>{A()})}),(n,u)=>{const s=q,a=U,i=R,y=M,b=T,w=I,h=N,x=$,v=L,S=O;return r(),E("div",null,[e(S,{"label-width":"120px",ref:"formRef"},{default:t(()=>[e(v,{label:"\u6A21\u578B\u8BBE\u7F6E"},{default:t(()=>[e(x,{ref_key:"tableRef",ref:f,size:"large","row-key":"name",data:m(d)},{default:t(()=>[e(a,{width:"50"},{default:t(()=>[B("div",ae,[e(s,{name:"el-icon-Rank"})])]),_:1}),e(a,{label:"\u5E8F\u53F7",width:"60"},{default:t(({$index:l})=>[_(Y(l+1),1)]),_:1}),e(a,{label:"\u6A21\u578B\u540D\u79F0",prop:"name","min-width":"200"},{default:t(({row:l})=>[e(y,{class:"w-full","model-value":l.name,filterable:"",onChange:o=>D(o,l)},{default:t(()=>[(r(!0),E(Z,null,ee(m(c),o=>(r(),le(i,{value:o.model,label:o.name,key:o.model,disabled:o.disabled&&o.model!==l.name},null,8,["value","label","disabled"]))),128))]),_:2},1032,["model-value","onChange"])]),_:1}),e(a,{label:"\u522B\u540D",prop:"alias","min-width":"180"},{default:t(({row:l})=>[e(b,{modelValue:l.alias,"onUpdate:modelValue":o=>l.alias=o,placeholder:"\u4E3A\u7A7A\u65F6\u663E\u793A\u9ED8\u8BA4\u540D\u5B57"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(a,{prop:"price","min-width":"160"},{header:t(()=>[oe]),default:t(({row:l})=>[e(b,{modelValue:l.price,"onUpdate:modelValue":o=>l.price=o,placeholder:"\u4E3A\u7A7A\u9ED8\u8BA4\u4E3A0\uFF0C\u652F\u63013\u4F4D\u5C0F\u6570\u70B9"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(a,{label:"\u72B6\u6001",width:"100"},{default:t(({row:l})=>[e(w,{modelValue:l.status,"onUpdate:modelValue":o=>l.status=o,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(a,{label:"\u64CD\u4F5C",width:"100",fixed:"right"},{default:t(({row:l,$index:o})=>[e(h,{type:"danger",link:"",onClick:te=>g(l,o)},{default:t(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e(v,null,{default:t(()=>[e(h,{type:"primary",link:"",onClick:k,disabled:m(d).length===m(c).length},{default:t(()=>[_(" +\u6DFB\u52A0\u6A21\u578B ")]),_:1},8,["disabled"])]),_:1})]),_:1},512)])}}});export{re as _};
