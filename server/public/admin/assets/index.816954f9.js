import{C as j,D as q,I as z,J as G,w as J,F as M,G as O,b as Q,K as W,t as H,L as X,M as Y}from"./element-plus.5bcb7c8a.js";import{_ as Z}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as ee,b as te}from"./index.850efb0d.js";import{d as V,s as oe,a0 as ae,r as le,aj as ne,o as i,c as ie,W as e,Q as o,u as a,a8 as re,U as c,a as k,R as b,P as u,T as w,j as se,n as B}from"./@vue.a11433a6.js";import{u as ue}from"./usePaging.b48cb079.js";import{_ as me}from"./edit.vue_vue_type_script_setup_true_lang.f4e2f5a9.js";import{d as pe,b as ce,c as de}from"./draw_lora.39493e7e.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./picker.9a1dad65.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./useDictOptions.583d6eb9.js";const _e={class:"flex justify-end mt-4"},fe=V({name:"problemExample"}),vt=V({...fe,setup(ve){const d=oe(),r=ae({title:"",status:""}),_=le(!1),x=async()=>{var n;_.value=!0,await B(),(n=d.value)==null||n.open("add")},D=async n=>{var l,v;_.value=!0,await B(),(l=d.value)==null||l.open("edit"),(v=d.value)==null||v.setFormData(n)},A=async(n,l)=>{await ee.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await pe({id:n}),f()},L=n=>{ce({id:n})},{pager:m,getLists:f,resetPage:h,resetParams:$}=ue({fetchFun:de,params:r});return f(),(n,l)=>{const v=j,E=q,g=z,P=G,p=J,R=M,y=O,S=te,U=Q,s=W,T=H,I=X,K=Z,F=ne("perms"),N=Y;return i(),ie("div",null,[e(y,{class:"!border-none",shadow:"never"},{default:o(()=>[e(R,{ref:"formRef",class:"mb-[-16px]",model:a(r),inline:!0},{default:o(()=>[e(E,{label:"\u6A21\u578B\u540D\u79F0"},{default:o(()=>[e(v,{class:"w-[280px]",modelValue:a(r).title,"onUpdate:modelValue":l[0]||(l[0]=t=>a(r).title=t),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u540D\u79F0",clearable:"",onKeyup:re(a(h),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(E,{class:"w-[280px]",label:"\u6A21\u578B\u72B6\u6001"},{default:o(()=>[e(P,{modelValue:a(r).status,"onUpdate:modelValue":l[1]||(l[1]=t=>a(r).status=t),clearable:""},{default:o(()=>[e(g,{label:"\u5168\u90E8",value:""}),e(g,{label:"\u5F00\u542F",value:1}),e(g,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(E,null,{default:o(()=>[e(p,{type:"primary",onClick:a(h)},{default:o(()=>[c("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(p,{onClick:a($)},{default:o(()=>[c("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(y,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[k("div",null,[b((i(),u(p,{type:"primary",onClick:x},{icon:o(()=>[e(S,{name:"el-icon-Plus"})]),default:o(()=>[c(" \u65B0\u589E ")]),_:1})),[[F,["creation.creationCategory/add"]]])]),b((i(),u(I,{size:"large",class:"mt-4",data:a(m).lists},{default:o(()=>[e(s,{label:"\u6A21\u578B\u5C01\u9762","min-width":"100"},{default:o(({row:t})=>[t.cover?(i(),u(U,{key:0,src:t.cover,class:"w-[44px] h-[44px]"},null,8,["src"])):w("",!0)]),_:1}),e(s,{label:"\u6A21\u578B\u540D\u79F0",prop:"title","min-width":"120"}),e(s,{label:"\u72B6\u6001","min-width":"100"},{default:o(({row:t})=>[e(T,{onChange:C=>L(t.id),modelValue:t.status,"onUpdate:modelValue":C=>t.status=C,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),e(s,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(s,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:o(({row:t})=>[b((i(),u(p,{type:"primary",link:"",onClick:C=>D(t)},{default:o(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[F,["creation.creationCategory/edit"]]]),b((i(),u(p,{type:"danger",link:"",onClick:C=>A(t.id,t.model_count)},{default:o(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[F,["creation.creationCategory/del"]]])]),_:1})]),_:1},8,["data"])),[[N,a(m).loading]]),k("div",_e,[e(K,{modelValue:a(m),"onUpdate:modelValue":l[2]||(l[2]=t=>se(m)?m.value=t:null),onChange:a(f)},null,8,["modelValue","onChange"])])]),_:1}),a(_)?(i(),u(me,{key:0,ref_key:"editRef",ref:d,onSuccess:a(f),onClose:l[3]||(l[3]=t=>_.value=!1)},null,8,["onSuccess"])):w("",!0)])}}});export{vt as default};
