import{x as B,y as D,E as T,w as j,P as F}from"./element-plus.5bcb7c8a.js";import{f as r,b as N}from"./index.850efb0d.js";import{u as P}from"./vue-clipboard3.ba321cef.js";import{d as U,r as $,b as S,a4 as L,o as c,c as d,W as o,Q as t,u as m,j as i,F as Q,a7 as R,P as W,a as p,U as q}from"./@vue.a11433a6.js";const z={class:"code-preview"},A={class:"flex",style:{height:"50vh"}},M=U({__name:"code-preview",props:{modelValue:{type:Boolean},code:{}},emits:["update:modelValue"],setup(_,{emit:f}){const b=_,h=f,{toClipboard:V}=P(),n=$("index0"),g=async a=>{try{await V(a),r.msgSuccess("\u590D\u5236\u6210\u529F")}catch{r.msgError("\u590D\u5236\u5931\u8D25")}},s=S({get(){return b.modelValue},set(a){h("update:modelValue",a)}});return(a,l)=>{const v=L("highlightjs"),y=T,C=N,E=j,k=B,x=D,w=F;return c(),d("div",z,[o(w,{modelValue:m(s),"onUpdate:modelValue":l[1]||(l[1]=e=>i(s)?s.value=e:null),width:"900px",title:"\u4EE3\u7801\u9884\u89C8"},{default:t(()=>[o(x,{modelValue:m(n),"onUpdate:modelValue":l[0]||(l[0]=e=>i(n)?n.value=e:null)},{default:t(()=>[(c(!0),d(Q,null,R(a.code,(e,u)=>(c(),W(k,{label:e.name,name:`index${u}`,key:u},{default:t(()=>[p("div",A,[o(y,{class:"flex-1"},{default:t(()=>[o(v,{autodetect:"",code:e.content},null,8,["code"])]),_:2},1024),p("div",null,[o(E,{onClick:G=>g(e.content),type:"primary",link:""},{icon:t(()=>[o(C,{name:"el-icon-CopyDocument"})]),default:t(()=>[q(" \u590D\u5236 ")]),_:2},1032,["onClick"])])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])])}}});export{M as _};
