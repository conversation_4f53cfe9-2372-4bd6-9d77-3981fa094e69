import{r as e}from"./index.850efb0d.js";function t(r){return e.get({url:"/draw.draw_square/lists",params:r},{ignoreCancelToken:!0})}function a(r){return e.get({url:"/draw.draw_square/detail",params:r})}function s(r){return e.post({url:"/draw.draw_square/add",params:r})}function i(r){return e.post({url:"/draw.draw_square/edit",params:r})}function o(r){return e.post({url:"/draw.draw_square/verifyStatus",params:r})}function n(r){return e.post({url:"/draw.draw_square/del",params:r})}function d(r){return e.post({url:"/draw.draw_square/isShow",params:r})}function q(r){return e.post({url:"/draw.draw_square/removeCategory",params:r})}function c(r){return e.get({url:"/music.musicSquare/lists",params:r},{ignoreCancelToken:!0})}function S(r){return e.get({url:"/music.musicSquare/detail",params:r})}function l(r){return e.post({url:"/music.musicSquare/add",params:r})}function g(r){return e.post({url:"/music.musicSquare/edit",params:r})}function f(r){return e.post({url:"/music.musicSquare/verifyStatus",params:r})}function p(r){return e.post({url:"/music.musicSquare/del",params:r})}function w(r){return e.post({url:"/music.musicSquare/isShow",params:r})}function v(r){return e.post({url:"/music.musicSquare/removeCategory",params:r})}function y(r){return e.get({url:"/video.videoSquare/lists",params:r},{ignoreCancelToken:!0})}function m(r){return e.get({url:"/video.videoSquare/detail",params:r})}function C(r){return e.post({url:"/video.videoSquare/add",params:r})}function D(r){return e.post({url:"/video.videoSquare/edit",params:r})}function M(r){return e.post({url:"/video.videoSquare/verifyStatus",params:r})}function V(r){return e.post({url:"/video.videoSquare/del",params:r})}function _(r){return e.post({url:"/video.videoSquare/isShow",params:r})}function k(r){return e.post({url:"/video.videoSquare/removeCategory",params:r})}function T(){return e.get({url:"/market.activityReward/getSquareSetting"})}function h(r){return e.post({url:"/market.activityReward/setSquareSetting",params:r})}function L(r){return e.get({url:"/square.squareCategory/lists",params:r})}function x(r){return e.get({url:"/square.squareCategory/categoryLists",params:r})}function A(r){return e.post({url:"/square.squareCategory/add",params:r})}function R(r){return e.post({url:"/square.squareCategory/edit",params:r})}function b(r){return e.post({url:"/square.squareCategory/del",params:r})}function j(r){return e.post({url:"/square.squareCategory/status",params:r})}export{D as A,C as B,m as C,_ as D,V as E,y as F,v as a,k as b,R as c,A as d,b as e,j as f,x as g,L as h,o as i,i as j,s as k,a as l,t as m,d as n,n as o,q as p,f as q,g as r,l as s,S as t,w as u,p as v,c as w,T as x,h as y,M as z};
