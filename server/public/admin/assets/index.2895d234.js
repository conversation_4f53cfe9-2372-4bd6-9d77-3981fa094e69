import{H as X,C as Y,D as Z,I as ee,J as te,w as le,F as ae,G as ue,K as oe,L as ne,M as se}from"./element-plus.5bcb7c8a.js";import{_ as ie,a as re}from"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as S,_ as me}from"./index.850efb0d.js";import{_ as pe}from"./index.vue_vue_type_script_setup_true_lang.1e869df8.js";import{d as O,a0 as I,r as L,aj as _e,o as u,c as g,W as t,Q as l,a8 as N,u as s,F as k,a7 as V,P as i,U as _,R as D,a as v,T as c,V as f,j as ce}from"./@vue.a11433a6.js";import{u as fe}from"./usePaging.b48cb079.js";import{d as ve,g as ye,a as be}from"./video.6ba3bf3d.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue-drag-resize.527c6620.js";import"./vue3-video-play.632beab5.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const Ce={class:"flex items-center"},Fe={class:"line-clamp-2 ml-[10px]"},ge=["onClick"],Ee={class:"flex justify-end mt-4"},ke=O({name:"videoRecord"}),ft=O({...ke,setup(Ve){const o=I({user_info:"",prompt:"",status:-1,style_id:"",type:"",start_time:"",end_time:"",channel:""}),h=L([]),j=d=>{h.value=d.map(a=>a.id)},y=I({show:!1,url:""}),z=d=>{y.url=d,setTimeout(()=>{y.show=!0})},T=async d=>{await S.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await ve({id:d}),w()},U=(d,a)=>{S.confirm(d,a,{showCancelButton:!1,type:"",customStyle:{"max-width":"550px"}})},{pager:b,getLists:w,resetPage:B,resetParams:A}=fe({fetchFun:be,params:o}),C=L({style:[],status:[],type:[],channel:[]});return(async()=>{C.value=await ye()})(),w(),(d,a)=>{const R=Y,m=Z,p=ee,E=te,q=pe,F=le,G=ae,$=ue,r=oe,K=me,x=X,H=ie,J=ne,M=de,Q=re,P=_e("perms"),W=se;return u(),g("div",null,[t($,{shadow:"never",class:"!border-none"},{default:l(()=>[t(G,{ref:"formRef",class:"mb-[-16px]",model:o,inline:!0},{default:l(()=>[t(m,{label:"\u7528\u6237\u4FE1\u606F"},{default:l(()=>[t(R,{class:"w-[280px]",modelValue:o.user_info,"onUpdate:modelValue":a[0]||(a[0]=e=>o.user_info=e),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237ID/\u7528\u6237\u6635\u79F0",clearable:"",onKeyup:N(s(B),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(m,{label:"\u5173\u952E\u8BCD"},{default:l(()=>[t(R,{class:"w-[280px]",modelValue:o.prompt,"onUpdate:modelValue":a[1]||(a[1]=e=>o.prompt=e),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD",clearable:"",onKeyup:N(s(B),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(m,{label:"\u751F\u6210\u7ED3\u679C"},{default:l(()=>[t(E,{class:"!w-[280px]",modelValue:o.status,"onUpdate:modelValue":a[2]||(a[2]=e=>o.status=e)},{default:l(()=>[t(p,{label:"\u5168\u90E8",value:-1}),(u(!0),g(k,null,V(s(C).status,(e,n)=>(u(),i(p,{label:e,value:n,key:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"\u751F\u6210\u7C7B\u578B"},{default:l(()=>[t(E,{class:"!w-[280px]",modelValue:o.type,"onUpdate:modelValue":a[3]||(a[3]=e=>o.type=e)},{default:l(()=>[t(p,{label:"\u5168\u90E8",value:""}),(u(!0),g(k,null,V(s(C).type,(e,n)=>(u(),i(p,{label:e,value:n,key:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"\u89C6\u9891\u98CE\u683C"},{default:l(()=>[t(E,{class:"!w-[280px]",modelValue:o.style_id,"onUpdate:modelValue":a[4]||(a[4]=e=>o.style_id=e)},{default:l(()=>[t(p,{label:"\u5168\u90E8",value:""}),(u(!0),g(k,null,V(s(C).style,(e,n)=>(u(),i(p,{label:e.name,value:e.id,key:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"\u97F3\u4E50\u6E20\u9053"},{default:l(()=>[t(E,{class:"!w-[280px]",modelValue:o.channel,"onUpdate:modelValue":a[5]||(a[5]=e=>o.channel=e)},{default:l(()=>[t(p,{label:"\u5168\u90E8",value:""}),(u(!0),g(k,null,V(s(C).channel,(e,n)=>(u(),i(p,{label:e,value:n,key:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"\u751F\u6210\u65F6\u95F4"},{default:l(()=>[t(q,{startTime:o.start_time,"onUpdate:startTime":a[6]||(a[6]=e=>o.start_time=e),endTime:o.end_time,"onUpdate:endTime":a[7]||(a[7]=e=>o.end_time=e)},null,8,["startTime","endTime"])]),_:1}),t(m,null,{default:l(()=>[t(F,{type:"primary",onClick:s(B)},{default:l(()=>[_("\u67E5\u8BE2")]),_:1},8,["onClick"]),t(F,{onClick:s(A)},{default:l(()=>[_("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),t($,{class:"!border-none mt-4",shadow:"never"},{default:l(()=>[D((u(),i(F,{class:"mb-4",onClick:a[8]||(a[8]=e=>T(s(h))),disabled:!s(h).length},{default:l(()=>[_(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])),[[P,["video.videoRecord/del"]]]),D((u(),i(J,{size:"large",data:s(b).lists,"row-key":"id",onSelectionChange:j},{default:l(()=>[t(r,{type:"selection",width:"55"}),t(r,{label:"\u7528\u6237\u6635\u79F0","min-width":"180"},{default:l(({row:e})=>[v("div",Ce,[e.avatar?(u(),i(K,{key:0,class:"flex-none rounded-[50%]",src:e.avatar,width:48,height:48,fit:"contain"},null,8,["src"])):c("",!0),v("div",Fe,f(e.nickname),1)])]),_:1}),t(r,{label:"\u7528\u6237\u8F93\u5165","min-width":"180"},{default:l(({row:e})=>[v("div",{class:"line-clamp-3 cursor-pointer",onClick:n=>U(e.prompt,"\u7528\u6237\u8F93\u5165")},f(e.prompt),9,ge)]),_:1}),t(r,{label:"\u53C2\u8003\u56FE",width:"100"},{default:l(({row:e})=>[e.image?(u(),i(K,{key:0,class:"flex-none",src:e.image,"preview-teleported":"","preview-src-list":[e.image],width:62,height:62,fit:"contain"},null,8,["src","preview-src-list"])):c("",!0)]),_:1}),t(r,{label:"\u751F\u6210\u7ED3\u679C","min-width":"180"},{default:l(({row:e})=>[e.status==0?(u(),i(x,{key:0,type:"info"},{default:l(()=>[_(f(e.status_desc),1)]),_:2},1024)):c("",!0),e.status==1?(u(),i(x,{key:1,type:"warning"},{default:l(()=>[_(f(e.status_desc),1)]),_:2},1024)):c("",!0),e.status==2?(u(),i(H,{key:2,"file-size":"100px",class:"cursor-pointer",type:"video",uri:e.video_url,onClick:n=>z(e.video_url)},null,8,["uri","onClick"])):c("",!0),e.status==3?(u(),i(x,{key:3,type:"danger"},{default:l(()=>[_(f(e.status_desc),1)]),_:2},1024)):c("",!0),e.status==3&&e.fail_reason?(u(),i(F,{key:4,type:"danger",link:!0,size:"small",onClick:n=>U(`\u9519\u8BEF\u4FE1\u606F\uFF1A${e.fail_reason}`,"\u9519\u8BEF\u539F\u56E0")},{default:l(()=>[_(" \u67E5\u770B\u539F\u56E0 ")]),_:2},1032,["onClick"])):c("",!0)]),_:1}),t(r,{label:"\u6E20\u9053","min-width":"120"},{default:l(({row:e})=>[v("div",null,f(e.channel||"-"),1)]),_:1}),t(r,{label:"\u89C6\u9891\u98CE\u683C","min-width":"100"},{default:l(({row:e})=>[v("div",null,f(e.style_desc||"-"),1)]),_:1}),t(r,{label:"\u6D88\u8017\u7535\u529B\u503C","min-width":"120",prop:"tokens"}),t(r,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"180"}),t(r,{label:"\u8BF7\u6C42ip",prop:"ip","min-width":"140"}),t(r,{label:"\u64CD\u4F5C",width:"100",fixed:"right"},{default:l(({row:e})=>[D((u(),i(F,{type:"danger",link:"",onClick:n=>T([e.id])},{default:l(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[P,["video.videoRecord/del"]]])]),_:1})]),_:1},8,["data"])),[[W,s(b).loading]]),v("div",Ee,[t(M,{modelValue:s(b),"onUpdate:modelValue":a[9]||(a[9]=e=>ce(b)?b.value=e:null),onChange:s(w)},null,8,["modelValue","onChange"])])]),_:1}),t(Q,{modelValue:y.show,"onUpdate:modelValue":a[10]||(a[10]=e=>y.show=e),type:"video",url:y.url},null,8,["modelValue","url"])])}}});export{ft as default};
