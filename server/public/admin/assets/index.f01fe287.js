import{W as I,I as N,J as M,D as S,C as U,w as q,F as P,b as W,E as j,G as A}from"./element-plus.5bcb7c8a.js";import{r as G}from"./index.850efb0d.js";import{u as J}from"./useLockFn.b2f69334.js";import{d as K,r as m,i as O,o as r,c as a,W as o,Q as l,a as s,u as t,F as f,a7 as x,P as Q,U as z,T as b,V as u}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const H="/admin/assets/empty.dce45c37.png";function R(_){return G.post({url:"/know.Embedding/test",params:_})}const X={style:{height:"calc(100vh - 130px)"}},Y={class:"grid grid-cols-2 gap-4 h-full"},Z={class:"px-[10px] py-[5px] h-full min-h-0",style:{"border-left":"1px solid #e2e2e2"}},$={key:0,class:"flex flex-col items-center justify-center h-full"},tt=s("div",{class:"mt-[10px] text-[#5a646e]"},"\u6D4B\u8BD5\u7ED3\u679C\u5C06\u5728\u8FD9\u91CC\u5C55\u793A",-1),et={key:0},ot={class:"text-sm text-tx-secondary mt-[5px]"},lt={class:"text-sm text-tx-secondary"},Gt=K({__name:"index",setup(_){const n=m({kb_id:"",text:""}),g=m([]),i=m([]),h=async()=>{},y=async()=>{i.value=await R({...n.value})},{lockFn:k,isLock:v}=J(y);return O(()=>{h()}),(st,d)=>{const E=N,w=M,p=S,C=U,B=q,D=P,V=W,F=I,L=j,T=A;return r(),a("div",X,[o(T,{class:"!border-none h-full",shadow:"never","body-style":{height:"100%"}},{default:l(()=>[s("div",Y,[s("div",null,[o(D,{"label-width":"90px"},{default:l(()=>[o(p,{label:"\u9009\u62E9\u77E5\u8BC6\u5E93"},{default:l(()=>[o(w,{modelValue:t(n).kb_id,"onUpdate:modelValue":d[0]||(d[0]=e=>t(n).kb_id=e),class:"w-full",filterable:""},{default:l(()=>[(r(!0),a(f,null,x(t(g),(e,c)=>(r(),Q(E,{key:c,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(p,{label:"\u6D4B\u8BD5\u6587\u672C"},{default:l(()=>[o(C,{modelValue:t(n).text,"onUpdate:modelValue":d[1]||(d[1]=e=>t(n).text=e),type:"textarea",rows:"20"},null,8,["modelValue"])]),_:1}),o(p,null,{default:l(()=>[s("div",null,[o(B,{loading:t(v),onClick:t(k),disabled:t(n).text==""||t(n).kb_id=="",type:"primary"},{default:l(()=>[z(" \u6D4B\u8BD5 ")]),_:1},8,["loading","onClick","disabled"])])]),_:1})]),_:1})]),s("div",Z,[t(i).length==0?(r(),a("div",$,[o(V,{src:t(H)},null,8,["src"]),tt])):b("",!0),o(L,null,{default:l(()=>[t(i).length!=0?(r(),a("div",et,[(r(!0),a(f,null,x(t(i),(e,c)=>(r(),a("div",{class:"p-[10px] border border-solid border-br-light mb-[10px] rounded",key:c},[o(F,{percentage:Math.abs(e.distance/1)*100,color:"var(--el-text-color-disabled)"},{default:l(()=>[s("span",null,u(Math.abs(e.distance).toFixed(5)),1)]),_:2},1032,["percentage"]),s("div",ot,u(e.question),1),s("div",lt,u(e.answer),1)]))),128))])):b("",!0)]),_:1})])])]),_:1})])}}});export{Gt as default};
