import{Q as N,R as $,x as L,y as P,D as j,C as G,t as W,w as q,F as H}from"./element-plus.5bcb7c8a.js";import{_ as J}from"./index.4a09b22e.js";import{b as K}from"./index.850efb0d.js";import{_ as O}from"./picker.e4cc82a9.js";import{_ as X}from"./picker.9a1dad65.js";import{m as A}from"./@vueuse.a2407f20.js";import{D as U}from"./vuedraggable.2019ddfd.js";import{d as Y,r as Z,o as v,c as ll,W as l,Q as u,u as t,j as B,U as f,a as d,P as D}from"./@vue.a11433a6.js";const el={class:"bg-fill-light w-full p-4 mb-4"},ul=d("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A1920px*347px",-1),ol={class:"bg-fill-light w-full p-4 mb-4"},al=d("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A240px*34px",-1),tl=d("div",{class:"el-form-item__label w-[70px] mb-[10px]"},"\u83DC\u5355\u8BBE\u7F6E",-1),dl={class:"bg-fill-light w-full p-4 mb-4"},nl={class:"w-full"},sl={class:"flex-1 flex items-center"},ml={class:"drag-move cursor-move ml-auto"},il=d("div",{class:"el-form-item__label w-[70px] mb-[10px]"},"\u83DC\u5355\u8BBE\u7F6E",-1),pl={class:"bg-fill-light w-full p-4 mb-4"},_l={class:"w-full"},rl={class:"flex-1 flex items-center"},cl={class:"drag-move cursor-move ml-auto"},fl={class:"bg-fill-light w-full p-4 mb-4"},Vl=d("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A120px*120px",-1),bl={class:"bg-fill-light w-full p-4 mb-4"},vl=d("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A120px*120px",-1),Ul=Y({__name:"prop",props:{isShow:{type:Boolean},prop:{}},emits:["update:prop","update:isShow"],setup(S,{emit:k}){const E=S,F=k,o=A(E,"prop",F),V=A(E,"isShow",F),b=Z("bg"),Q=()=>{o.value.columnMenu1.push({title:"",isShow:!0})},y=_=>{o.value.columnMenu1.splice(_,1)},M=()=>{o.value.columnMenu2.push({title:"",isShow:!0})},T=_=>{o.value.columnMenu2.splice(_,1)};return(_,a)=>{const g=N,z=$,n=j,r=X,c=L,m=G,i=W,h=O,w=K,x=J,C=q,I=P,R=H;return v(),ll("div",null,[l(R,{"label-width":"70px"},{default:u(()=>[l(n,{label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[l(z,{modelValue:t(V),"onUpdate:modelValue":a[0]||(a[0]=e=>B(V)?V.value=e:null),class:"ml-4"},{default:u(()=>[l(g,{label:!0},{default:u(()=>[f("\u663E\u793A")]),_:1}),l(g,{label:!1},{default:u(()=>[f("\u4E0D\u663E\u793A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(I,{modelValue:t(b),"onUpdate:modelValue":a[15]||(a[15]=e=>B(b)?b.value=e:null)},{default:u(()=>[l(c,{label:"\u80CC\u666F\u8BBE\u7F6E",name:"bg"},{default:u(()=>[d("div",null,[d("div",el,[l(n,{label:"\u80CC\u666F\u56FE\u7247"},{default:u(()=>[d("div",null,[l(r,{modelValue:t(o).bgImage,"onUpdate:modelValue":a[1]||(a[1]=e=>t(o).bgImage=e),"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue"]),ul])]),_:1})])])]),_:1}),l(c,{label:"\u5DE6\u4FA7\u8BBE\u7F6E",name:"left"},{default:u(()=>[d("div",null,[d("div",ol,[l(n,{label:"logo\u56FE\u7247"},{default:u(()=>[d("div",null,[l(r,{modelValue:t(o).logoImage,"onUpdate:modelValue":a[2]||(a[2]=e=>t(o).logoImage=e),"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue"]),al])]),_:1}),l(n,{label:"\u5E7F\u544A\u8BED"},{default:u(()=>[l(m,{modelValue:t(o).content,"onUpdate:modelValue":a[3]||(a[3]=e=>t(o).content=e)},null,8,["modelValue"])]),_:1}),l(n,{label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[l(i,{modelValue:t(o).isShowLeft,"onUpdate:modelValue":a[4]||(a[4]=e=>t(o).isShowLeft=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})])])]),_:1}),l(c,{label:"\u4E2D\u95F4\u83DC\u5355",name:"middle"},{default:u(()=>[l(n,{label:"\u680F\u76EE\u4E00"},{default:u(()=>[l(m,{modelValue:t(o).column1,"onUpdate:modelValue":a[5]||(a[5]=e=>t(o).column1=e)},null,8,["modelValue"])]),_:1}),d("div",null,[tl,l(t(U),{class:"draggable px-[10px]",modelValue:t(o).columnMenu1,"onUpdate:modelValue":a[6]||(a[6]=e=>t(o).columnMenu1=e),animation:"300",handle:".drag-move"},{item:u(({element:e,index:p})=>[(v(),D(x,{key:p,onClose:s=>y(p)},{default:u(()=>[d("div",dl,[l(n,{label:"\u83DC\u5355\u540D\u79F0"},{default:u(()=>[l(m,{modelValue:e.title,"onUpdate:modelValue":s=>e.title=s},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(n,{label:"\u8DF3\u8F6C\u94FE\u63A5"},{default:u(()=>[d("div",nl,[l(h,{modelValue:e.link,"onUpdate:modelValue":s=>e.link=s},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1024),l(n,{class:"mt-[18px]",label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[d("div",sl,[l(i,{"active-value":!0,"inactive-value":!1,modelValue:e.isShow,"onUpdate:modelValue":s=>e.isShow=s},null,8,["modelValue","onUpdate:modelValue"]),d("div",ml,[l(w,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"]),l(n,{"label-width":"0"},{default:u(()=>[l(C,{type:"primary",onClick:Q},{default:u(()=>[f("\u6DFB\u52A0\u83DC\u5355")]),_:1})]),_:1})]),l(n,{label:"\u680F\u76EE\u4E8C"},{default:u(()=>[l(m,{modelValue:t(o).column2,"onUpdate:modelValue":a[7]||(a[7]=e=>t(o).column2=e)},null,8,["modelValue"])]),_:1}),d("div",null,[il,l(t(U),{class:"draggable px-[10px]",modelValue:t(o).columnMenu2,"onUpdate:modelValue":a[8]||(a[8]=e=>t(o).columnMenu2=e),animation:"300",handle:".drag-move"},{item:u(({element:e,index:p})=>[(v(),D(x,{key:p,onClose:s=>T(p)},{default:u(()=>[d("div",pl,[l(n,{label:"\u83DC\u5355\u540D\u79F0"},{default:u(()=>[l(m,{modelValue:e.title,"onUpdate:modelValue":s=>e.title=s},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(n,{label:"\u8DF3\u8F6C\u94FE\u63A5"},{default:u(()=>[d("div",_l,[l(h,{modelValue:e.link,"onUpdate:modelValue":s=>e.link=s},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1024),l(n,{class:"mt-[18px]",label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[d("div",rl,[l(i,{"active-value":!0,"inactive-value":!1,modelValue:e.isShow,"onUpdate:modelValue":s=>e.isShow=s},null,8,["modelValue","onUpdate:modelValue"]),d("div",cl,[l(w,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"]),l(n,{"label-width":"0"},{default:u(()=>[l(C,{type:"primary",onClick:M},{default:u(()=>[f("\u6DFB\u52A0\u83DC\u5355")]),_:1})]),_:1})])]),_:1}),l(c,{label:"\u53F3\u4FA7\u4E8C\u7EF4\u7801",name:"right"},{default:u(()=>[d("div",null,[d("div",fl,[l(n,{label:"\u4E8C\u7EF4\u7801"},{default:u(()=>[d("div",null,[l(r,{modelValue:t(o).rightQrcode1,"onUpdate:modelValue":a[9]||(a[9]=e=>t(o).rightQrcode1=e),"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue"]),Vl])]),_:1}),l(n,{label:"\u6807\u9898\u540D\u79F0"},{default:u(()=>[l(m,{modelValue:t(o).rightQrcodeTitle1,"onUpdate:modelValue":a[10]||(a[10]=e=>t(o).rightQrcodeTitle1=e)},null,8,["modelValue"])]),_:1}),l(n,{label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[l(i,{modelValue:t(o).rightQrcodeShow1,"onUpdate:modelValue":a[11]||(a[11]=e=>t(o).rightQrcodeShow1=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),d("div",bl,[l(n,{label:"\u4E8C\u7EF4\u7801"},{default:u(()=>[d("div",null,[l(r,{modelValue:t(o).rightQrcode2,"onUpdate:modelValue":a[12]||(a[12]=e=>t(o).rightQrcode2=e),"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue"]),vl])]),_:1}),l(n,{label:"\u6807\u9898\u540D\u79F0"},{default:u(()=>[l(m,{modelValue:t(o).rightQrcodeTitle2,"onUpdate:modelValue":a[13]||(a[13]=e=>t(o).rightQrcodeTitle2=e)},null,8,["modelValue"])]),_:1}),l(n,{label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[l(i,{modelValue:t(o).rightQrcodeShow2,"onUpdate:modelValue":a[14]||(a[14]=e=>t(o).rightQrcodeShow2=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})])])]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}});export{Ul as _};
