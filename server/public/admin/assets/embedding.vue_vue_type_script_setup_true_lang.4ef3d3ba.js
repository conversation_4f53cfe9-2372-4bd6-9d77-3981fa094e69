import{Q as P,R as K,w as O,D as q,C as L,F as M}from"./element-plus.5bcb7c8a.js";import{g as Q}from"./index.850efb0d.js";import{m as S}from"./@vueuse.a2407f20.js";import{d as T,s as z,r as b,b as v,w as W,a4 as X,aj as H,o as s,c as p,W as r,Q as o,a as n,u as t,j as J,F as Y,a7 as Z,P as _,U as c,V as h,R as $,T as m}from"./@vue.a11433a6.js";const ee={class:"pt-[10px]"},te={key:0,class:"form-tips !text-[14px]"},ue={key:1},oe=n("span",{class:"form-tips !text-[14px]"},"\u5F00\u901A\u7F51\u5740\uFF1Ahttps://open.bigmodel.cn/dev/api#text_embedding",-1),ae={href:"https://open.bigmodel.cn/dev/api#text_embedding",target:"_blank",rel:"noopener noreferrer"},le={key:2},se=n("span",{class:"form-tips !text-[14px]"},"\u5F00\u901A\u7F51\u5740\uFF1Ahttps://www.xfyun.cn/doc/spark/embedding_api.html",-1),ne={href:"https://www.xfyun.cn/doc/spark/embedding_api.html",target:"_blank",rel:"noopener noreferrer"},re={class:"w-[400px]"},ie={class:"form-tips"},de={class:"w-[400px]"},pe={class:"form-tips"},Fe=T({__name:"embedding",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(y,{expose:x,emit:V}){const g=y,B=V,E=z(),d=S(g,"modelValue",B),a=b({}),u=b(""),w=v(()=>u.value.includes("zhipu")),C=v(()=>u.value==="xunfei"),k=v(()=>u.value.includes("openai")),R=i=>{Object.keys(d.value).forEach(e=>{d.value[e].checked=!1,i===e&&(a.value=d.value[e],d.value[e].checked=!0)})};return W(()=>g.modelValue,i=>{u.value=Object.keys(d.value).find(e=>i[e].checked)||"",console.log(u.value),u.value&&(a.value=d.value[u.value])},{immediate:!0}),x({validate(){var i;return(i=E.value)==null?void 0:i.validate()}}),(i,e)=>{const j=P,N=K,f=O,U=X("router-link"),F=q,D=L,G=M,I=H("perms");return s(),p("div",ee,[r(G,{"label-width":"120px",ref_key:"formRef",ref:E,model:t(a)},{default:o(()=>[r(F,{label:"AI\u63A5\u53E3"},{default:o(()=>[n("div",null,[r(N,{modelValue:t(u),"onUpdate:modelValue":e[0]||(e[0]=l=>J(u)?u.value=l:null),onChange:e[1]||(e[1]=l=>R(l))},{default:o(()=>[(s(!0),p(Y,null,Z(y.modelValue,(l,A)=>(s(),_(j,{key:A,label:A},{default:o(()=>[c(h(l.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),t(k)?(s(),p("div",te,[c(" \u9009\u62E9\u6B64\u901A\u9053\uFF0C\u9700\u8981\u5728\u3010key\u6C60\u3011\u6DFB\u52A0gpt3.5\u7684key\u624D\u80FD\u6B63\u5E38\u4F7F\u7528\uFF0C\u5426\u5219\u65E0\u6CD5\u8BAD\u7EC3 "),$((s(),_(U,{to:{path:t(Q)("setting.KeyPool/lists")}},{default:o(()=>[r(f,{type:"primary",link:""},{default:o(()=>[c("\u524D\u5F80\u6DFB\u52A0")]),_:1})]),_:1},8,["to"])),[[I,["setting.KeyPool/lists"]]])])):m("",!0),t(w)?(s(),p("div",ue,[oe,n("a",ae,[r(f,{type:"primary",link:"",class:"ml-2"},{default:o(()=>[c("\u524D\u5F80\u5F00\u901A")]),_:1})])])):m("",!0),t(C)?(s(),p("div",le,[se,n("a",ne,[r(f,{type:"primary",link:"",class:"ml-2"},{default:o(()=>[c("\u524D\u5F80\u5F00\u901A")]),_:1})])])):m("",!0)])]),_:1}),t(k)?(s(),_(F,{key:0,label:"\u81EA\u5B9A\u4E49api\u57DF\u540D"},{default:o(()=>[n("div",re,[r(D,{modelValue:t(a).agency_api,"onUpdate:modelValue":e[2]||(e[2]=l=>t(a).agency_api=l),placeholder:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49\u57DF\u540D"},null,8,["modelValue"]),n("div",ie,h(t(a).agency_tips),1)])]),_:1})):m("",!0),t(u)==="m3e"?(s(),_(F,{key:1,label:"\u81EA\u5B9A\u4E49api\u57DF\u540D",prop:"agency_api",rules:[{message:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49api\u57DF\u540D",required:!0}]},{default:o(()=>[n("div",de,[r(D,{modelValue:t(a).agency_api,"onUpdate:modelValue":e[3]||(e[3]=l=>t(a).agency_api=l),placeholder:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49api\u57DF\u540D"},null,8,["modelValue"]),n("div",pe,h(t(a).agency_tips),1)])]),_:1})):m("",!0)]),_:1},8,["model"])])}}});export{Fe as _};
