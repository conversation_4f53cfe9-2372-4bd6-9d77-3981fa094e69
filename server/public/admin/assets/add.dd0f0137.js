import{_ as y}from"./index.88e852a7.js";import{w as b}from"./element-plus.5bcb7c8a.js";import{a as v,u as h}from"./vue-router.919c7bec.js";import{p as R}from"./model.63e41af8.js";import{_ as k}from"./form.vue_vue_type_script_setup_true_lang.7cdbac94.js";import{i as w}from"./index.850efb0d.js";import{d as n,s as V,a0 as B,o as T,c as g,W as o,u as a,j as x,Q as s,U as C}from"./@vue.a11433a6.js";import"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./picker.9a1dad65.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const D=n({name:"aiModelAdd"}),St=n({...D,setup(S){const l=w(),r=v(),e=h(),m=e.query.type,i=V(),t=B({type:1,channel:"",logo:"",name:"",configs:{},is_enable:1,models:[]}),u=async()=>{await i.value.validate(),await R({...t,type:m}),setTimeout(()=>{r.back(),l.removeTab(e.fullPath,r)})};return(f,p)=>{const c=b,_=y;return T(),g("div",null,[o(k,{ref_key:"formRef",ref:i,modelValue:a(t),"onUpdate:modelValue":p[0]||(p[0]=d=>x(t)?t.value=d:null),"header-title":f.$route.meta.title,type:a(m)},null,8,["modelValue","header-title","type"]),o(_,null,{default:s(()=>[o(c,{type:"primary",onClick:u},{default:s(()=>[C("\u4FDD\u5B58")]),_:1})]),_:1})])}}});export{St as default};
