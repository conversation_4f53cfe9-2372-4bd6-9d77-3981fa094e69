import{s as w,C as v,w as S,Q as D,R as I,a1 as N,D as T,B as j,F as R}from"./element-plus.5bcb7c8a.js";import{d as U,b as $,o as d,c as g,W as o,u as b,j as _,Q as n,U as s,P as f,a as m,T as V,K as k,bk as O,bj as P}from"./@vue.a11433a6.js";import{_ as Q}from"./picker.9a1dad65.js";import{u as z}from"./index.850efb0d.js";import{_ as G}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const K={class:"color-picker flex flex-1"},W=U({__name:"index",props:{modelValue:{type:String},defaultColor:{type:String}},emits:["update:modelValue"],setup(e,{emit:x}){const p=e,C=x,u=$({get(){return p.modelValue},set(c){C("update:modelValue",c)}}),t=["#409EFF","#28C76F","#EA5455","#FF9F43","#01CFE8","#4A5DFF"],r=()=>{u.value=p.defaultColor};return(c,a)=>{const y=w,E=v,F=S;return d(),g("div",K,[o(y,{modelValue:b(u),"onUpdate:modelValue":a[0]||(a[0]=i=>_(u)?u.value=i:null),predefine:t},null,8,["modelValue"]),o(E,{modelValue:b(u),"onUpdate:modelValue":a[1]||(a[1]=i=>_(u)?u.value=i:null),class:"mx-[10px] flex-1",type:"text",readonly:""},null,8,["modelValue"]),o(F,{type:"text",onClick:r},{default:n(()=>[s("\u91CD\u7F6E")]),_:1})])}}}),q=e=>(O("data-v-0a31aa07"),e=e(),P(),e),H=["src"],J=["src"],L=q(()=>m("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8: 750 * 400",-1)),M={class:"flex"},X=U({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},type:{type:String,default:"mobile"}},setup(e){const x=e,p=z(),C=u=>{x.content.poster=u};return(u,t)=>{const r=D,c=I,a=T,y=Q,E=v,F=j,i=W,B=N,A=R;return d(),g("div",null,[o(A,{"label-width":"70px"},{default:n(()=>[e.type=="mobile"?(d(),f(a,{key:0,label:"\u6D77\u62A5\u80CC\u666F"},{default:n(()=>[o(c,{modelValue:e.content.default,"onUpdate:modelValue":t[0]||(t[0]=l=>e.content.default=l)},{default:n(()=>[m("div",null,[o(r,{label:1},{default:n(()=>[s("\u7CFB\u7EDF\u9ED8\u8BA4")]),_:1}),o(r,{label:2},{default:n(()=>[s("\u81EA\u5B9A\u4E49")]),_:1})])]),_:1},8,["modelValue"])]),_:1})):V("",!0),e.type=="mobile"&&e.content.default==1?(d(),f(a,{key:1,label:""},{default:n(()=>[m("img",{src:b(p).getImageUrl(e.content.defaultUrl1),alt:"",class:k(["w-[80px] mr-[30px] cursor-pointer p-[10px]",{actived:e.content.poster==1}]),onClick:t[1]||(t[1]=l=>C(1))},null,10,H),m("img",{src:b(p).getImageUrl(e.content.defaultUrl2),alt:"",class:k(["w-[80px] cursor-pointer p-[10px]",{actived:e.content.poster==2}]),onClick:t[2]||(t[2]=l=>C(2))},null,10,J)]),_:1})):V("",!0),e.type=="mobile"&&e.content.default==2?(d(),f(a,{key:2,label:""},{default:n(()=>[m("div",null,[o(y,{modelValue:e.content.posterUrl,"onUpdate:modelValue":t[3]||(t[3]=l=>e.content.posterUrl=l),"exclude-domain":""},null,8,["modelValue"]),L])]),_:1})):V("",!0),e.type=="mobile"?(d(),f(a,{key:3,label:"\u9080\u8BF7\u6587\u6848"},{default:n(()=>[m("div",M,[o(E,{placeholder:"",modelValue:e.content.data,"onUpdate:modelValue":t[4]||(t[4]=l=>e.content.data=l),class:"w-[300px]"},null,8,["modelValue"]),o(F,{class:"ml-2","true-label":1,modelValue:e.content.showData,"onUpdate:modelValue":t[5]||(t[5]=l=>e.content.showData=l),"false-label":0},{default:n(()=>[s("\u663E\u793A")]),_:1},8,["modelValue"])])]),_:1})):V("",!0),o(a,{label:"\u80CC\u666F\u989C\u8272"},{default:n(()=>[m("div",null,[o(i,{modelValue:e.content.bgColor,"onUpdate:modelValue":t[6]||(t[6]=l=>e.content.bgColor=l)},null,8,["modelValue"])])]),_:1}),o(a,{label:"\u6587\u5B57\u989C\u8272"},{default:n(()=>[m("div",null,[o(i,{modelValue:e.content.textColor,"onUpdate:modelValue":t[7]||(t[7]=l=>e.content.textColor=l),"default-color":"#ffffff"},null,8,["modelValue"])])]),_:1}),o(a,{label:"\u5185\u5BB9\u663E\u793A"},{default:n(()=>[o(c,{modelValue:e.content.showContentType,"onUpdate:modelValue":t[8]||(t[8]=l=>e.content.showContentType=l)},{default:n(()=>[o(r,{label:1},{default:n(()=>[s("\u56FA\u5B9A\u884C\u6570")]),_:1}),o(r,{label:2},{default:n(()=>[s("\u663E\u793A\u5168\u90E8")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.content.showContentType==1?(d(),f(a,{key:4},{default:n(()=>[o(B,{min:1,max:30,modelValue:e.content.contentNum,"onUpdate:modelValue":t[9]||(t[9]=l=>e.content.contentNum=l),"show-input":""},null,8,["modelValue"])]),_:1})):V("",!0)]),_:1})])}}});const Je=G(X,[["__scopeId","data-v-0a31aa07"]]);export{Je as default};
