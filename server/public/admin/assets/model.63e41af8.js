import{r as e}from"./index.850efb0d.js";function n(){return e.get({url:"/setting.ai.models/lists"})}function i(t){return e.get({url:"/setting.ai.models/detail",params:t})}function l(t){return e.get({url:"/setting.ai.models/channels",params:t})}function o(t){return e.post({url:"/setting.ai.models/add",params:t})}function r(t){return e.post({url:"/setting.ai.models/edit",params:t})}function u(t){return e.post({url:"/setting.ai.models/del",params:t})}function a(t){return e.post({url:"/setting.ai.models/sort",params:t})}function d(){return e.get({url:"/setting.ai.cost/models"})}function g(t){return e.get({url:"/setting.ai.models/lists",params:t})}function c(t){return e.post({url:"/setting.ai.cost/save",params:t})}function f(t){return e.post({url:"/setting.ai.cost/checkDel",params:t})}export{r as a,i as b,n as c,u as d,a as e,f,l as g,d as h,g as i,o as p,c as s};
