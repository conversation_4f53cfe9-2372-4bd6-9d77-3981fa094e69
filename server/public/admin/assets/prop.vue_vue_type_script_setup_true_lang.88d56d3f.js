import{Q as S,R as y,D as R,C as z,t as M,w as N,F as $}from"./element-plus.5bcb7c8a.js";import{_ as I}from"./index.4a09b22e.js";import{b as P}from"./index.850efb0d.js";import{_ as Q}from"./picker.e4cc82a9.js";import{_ as j}from"./picker.9a1dad65.js";import{m as F}from"./@vueuse.a2407f20.js";import{D as G}from"./vuedraggable.2019ddfd.js";import{d as T,o as E,c as W,W as e,Q as l,u as d,j as q,U as p,a as t,P as H}from"./@vue.a11433a6.js";const J=t("span",{class:"form-tips !text-xs"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A1168*675px",-1),K={class:"bg-fill-light w-full p-4 mb-4"},L=t("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A1168px*675px",-1),O={class:"flex-1 flex items-center"},X={class:"drag-move cursor-move ml-auto"},ne=T({__name:"prop",props:{isShow:{type:Boolean},prop:{}},emits:["update:prop","update:isShow"],setup(b,{emit:h}){const _=b,i=h,n=F(_,"prop",i),m=F(_,"isShow",i),v=()=>{n.value.data.push({image:"",title:"\u8FD9\u662F\u6807\u9898",subtitle:"\u8FD9\u662F\u4E00\u6BB5\u6807\u9898\u7B80\u4ECB",functionPoint:[],isShow:!0})},B=c=>{n.value.data.splice(c,1)};return(c,s)=>{const r=S,g=y,u=R,w=j,f=z,A=Q,x=M,U=P,C=I,D=N,k=$;return E(),W("div",null,[e(k,{"label-width":"70px"},{default:l(()=>[e(u,{label:"\u662F\u5426\u663E\u793A"},{default:l(()=>[e(g,{modelValue:d(m),"onUpdate:modelValue":s[0]||(s[0]=o=>q(m)?m.value=o:null),class:"ml-4"},{default:l(()=>[e(r,{label:!0},{default:l(()=>[p("\u663E\u793A")]),_:1}),e(r,{label:!1},{default:l(()=>[p("\u4E0D\u663E\u793A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t("div",null,[e(u,{label:"\u5BFC\u822A\u83DC\u5355"},{default:l(()=>[J]),_:1}),e(d(G),{class:"draggable",modelValue:d(n).data,"onUpdate:modelValue":s[1]||(s[1]=o=>d(n).data=o),animation:"300",handle:".drag-move"},{item:l(({element:o,index:V})=>[(E(),H(C,{key:V,onClose:a=>B(V)},{default:l(()=>[t("div",K,[e(u,{label:"\u56FE\u7247\u8BBE\u7F6E"},{default:l(()=>[t("div",null,[e(w,{modelValue:o.image,"onUpdate:modelValue":a=>o.image=a,"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue","onUpdate:modelValue"]),L])]),_:2},1024),e(u,{label:"\u6807\u9898\u540D\u79F0"},{default:l(()=>[e(f,{modelValue:o.title,"onUpdate:modelValue":a=>o.title=a},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u526F\u6807\u9898"},{default:l(()=>[e(f,{modelValue:o.subtitle,"onUpdate:modelValue":a=>o.subtitle=a,type:"textarea",rows:4,resize:"none"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u8DF3\u8F6C\u94FE\u63A5"},{default:l(()=>[e(A,{modelValue:o.link,"onUpdate:modelValue":a=>o.link=a},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{class:"mt-[18px]",label:"\u662F\u5426\u663E\u793A"},{default:l(()=>[t("div",O,[e(x,{"active-value":!0,"inactive-value":!1,modelValue:o.isShow,"onUpdate:modelValue":a=>o.isShow=a},null,8,["modelValue","onUpdate:modelValue"]),t("div",X,[e(U,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])]),e(u,{"label-width":"0"},{default:l(()=>[e(D,{type:"primary",onClick:v},{default:l(()=>[p("\u6DFB\u52A0\u529F\u80FD")]),_:1})]),_:1})]),_:1})])}}});export{ne as _};
