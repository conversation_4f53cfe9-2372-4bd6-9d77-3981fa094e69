import{C as N,D as Q,I as T,J as W,w as j,F as q,G as z,b as G,K as J,t as M,L as O,M as A}from"./element-plus.5bcb7c8a.js";import{f as E,b as H}from"./index.850efb0d.js";import{d as x,s as X,a0 as Y,r as Z,aj as ee,o as i,c as te,W as e,Q as t,u as a,a8 as oe,U as c,a as ae,R as _,P as s,T as V}from"./@vue.a11433a6.js";import{u as le}from"./usePaging.b48cb079.js";import{_ as ne}from"./edit.vue_vue_type_script_setup_true_lang.255b72cd.js";import{d as ie,b as re,g as me}from"./problem_category.9c1e9165.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./picker.9a1dad65.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";const se=x({name:"problemExample"}),st=x({...se,setup(pe){const v=X(),r=Y({name:"",status:""}),P=Z(!0),y=(m,l={})=>{var d;(d=v.value)==null||d.open(m,l)},S=async(m,l)=>{if(l!=0){E.msgWarning("\u8BF7\u89E3\u9664\u793A\u4F8B\u540E\u518D\u8BD5\uFF01");return}await E.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await ie({id:m}),f()},$=m=>{re({id:m})},{pager:C,getLists:f,resetPage:k,resetParams:B}=le({fetchFun:me,params:r});return f(),(m,l)=>{const d=N,g=Q,b=T,R=W,p=j,U=q,w=z,D=H,F=G,n=J,I=M,K=O,h=ee("perms"),L=A;return i(),te("div",null,[e(w,{class:"!border-none",shadow:"never"},{default:t(()=>[e(U,{ref:"formRef",class:"mb-[-16px]",model:a(r),inline:!0},{default:t(()=>[e(g,{label:"\u5206\u7C7B\u540D\u79F0"},{default:t(()=>[e(d,{class:"w-[280px]",modelValue:a(r).name,"onUpdate:modelValue":l[0]||(l[0]=o=>a(r).name=o),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:"",onKeyup:oe(a(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(g,{label:"\u7C7B\u76EE\u72B6\u6001"},{default:t(()=>[e(R,{class:"w-[280px]",modelValue:a(r).status,"onUpdate:modelValue":l[1]||(l[1]=o=>a(r).status=o)},{default:t(()=>[e(b,{label:"\u5168\u90E8",value:""}),e(b,{label:"\u5F00\u542F",value:1}),e(b,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:t(()=>[e(p,{type:"primary",onClick:a(k)},{default:t(()=>[c("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(p,{onClick:a(B)},{default:t(()=>[c("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(w,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[ae("div",null,[_((i(),s(p,{type:"primary",onClick:l[2]||(l[2]=o=>y("add"))},{icon:t(()=>[e(D,{name:"el-icon-Plus"})]),default:t(()=>[c(" \u65B0\u589E ")]),_:1})),[[h,["chat.chat_category/add"]]])]),_((i(),s(K,{size:"large",class:"mt-4",data:a(C).lists},{default:t(()=>[e(n,{label:"\u5206\u7C7B\u56FE\u6807","min-width":"120"},{default:t(({row:o})=>[o.image?(i(),s(F,{key:0,src:o.image,class:"w-[47px] h-[47px]"},null,8,["src"])):V("",!0)]),_:1}),e(n,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name","min-width":"120"}),e(n,{label:"\u793A\u4F8B\u6570\u91CF","min-width":"100",prop:"sample_count"}),e(n,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:o})=>[e(I,{onChange:u=>$(o.id),modelValue:o.status,"onUpdate:modelValue":u=>o.status=u,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),e(n,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(n,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(n,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:t(({row:o})=>[_((i(),s(p,{type:"primary",link:"",onClick:u=>y("edit",o)},{default:t(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[h,["chat.chat_category/edit"]]]),_((i(),s(p,{type:"danger",link:"",onClick:u=>S(o.id,o.sample_count)},{default:t(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["chat.chat_category/del"]]])]),_:1})]),_:1},8,["data"])),[[L,a(C).loading]])]),_:1}),a(P)?(i(),s(ne,{key:0,ref_key:"editRef",ref:v,onSuccess:a(f)},null,8,["onSuccess"])):V("",!0)])}}});export{st as default};
