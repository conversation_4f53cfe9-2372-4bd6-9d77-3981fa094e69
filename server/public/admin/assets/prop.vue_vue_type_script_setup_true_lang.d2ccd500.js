import{Q as E,R as b,D as V,C as F,F as x}from"./element-plus.5bcb7c8a.js";import{_ as A}from"./picker.9a1dad65.js";import{m as B}from"./@vueuse.a2407f20.js";import{d as C,o as g,c as D,W as e,Q as o,a as s,u,U as m}from"./@vue.a11433a6.js";const U=s("div",{class:"form-tips"}," \u5EFA\u8BAE\u56FE\u7247\u5C3A\u5BF8\u4E3A\uFF1A1920px*300px ",-1),Q=C({__name:"prop",props:{prop:{}},emits:["update:prop"],setup(d,{emit:r}){const l=B(d,"prop",r);return(N,t)=>{const _=A,n=V,i=F,p=E,c=b,f=x;return g(),D("div",null,[e(f,{"label-width":"70px"},{default:o(()=>[e(n,{label:"\u80CC\u666F\u56FE\u7247"},{default:o(()=>[s("div",null,[e(_,{modelValue:u(l).banner_bg,"onUpdate:modelValue":t[0]||(t[0]=a=>u(l).banner_bg=a),"upload-class":"bg-body","exclude-domain":""},null,8,["modelValue"]),U])]),_:1}),e(n,{label:"\u6807\u9898\u540D\u79F0"},{default:o(()=>[e(i,{modelValue:u(l).title,"onUpdate:modelValue":t[1]||(t[1]=a=>u(l).title=a)},null,8,["modelValue"])]),_:1}),e(n,{label:"\u6309\u94AE\u663E\u793A"},{default:o(()=>[e(c,{modelValue:u(l).title_color,"onUpdate:modelValue":t[2]||(t[2]=a=>u(l).title_color=a)},{default:o(()=>[e(p,{label:1},{default:o(()=>[m("\u9ED1\u8272")]),_:1}),e(p,{label:2},{default:o(()=>[m("\u767D\u8272")]),_:1}),e(p,{label:3},{default:o(()=>[m("\u4E3B\u9898\u8272")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})])}}});export{Q as _};
