import{w as g,M as h}from"./element-plus.5bcb7c8a.js";import{_ as w}from"./index.88e852a7.js";import{a as R,u as T}from"./vue-router.919c7bec.js";import{i as V,f as M}from"./index.850efb0d.js";import{_ as q}from"./form.vue_vue_type_script_setup_true_lang.7cdbac94.js";import{a as x,d as A,b as L}from"./model.63e41af8.js";import{u as N}from"./useLockFn.b2f69334.js";import{d as f,s as P,r as S,aj as $,R as l,u as o,o as c,c as j,W as i,j as U,Q as a,P as Q,U as d,T as W}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./picker.9a1dad65.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";const z=f({name:"aiModelEdit"}),Ht=f({...z,setup(G){const r=R(),e=T(),m=e.query.type,_=e.query.id,u=V(),s=P(),t=S({is_system:0,type:1,channel:"",logo:"",name:"",configs:{},is_enable:0,models:[]}),{lockFn:y,isLock:v}=N(async()=>{t.value=await L({id:e.query.id})}),F=async()=>{await s.value.validate(),await x({...t.value,type:m}),setTimeout(()=>{r.back(),u.removeTab(e.fullPath,r)})},k=async()=>{await M.confirm(`\u786E\u5B9A\u8981\u5220\u9664 ${t.value.name} \u5417\uFF1F\u5220\u9664\u540E\u5DF2\u7ECF\u9009\u62E9\u8BE5\u6A21\u578B\u7684\u7528\u6237\u5C06\u65E0\u6CD5\u6B63\u5E38\u4F7F\u7528\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C\uFF01`),await A({id:e.query.id}),r.back(),u.removeTab(e.fullPath,r)};return y(),(C,p)=>{const n=g,b=w,D=$("perms"),E=h;return l((c(),j("div",null,[i(q,{ref_key:"formRef",ref:s,modelValue:o(t),"onUpdate:modelValue":p[0]||(p[0]=B=>U(t)?t.value=B:null),"header-title":C.$route.meta.title,"current-id":o(_),type:o(m)},null,8,["modelValue","header-title","current-id","type"]),i(b,null,{default:a(()=>[o(t).is_system?W("",!0):l((c(),Q(n,{key:0,type:"danger",onClick:k},{default:a(()=>[d(" \u5220\u9664 ")]),_:1})),[[D,["setting.ai.models/del"]]]),i(n,{type:"primary",onClick:F},{default:a(()=>[d("\u4FDD\u5B58")]),_:1})]),_:1})])),[[E,o(v)]])}}});export{Ht as default};
