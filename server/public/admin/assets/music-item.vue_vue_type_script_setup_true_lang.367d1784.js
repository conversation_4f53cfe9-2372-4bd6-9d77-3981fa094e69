import{b as x}from"./index.850efb0d.js";import{s as k,r as y,G as w,d as A,o as _,c as g,a as B,V as C,K as P,u as p,W as N,T as S}from"./@vue.a11433a6.js";const v=new Set,z=t=>{const{onstart:o,onstop:n,onerror:s}=t||{},e=k(null),u=y(!1),l=y(!1),m=async(a,V=!0)=>{var d;let c;if(typeof a=="string"?c=()=>a:c=a,i(),e.value||h(),V&&((d=e.value)==null?void 0:d.src)){e.value.play();return}u.value=!0;try{const f=await Promise.resolve(c());e.value.src=f,e.value.play()}catch(f){console.error(f),s==null||s()}finally{u.value=!1}},r=()=>{var a;(a=e.value)==null||a.pause(),l.value=!1},i=()=>{v.forEach(a=>{a.pause(),a.currentTime=0,a.audioPlaying.value=!1})},h=()=>{e.value=new Audio,e.value.audioPlaying=l,v.add(e.value),e.value.onplay=()=>{o==null||o(),l.value=!0},e.value.onended=()=>{n==null||n(),l.value=!1},e.value.onerror=()=>{s==null||s(),l.value=!1}};return w(()=>{var a;(a=e.value)!=null&&a.src&&i(),e.value&&(v.delete(e.value),e.value=null)}),{play:m,audioLoading:u,audioPlaying:l,pause:r,pauseAll:i}},E={class:"line-clamp-1"},$=A({__name:"music-item",props:{name:{},url:{}},setup(t){const o=t,{pause:n,play:s,audioPlaying:e}=z(),u=()=>{!o.url||(e.value?n():s(o.url))};return(l,m)=>{const r=x;return _(),g("div",{class:P(["flex items-center cursor-pointer hover:text-primary",{"text-primary":p(e)}]),onClick:u},[B("div",E,C(l.name),1),l.url?(_(),g("span",{key:0,class:P(["flex ml-[10px]",{"!opacity-100":p(e)}])},[N(r,{name:`el-icon-${p(e)?"VideoPause":"VideoPlay"}`,size:18},null,8,["name"])],2)):S("",!0)],2)}}});export{$ as _};
