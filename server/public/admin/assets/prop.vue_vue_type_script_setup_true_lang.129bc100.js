import{Q as y,R as S,D as R,C as z,t as M,w as N,F as T}from"./element-plus.5bcb7c8a.js";import{_ as $}from"./index.4a09b22e.js";import{b as I}from"./index.850efb0d.js";import{_ as Q}from"./picker.e4cc82a9.js";import{_ as j}from"./picker.9a1dad65.js";import{m as b}from"./@vueuse.a2407f20.js";import{D as G}from"./vuedraggable.2019ddfd.js";import{d as P,o as F,c as W,W as e,Q as l,u as s,j as q,U as m,a as d,P as H}from"./@vue.a11433a6.js";const J=d("span",{class:"form-tips !text-xs"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A20*20px",-1),K={class:"bg-fill-light w-full p-4 mb-4"},L={class:"w-full"},O={class:"flex-1 flex items-center"},X={class:"drag-move cursor-move ml-auto"},ne=P({__name:"prop",props:{isShow:{type:Boolean},prop:{}},emits:["update:prop","update:isShow"],setup(h,{emit:w}){const c=h,i=w,t=b(c,"prop",i),_=b(c,"isShow",i),A=()=>{t.value.data.push({icon:"",title:"",desc:"",isShow:!0})},v=r=>{t.value.data.splice(r,1)};return(r,n)=>{const p=y,f=S,u=R,U=j,V=z,g=Q,x=M,C=I,B=$,k=N,D=T;return F(),W("div",null,[e(D,{"label-width":"70px"},{default:l(()=>[e(u,{label:"\u662F\u5426\u663E\u793A"},{default:l(()=>[e(f,{modelValue:s(_),"onUpdate:modelValue":n[0]||(n[0]=o=>q(_)?_.value=o:null),class:"ml-4"},{default:l(()=>[e(p,{label:!0},{default:l(()=>[m("\u663E\u793A")]),_:1}),e(p,{label:!1},{default:l(()=>[m("\u4E0D\u663E\u793A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),d("div",null,[e(u,{label:"\u663E\u793A\u6837\u5F0F"},{default:l(()=>[e(f,{modelValue:s(t).showType,"onUpdate:modelValue":n[1]||(n[1]=o=>s(t).showType=o),class:"ml-4"},{default:l(()=>[e(p,{label:3},{default:l(()=>[m("\u4E00\u884C3\u4E2A")]),_:1}),e(p,{label:4},{default:l(()=>[m("\u4E00\u884C4\u4E2A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u5BFC\u822A\u83DC\u5355"},{default:l(()=>[J]),_:1}),e(s(G),{class:"draggable",modelValue:s(t).data,"onUpdate:modelValue":n[2]||(n[2]=o=>s(t).data=o),animation:"300",handle:".drag-move"},{item:l(({element:o,index:E})=>[(F(),H(B,{key:E,onClose:a=>v(E)},{default:l(()=>[d("div",K,[e(u,{label:"\u5BFC\u822A\u56FE\u6807"},{default:l(()=>[e(U,{modelValue:o.icon,"onUpdate:modelValue":a=>o.icon=a,"upload-class":"bg-body",size:"60px","exclude-domain":!0},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u573A\u666F\u540D\u79F0"},{default:l(()=>[e(V,{modelValue:o.title,"onUpdate:modelValue":a=>o.title=a},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u573A\u666F\u8BF4\u660E"},{default:l(()=>[e(V,{modelValue:o.desc,"onUpdate:modelValue":a=>o.desc=a,type:"textarea",rows:4,resize:"none"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u8DF3\u8F6C\u94FE\u63A5"},{default:l(()=>[d("div",L,[e(g,{modelValue:o.link,"onUpdate:modelValue":a=>o.link=a},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1024),e(u,{class:"mt-[18px]",label:"\u662F\u5426\u663E\u793A"},{default:l(()=>[d("div",O,[e(x,{"active-value":!0,"inactive-value":!1,modelValue:o.isShow,"onUpdate:modelValue":a=>o.isShow=a},null,8,["modelValue","onUpdate:modelValue"]),d("div",X,[e(C,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])]),e(u,{"label-width":"0"},{default:l(()=>[e(k,{type:"primary",onClick:A},{default:l(()=>[m("\u6DFB\u52A0\u5E94\u7528")]),_:1})]),_:1})]),_:1})])}}});export{ne as _};
