import{_ as S}from"./index.4a09b22e.js";import{P as V}from"./index.324d704f.js";import{c as B,w as A}from"./element-plus.5bcb7c8a.js";import{b as M}from"./index.850efb0d.js";import{d as R,s as $,a4 as g,o as u,c as d,a7 as N,R as L,X as O,u as a,W as t,Q as n,a as p,V as v,U as b,F as j,bk as z,bj as Q}from"./@vue.a11433a6.js";import{u as W}from"./useMenuOa.88866e3a.js";import{_ as X}from"./oa-menu-form.vue_vue_type_script_setup_true_lang.609c965e.js";import{_ as y}from"./oa-menu-form-edit.vue_vue_type_script_setup_true_lang.e1e7307e.js";import{D as q}from"./vuedraggable.2019ddfd.js";import{_ as G}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./wx_oa.1673a390.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";const H=i=>(z("data-v-f244015c"),i=i(),Q(),i),J=H(()=>p("div",{class:"text-base oa-attr-title"},"\u83DC\u5355\u914D\u7F6E",-1)),K={class:"flex items-center w-full p-4 mt-4 rounded bg-fill-light"},Y={class:"flex-1"},Z={class:"drag-move cursor-move mr-[10px] flex"},I={class:"mr-auto"},ee=R({__name:"oa-attr",setup(i){const l=$(),{menuList:U,menuIndex:s,handleAddSubMenu:D,handleEditSubMenu:x,handleDelMenu:k,handleDelSubMenu:w}=W(l);return(oe,_)=>{const C=M,E=g("EditPen"),c=B,m=A,F=g("Delete"),T=V,P=S;return u(!0),d(j,null,N(a(U),(o,f)=>L((u(),d("div",{key:f,class:"flex-1 oa-attr min-w-0"},[J,t(P,{onClose:_[0]||(_[0]=e=>a(k)(a(s)))},{default:n(()=>[p("div",K,[t(X,{ref_for:!0,ref_key:"menuRef",ref:l,modular:"master",name:o.name,"onUpdate:name":e=>o.name=e,menuType:o.has_menu,"onUpdate:menuType":e=>o.has_menu=e,visitType:o.type,"onUpdate:visitType":e=>o.type=e,url:o.url,"onUpdate:url":e=>o.url=e,appId:o.appid,"onUpdate:appId":e=>o.appid=e,pagePath:o.pagepath,"onUpdate:pagePath":e=>o.pagepath=e},{default:n(()=>[p("div",Y,[t(a(q),{class:"draggable",modelValue:o.sub_button,"onUpdate:modelValue":e=>o.sub_button=e,animation:"300",handle:".drag-move",tag:"ul"},{item:n(({element:e,index:r})=>[(u(),d("li",{class:"flex items-center",style:{padding:"8px"},key:r},[p("div",Z,[t(C,{name:"el-icon-Rank",size:"18"})]),p("span",I,v(e.name),1),t(y,{modular:"edit",subItem:e,onEdit:h=>a(x)(h,r)},{default:n(()=>[t(m,{link:""},{default:n(()=>[t(c,null,{default:n(()=>[t(E)]),_:1})]),_:1})]),_:2},1032,["subItem","onEdit"]),t(T,{onConfirm:h=>a(w)(a(s),r)},{trigger:n(()=>[t(m,{link:""},{default:n(()=>[t(c,{class:"ml-5"},{default:n(()=>[t(F)]),_:1})]),_:1})]),default:n(()=>[b(" \u662F\u5426\u5220\u9664\u5F53\u524D\u5B50\u83DC\u5355\uFF1F ")]),_:2},1032,["onConfirm"])]))]),_:2},1032,["modelValue","onUpdate:modelValue"]),t(y,{modular:"add",onAdd:a(D)},{default:n(()=>[t(m,{type:"primary",link:"",disabled:o.sub_button.length>=5},{default:n(()=>[b(" \u6DFB\u52A0\u5B50\u83DC\u5355("+v(o.sub_button.length)+"/5) ",1)]),_:2},1032,["disabled"])]),_:2},1032,["onAdd"])])]),_:2},1032,["name","onUpdate:name","menuType","onUpdate:menuType","visitType","onUpdate:visitType","url","onUpdate:url","appId","onUpdate:appId","pagePath","onUpdate:pagePath"])])]),_:2},1024)],512)),[[O,f===a(s)]])),128)}}});const Ge=G(ee,[["__scopeId","data-v-f244015c"]]);export{Ge as default};
