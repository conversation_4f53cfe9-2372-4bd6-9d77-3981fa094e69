import{a3 as k,G as V,C as z,D as I,w as M,F as N}from"./element-plus.5bcb7c8a.js";import{u as S,c as B,b as U,f as C,t as L}from"./index.850efb0d.js";import{u as R}from"./weapp.dceef78d.js";import{u as K}from"./useLockFn.b2f69334.js";import{d as D,a0 as A,s as T,i as j,o as i,c,W as t,Q as s,a as e,u as o,U as G,P as O,T as p,K as P,V as x}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const Q=e("div",{class:"py-[3px]"},"\u6E29\u99A8\u63D0\u793A\uFF1A",-1),W=e("div",{class:"py-[3px]"}," 1.\u9996\u6B21\u4F7F\u7528\u5C0F\u7A0B\u5E8F\u4E00\u952E\u4E0A\u4F20\uFF0C\u5148\u5B89\u88C5\u597Dnode\u73AF\u5883\uFF0C\u7136\u540E\u5728\u7EC8\u7AEF\u4E0B\u4F7F\u7528cd\u547D\u4EE4\u8FDB\u5230\u9879\u76EE\u4E0B\u7684server/extend/miniprogram-ci\u76EE\u5F55\uFF0C\u8FD0\u884C\u547D\u4EE4 npm install miniprogram-ci --save \u5EFA\u8BAE\u4F7F\u7528node\u6700\u65B0\u7248\u672C\u3002 ",-1),$=e("div",{class:"py-[3px]"}," 2.\u5C0F\u7A0B\u5E8F\u4E0A\u4F20\u524D\uFF0C\u8BF7\u5148\u5230\u672C\u7CFB\u7EDF\u7684`\u5C0F\u7A0B\u5E8F\u914D\u7F6E`\u8BBE\u7F6E\u5C0F\u7A0B\u5E8F\u4EE3\u7801\u4E0A\u4F20\u5BC6\u94A5\uFF0C\u5982\u5DF2\u914D\u7F6E\uFF0C\u8BF7\u5FFD\u7565\uFF1B ",-1),q=e("div",{class:"py-[3px]"},"3.\u5C0F\u7A0B\u5E8F\u4E0A\u4F20\u6210\u529F\u540E\uFF0C\u8981\u524D\u5F80\u5C0F\u7A0B\u5E8F\u540E\u53F0\u63D0\u4EA4\u5BA1\u6838\u54E6\uFF1B",-1),H=e("div",{class:"font-medium mb-7"},"\u5C0F\u7A0B\u5E8F\u4E00\u952E\u4E0A\u4F20",-1),J={class:"w-80"},X={class:"w-80"},Y={class:"bg-page w-80 p-[20px] rounded"},Z={key:0,class:"flex items-center"},uu=e("span",{class:"font-bold ml-[10px]"}," \u4E0A\u4F20\u6210\u529F ",-1),ou={key:1,class:"flex items-center"},tu=e("span",{class:"font-bold ml-[10px]"}," \u4E0A\u4F20\u5931\u8D25 ",-1),eu={class:"text-tx-secondary text-sm"},su=D({name:"weappUpload"}),Pu=D({...su,setup(au){const d="mnp_upload_info",F=S(),n=A({upload_desc:""}),y=T(),u=A({status:-1,msg:"",time:""}),{lockFn:h,isLock:g}=K(async()=>{C.loading("\u6B63\u5728\u4E0A\u4F20\u4E2D...");try{const{code:a}=await R(n);u.status=a,a===1?u.msg="\u8BF7\u524D\u5F80\u5C0F\u7A0B\u5E8F\u540E\u53F0\u63D0\u4EA4\u5BA1\u6838\uFF01":u.msg="\u8BF7\u91CD\u65B0\u4E0A\u4F20"}catch(a){u.status=0,u.msg="\u8BF7\u91CD\u65B0\u4E0A\u4F20",console.error(a)}finally{u.time=L(Date.now(),"yyyy-mm-dd hh:MM:ss"),B.set(d,u,24*3600),C.closeLoading()}});return j(()=>{const a=B.get(d);a&&Object.assign(u,a)}),(a,r)=>{const v=k,_=V,E=z,l=I,b=M,f=U,w=N;return i(),c("div",null,[t(_,{class:"!border-none",shadow:"never"},{default:s(()=>[t(v,{class:"text-xxl",type:"error",closable:!1,"show-icon":""},{title:s(()=>[Q,W,$,q]),_:1})]),_:1}),t(w,{ref_key:"formRef",ref:y,model:o(n),"label-width":"120px"},{default:s(()=>[t(_,{class:"!border-none mt-4",shadow:"never"},{default:s(()=>[H,t(l,{label:"\u7248\u672C\u53F7",prop:"name"},{default:s(()=>[e("div",J,[t(E,{disabled:!0,modelValue:o(F).config.version,"onUpdate:modelValue":r[0]||(r[0]=m=>o(F).config.version=m)},null,8,["modelValue"])])]),_:1}),t(l,{label:"\u9879\u76EE\u5907\u6CE8",prop:"original_id"},{default:s(()=>[e("div",X,[t(E,{type:"textarea",rows:4,resize:"none",modelValue:o(n).upload_desc,"onUpdate:modelValue":r[1]||(r[1]=m=>o(n).upload_desc=m),placeholder:"\u8BF7\u8F93\u5165\u9879\u76EE\u5907\u6CE8"},null,8,["modelValue"])])]),_:1}),t(l,null,{default:s(()=>[t(b,{type:"primary",loading:o(g),onClick:o(h)},{default:s(()=>[G("\u4E0A\u4F20\u5C0F\u7A0B\u5E8F")]),_:1},8,["loading","onClick"])]),_:1}),o(u).status!==-1?(i(),O(l,{key:0},{default:s(()=>[e("div",Y,[o(u).status==1?(i(),c("div",Z,[t(f,{name:"el-icon-SuccessFilled",class:"text-success",size:18}),uu])):p("",!0),o(u).status==0?(i(),c("div",ou,[t(f,{name:"el-icon-CircleCloseFilled",class:"text-error",size:18}),tu])):p("",!0),e("span",{class:P(["msg break-words",{"text-error":o(u).status==0,"text-tx-secondary":o(u).status==1}])},x(o(u).msg),3),e("div",eu,x(o(u).time),1)])]),_:1})):p("",!0)]),_:1})]),_:1},8,["model"])])}}});export{Pu as default};
