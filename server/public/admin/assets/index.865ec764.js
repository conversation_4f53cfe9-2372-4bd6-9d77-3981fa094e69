import{_ as M,H as O,I as Q,J as W,D as X,C as Y,w as Z,F as ee,G as te,K as ae,L as oe,M as le}from"./element-plus.5bcb7c8a.js";import{_ as ne}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as se,b as ie}from"./index.850efb0d.js";import{u as ue}from"./vue-router.919c7bec.js";import{d as L,r as T,s as de,a0 as P,aj as re,o as s,c as w,W as e,Q as a,u as o,F as me,a7 as pe,P as d,a8 as ce,U as r,a as D,R as v,j as _e,T as fe,n as R}from"./@vue.a11433a6.js";import{c as ye,e as ve,f as ge}from"./dict.8ab6c66a.js";import{u as be}from"./usePaging.b48cb079.js";import{_ as Ce}from"./edit.vue_vue_type_script_setup_true_lang.9eef17b2.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const Ee={class:"dict-type"},ke={class:"mt-4"},we={class:"flex justify-end mt-4"},De=L({name:"dictData"}),ct=L({...De,setup(Fe){const{query:N}=ue(),g=T(!1),_=de(),i=P({type:"",type_id:Number(N.id),name:"",status:""}),E=P({dict_type:[]}),{pager:f,getLists:y,resetPage:F,resetParams:S}=be({fetchFun:ge,params:i}),k=T([]),U=n=>{k.value=n.map(({id:t})=>t)},A=async()=>{var t,m;g.value=!0,await R();const n=E.dict_type.find(p=>p.id==i.type_id);(t=_.value)==null||t.setFormData({type_value:n==null?void 0:n.type,type_id:n.id}),(m=_.value)==null||m.open("add")},I=async n=>{var t,m;g.value=!0,await R(),(t=_.value)==null||t.open("edit"),(m=_.value)==null||m.setFormData(n)},h=async n=>{await se.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await ye({id:n}),y()},K=async()=>{const n=await ve({page_type:0});E.dict_type=n.lists};return y(),K(),(n,t)=>{const m=M,p=Q,V=W,b=X,j=Y,c=Z,q=ee,B=te,x=ie,u=ae,$=O,H=oe,z=ne,C=re("perms"),G=le;return s(),w("div",Ee,[e(B,{class:"!border-none",shadow:"never"},{default:a(()=>[e(m,{class:"mb-4",content:"\u6570\u636E\u7BA1\u7406",onBack:t[0]||(t[0]=l=>n.$router.back())}),e(q,{ref:"formRef",class:"mb-[-16px]",model:o(i),inline:""},{default:a(()=>[e(b,{label:"\u5B57\u5178\u540D\u79F0"},{default:a(()=>[e(V,{class:"w-[280px]",modelValue:o(i).type_id,"onUpdate:modelValue":t[1]||(t[1]=l=>o(i).type_id=l),onChange:o(y)},{default:a(()=>[(s(!0),w(me,null,pe(o(E).dict_type,l=>(s(),d(p,{label:l.name,value:l.id,key:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),e(b,{label:"\u6570\u636E\u540D\u79F0"},{default:a(()=>[e(j,{class:"w-[280px]",modelValue:o(i).name,"onUpdate:modelValue":t[2]||(t[2]=l=>o(i).name=l),clearable:"",onKeyup:ce(o(F),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(b,{label:"\u6570\u636E\u72B6\u6001"},{default:a(()=>[e(V,{class:"w-[280px]",modelValue:o(i).status,"onUpdate:modelValue":t[3]||(t[3]=l=>o(i).status=l)},{default:a(()=>[e(p,{label:"\u5168\u90E8",value:""}),e(p,{label:"\u6B63\u5E38",value:1}),e(p,{label:"\u505C\u7528",value:0})]),_:1},8,["modelValue"])]),_:1}),e(b,null,{default:a(()=>[e(c,{type:"primary",onClick:o(F)},{default:a(()=>[r("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(c,{onClick:o(S)},{default:a(()=>[r("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(B,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[D("div",null,[v((s(),d(c,{type:"primary",onClick:A},{icon:a(()=>[e(x,{name:"el-icon-Plus"})]),default:a(()=>[r(" \u6DFB\u52A0\u6570\u636E ")]),_:1})),[[C,["setting.dict.dict_data/add"]]]),v((s(),d(c,{disabled:!o(k).length,type:"danger",onClick:t[4]||(t[4]=l=>h(o(k)))},{icon:a(()=>[e(x,{name:"el-icon-Delete"})]),default:a(()=>[r(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[C,["setting.dict.dict_data/delete"]]])]),v((s(),w("div",ke,[D("div",null,[e(H,{data:o(f).lists,size:"large",onSelectionChange:U},{default:a(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"ID",prop:"id"}),e(u,{label:"\u6570\u636E\u540D\u79F0",prop:"name","min-width":"120"}),e(u,{label:"\u6570\u636E\u503C",prop:"value","min-width":"120"}),e(u,{label:"\u72B6\u6001"},{default:a(({row:l})=>[l.status==1?(s(),d($,{key:0},{default:a(()=>[r("\u6B63\u5E38")]),_:1})):(s(),d($,{key:1,type:"danger"},{default:a(()=>[r("\u505C\u7528")]),_:1}))]),_:1}),e(u,{label:"\u5907\u6CE8",prop:"remark","min-width":"120","show-tooltip-when-overflow":""}),e(u,{label:"\u6392\u5E8F",prop:"sort"}),e(u,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:a(({row:l})=>[v((s(),d(c,{link:"",type:"primary",onClick:J=>I(l)},{default:a(()=>[r(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[C,["setting.dict.dict_data/edit"]]]),v((s(),d(c,{link:"",type:"danger",onClick:J=>h(l.id)},{default:a(()=>[r(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[C,["setting.dict.dict_data/delete"]]])]),_:1})]),_:1},8,["data"])]),D("div",we,[e(z,{modelValue:o(f),"onUpdate:modelValue":t[5]||(t[5]=l=>_e(f)?f.value=l:null),onChange:o(y)},null,8,["modelValue","onChange"])])])),[[G,o(f).loading]])]),_:1}),o(g)?(s(),d(Ce,{key:0,ref_key:"editRef",ref:_,onSuccess:o(y),onClose:t[6]||(t[6]=l=>g.value=!1)},null,8,["onSuccess"])):fe("",!0)])}}});export{ct as default};
