import{a7 as O,a8 as Q,O as q,H as G,w as J,G as K,C as W,D as X,I as Z,J as ee,F as te,K as oe,o as ae,L as le,M as se}from"./element-plus.5bcb7c8a.js";import{_ as ue}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{b as ie}from"./index.850efb0d.js";import{a as ne,b as de}from"./robot_edit_log.ad074937.js";import _e from"./detail.cf20575a.js";import{d as re,r as B,a0 as ce,i as me,w as pe,o as F,c as fe,W as e,Q as t,a,u as l,U as r,V as n,P as C,T as w,j as S,R as Fe,bk as ve,bj as be}from"./@vue.a11433a6.js";import{_ as he}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const v=b=>(ve("data-v-8af2b7b9"),b=b(),be(),b),Ee={class:"flex justify-between"},ye=v(()=>a("div",{class:"card-header"},[a("span",null,"\u7F16\u8F91\u65E5\u5FD7\u7EDF\u8BA1")],-1)),ge={class:"stat-item"},Be={class:"stat-value"},Ce=v(()=>a("div",{class:"stat-label"},"\u603B\u7F16\u8F91\u6B21\u6570",-1)),De={class:"stat-item"},ke={class:"stat-value"},we=v(()=>a("div",{class:"stat-label"},"\u81EA\u52A8\u4E0B\u67B6\u6B21\u6570",-1)),xe={class:"stat-item"},Ve={class:"stat-value"},Ae=v(()=>a("div",{class:"stat-label"},"\u4ECA\u65E5\u7F16\u8F91",-1)),Ie={class:"stat-item"},Re={class:"stat-value"},Se=v(()=>a("div",{class:"stat-label"},"\u672C\u5468\u7F16\u8F91",-1)),Pe={class:"mb-4"},Ye={class:"flex items-center"},Le={class:"font-medium"},Ue={class:"text-xs text-gray-500"},Me={class:"flex items-center"},je={class:"font-medium"},He={class:"text-xs text-gray-500"},Te={class:"flex justify-end mt-4"},Ne=re({name:"RobotEditLogIndex",__name:"index",setup(b){const x=B(),c=B(!1),u=ce({robot_keyword:"",user_keyword:"",edit_type:"",is_auto_offline:"",start_time:"",end_time:""}),h=B([]),{pager:m,getLists:E,resetPage:D,resetParams:$e}=usePaging({fetchFun:ne,params:u}),p=B({total_edits:0,auto_offline_count:0,today_edits:0,week_edits:0,edit_type_stats:[]}),V=async()=>{try{const i=await de();p.value=i}catch(i){console.error("\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:",i)}},P=i=>{i&&i.length===2?(u.start_time=i[0],u.end_time=i[1]):(u.start_time="",u.end_time="")},Y=()=>{Object.assign(u,{robot_keyword:"",user_keyword:"",edit_type:"",is_auto_offline:"",start_time:"",end_time:""}),h.value=[],D()},L=i=>{var s;(s=x.value)==null||s.open(i)};return me(()=>{E(),V()}),pe(c,i=>{i&&V()}),(i,s)=>{const U=ie,f=J,k=K,y=O,M=Q,A=W,_=X,g=Z,I=ee,j=q,H=te,d=oe,R=ae,T=G,N=le,$=ue,z=se;return F(),fe("div",null,[e(k,{class:"!border-none",shadow:"never"},{default:t(()=>[a("div",Ee,[a("div",null,[e(f,{type:"primary",onClick:l(D)},{icon:t(()=>[e(U,{name:"el-icon-Refresh"})]),default:t(()=>[r(" \u5237\u65B0 ")]),_:1},8,["onClick"])]),a("div",null,[e(f,{onClick:s[0]||(s[0]=o=>c.value=!l(c))},{default:t(()=>[r(n(l(c)?"\u9690\u85CF\u7EDF\u8BA1":"\u663E\u793A\u7EDF\u8BA1"),1)]),_:1})])])]),_:1}),l(c)?(F(),C(k,{key:0,class:"!border-none mt-4",shadow:"never"},{header:t(()=>[ye]),default:t(()=>[e(M,{gutter:20},{default:t(()=>[e(y,{span:6},{default:t(()=>[a("div",ge,[a("div",Be,n(l(p).total_edits),1),Ce])]),_:1}),e(y,{span:6},{default:t(()=>[a("div",De,[a("div",ke,n(l(p).auto_offline_count),1),we])]),_:1}),e(y,{span:6},{default:t(()=>[a("div",xe,[a("div",Ve,n(l(p).today_edits),1),Ae])]),_:1}),e(y,{span:6},{default:t(()=>[a("div",Ie,[a("div",Re,n(l(p).week_edits),1),Se])]),_:1})]),_:1})]),_:1})):w("",!0),e(k,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[a("div",Pe,[e(H,{model:l(u),inline:!0},{default:t(()=>[e(_,{label:"\u667A\u80FD\u4F53\u540D\u79F0"},{default:t(()=>[e(A,{modelValue:l(u).robot_keyword,"onUpdate:modelValue":s[1]||(s[1]=o=>l(u).robot_keyword=o),placeholder:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53\u540D\u79F0",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(_,{label:"\u7528\u6237"},{default:t(()=>[e(A,{modelValue:l(u).user_keyword,"onUpdate:modelValue":s[2]||(s[2]=o=>l(u).user_keyword=o),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0/\u8D26\u53F7",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(_,{label:"\u7F16\u8F91\u7C7B\u578B"},{default:t(()=>[e(I,{modelValue:l(u).edit_type,"onUpdate:modelValue":s[3]||(s[3]=o=>l(u).edit_type=o),placeholder:"\u8BF7\u9009\u62E9",clearable:""},{default:t(()=>[e(g,{label:"\u667A\u80FD\u4F53\u7F16\u8F91",value:1}),e(g,{label:"\u77E5\u8BC6\u5E93\u7F16\u8F91\u89E6\u53D1",value:2})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u662F\u5426\u81EA\u52A8\u4E0B\u67B6"},{default:t(()=>[e(I,{modelValue:l(u).is_auto_offline,"onUpdate:modelValue":s[4]||(s[4]=o=>l(u).is_auto_offline=o),placeholder:"\u8BF7\u9009\u62E9",clearable:""},{default:t(()=>[e(g,{label:"\u662F",value:1}),e(g,{label:"\u5426",value:0})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u65F6\u95F4\u8303\u56F4"},{default:t(()=>[e(j,{modelValue:l(h),"onUpdate:modelValue":s[5]||(s[5]=o=>S(h)?h.value=o:null),type:"datetimerange","range-separator":"\u81F3","start-placeholder":"\u5F00\u59CB\u65F6\u95F4","end-placeholder":"\u7ED3\u675F\u65F6\u95F4",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:P},null,8,["modelValue"])]),_:1}),e(_,null,{default:t(()=>[e(f,{type:"primary",onClick:l(D)},{default:t(()=>[r("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(f,{onClick:Y},{default:t(()=>[r("\u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),a("div",null,[Fe((F(),C(N,{data:l(m).lists,size:"large"},{default:t(()=>[e(d,{label:"ID",prop:"id",width:"80"}),e(d,{label:"\u667A\u80FD\u4F53","min-width":"200"},{default:t(({row:o})=>[a("div",Ye,[o.robot_image?(F(),C(R,{key:0,src:o.robot_image,size:40,class:"mr-2"},null,8,["src"])):w("",!0),a("div",null,[a("div",Le,n(o.robot_name||"\u5DF2\u5220\u9664"),1),a("div",Ue,"ID: "+n(o.robot_id),1)])])]),_:1}),e(d,{label:"\u64CD\u4F5C\u7528\u6237","min-width":"150"},{default:t(({row:o})=>[a("div",Me,[o.user_avatar?(F(),C(R,{key:0,src:o.user_avatar,size:32,class:"mr-2"},null,8,["src"])):w("",!0),a("div",null,[a("div",je,n(o.user_nickname||o.user_account),1),a("div",He,n(o.user_account),1)])])]),_:1}),e(d,{label:"\u7F16\u8F91\u7C7B\u578B",prop:"edit_type_text",width:"120"}),e(d,{label:"\u81EA\u52A8\u4E0B\u67B6",width:"100"},{default:t(({row:o})=>[e(T,{type:o.is_auto_offline?"danger":"success"},{default:t(()=>[r(n(o.is_auto_offline_text),1)]),_:2},1032,["type"])]),_:1}),e(d,{label:"\u4E0B\u67B6\u539F\u56E0",prop:"offline_reason","min-width":"150"}),e(d,{label:"\u7F16\u8F91\u65F6\u95F4",prop:"create_time_text",width:"160"}),e(d,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:t(({row:o})=>[e(f,{type:"primary",link:"",onClick:ze=>L(o)},{default:t(()=>[r(" \u67E5\u770B\u8BE6\u60C5 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[z,l(m).loading]])]),a("div",Te,[e($,{modelValue:l(m),"onUpdate:modelValue":s[6]||(s[6]=o=>S(m)?m.value=o:null),onChange:l(E)},null,8,["modelValue","onChange"])])]),_:1}),e(_e,{ref_key:"detailPopupRef",ref:x,onSuccess:l(E),onClose:l(E)},null,8,["onSuccess","onClose"])])}}});const It=he(Ne,[["__scopeId","data-v-8af2b7b9"]]);export{It as default};
