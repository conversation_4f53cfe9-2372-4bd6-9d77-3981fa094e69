import{D as b,C as w,t as D,F as y}from"./element-plus.5bcb7c8a.js";import{_ as k}from"./picker.9a1dad65.js";import{a as x,e as R}from"./problem_category.9c1e9165.js";import{P as h}from"./index.324d704f.js";import"./index.850efb0d.js";import{d as A,s as c,r as f,o as U,c as P,W as a,Q as m,u as t,a as _}from"./@vue.a11433a6.js";const Q={class:"edit-popup"},I=_("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u503C\u8D8A\u5927\u6392\u8D8A\u524D\u9762",-1),G=A({__name:"edit",emits:["success"],setup(N,{expose:v,emit:F}){const V=F,p=c(),r=c(),i=f(""),e=f({id:"",name:"",sort:0,status:"",image:""}),B={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:["blur"]}]},C=async()=>{var s,u;try{await((s=p.value)==null?void 0:s.validate()),e.value.id==""?await x(e.value):e.value.id!=""&&await R(e.value),(u=r.value)==null||u.close(),V("success")}catch(n){return n}};return v({open:(s,u)=>{var n;s=="add"?(e.value={id:"",name:"",sort:"0",status:1},i.value="\u65B0\u589E\u5206\u7C7B"):s=="edit"&&(Object.keys(e.value).map(l=>{e.value[l]=u[l]}),i.value="\u7F16\u8F91\u5206\u7C7B"),(n=r.value)==null||n.open()}}),(s,u)=>{const n=k,l=b,d=w,g=D,E=y;return U(),P("div",Q,[a(h,{ref_key:"popupRef",ref:r,title:t(i),async:!0,width:"550px",onConfirm:C},{default:m(()=>[a(E,{class:"ls-form",ref_key:"formRef",ref:p,rules:B,model:t(e),"label-width":"90px"},{default:m(()=>[a(l,{label:"\u5206\u7C7B\u56FE\u6807",prop:"image"},{default:m(()=>[a(n,{modelValue:t(e).image,"onUpdate:modelValue":u[0]||(u[0]=o=>t(e).image=o)},null,8,["modelValue"])]),_:1}),a(l,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name"},{default:m(()=>[a(d,{class:"ls-input",modelValue:t(e).name,"onUpdate:modelValue":u[1]||(u[1]=o=>t(e).name=o),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:""},null,8,["modelValue"])]),_:1}),a(l,{label:"\u6392\u5E8F"},{default:m(()=>[_("div",null,[a(d,{class:"ls-input",modelValue:t(e).sort,"onUpdate:modelValue":u[2]||(u[2]=o=>t(e).sort=o)},null,8,["modelValue"]),I])]),_:1}),a(l,{label:"\u72B6\u6001"},{default:m(()=>[a(g,{modelValue:t(e).status,"onUpdate:modelValue":u[3]||(u[3]=o=>t(e).status=o),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{G as _};
