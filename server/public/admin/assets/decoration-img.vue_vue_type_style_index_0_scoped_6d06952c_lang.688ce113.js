import{i as p,b as _}from"./element-plus.5bcb7c8a.js";import{u as m,n as s,b as u}from"./index.850efb0d.js";import{d as l,b as g,o as h,P as f,Q as o,a,W as b,M as y,u as S,bk as v,bj as I}from"./@vue.a11433a6.js";const w=e=>(v("data-v-6d06952c"),e=e(),I(),e),N=w(()=>a("div",{class:"image-slot"},null,-1)),P={class:"image-slot"},U=l({__name:"decoration-img",props:{width:{type:[String,Number],default:"auto"},height:{type:[String,Number],default:"auto"},radius:{type:[String,Number],default:0},...p},setup(e){const t=e,{getImageUrl:r}=m(),c=g(()=>({width:s(t.width),height:s(t.height),borderRadius:s(t.radius)}));return(i,k)=>{const n=u,d=_;return h(),f(d,y({style:c.value},t,{src:S(r)(i.src)}),{placeholder:o(()=>[N]),error:o(()=>[a("div",P,[b(n,{name:"el-icon-Picture",size:30})])]),_:1},16,["style","src"])}}});export{U as _};
