import{V as D,W as j,P as $}from"./element-plus.5bcb7c8a.js";import{f as h,e as N}from"./index.850efb0d.js";import{d as O,s as I,r as v,b as U,w as q,o as d,c as p,W as F,a9 as W,a7 as E,Q as y,J as z,ab as J,ac as M,M as Q,u,j as w,P as T,a as f,F as G,V as H,T as K}from"./@vue.a11433a6.js";const X={class:"upload"},Y={class:"file-list p-4"},Z={class:"flex-1"},ae=O({__name:"index",props:{files:{type:Array,default:()=>[]},type:{type:String,default:"image"},multiple:{type:Boolean,default:!0},limit:{type:Number,default:10},data:{type:Object,default:()=>({})},name:{type:String,default:"file"},header:{type:Object,default:()=>({})},showFileList:{type:Boolean,default:!1}},emits:["end","change","error","success","update:files"],setup(i,{emit:b}){const l=i,n=b,g=I(),r=v(!1),s=v([]),_=()=>{r.value=!0},m=e=>{n("change",e),s.value.length==0&&n("end")},x=(e,t)=>{m(t),n("success",e),n("update:files",[...l.files,{url:e.uri,name:e.name}]);const a=s.value.indexOf(t);!l.showFileList&&s.value.splice(a,1)},A=e=>{const t=s.value.indexOf(e),a=l.files;a.splice(t,1),n("update:files",[...a])},B=(e,t)=>{var a;m(t),h.msgError(`${t.name}\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25`),(a=g.value)==null||a.abort(t),r.value=!1,n("error",t)},k=()=>{h.msgError(`\u8D85\u51FA\u4E0A\u4F20\u4E0A\u9650${l.limit}\uFF0C\u8BF7\u91CD\u65B0\u4E0A\u4F20`)},C=()=>{s.value=[],r.value=!1},P=U(()=>{switch(l.type){case"image":return".jpg,.png,.gif,.jpeg";case"video":return".wmv,.avi,.mpg,.mpeg,.3gp,.mov,.mp4,.flv,.rmvb,.mkv";case"audio":return"*";default:return"*"}}),V=e=>N(l.type,{file:e.file,name:l.name,header:l.header,data:l.data},t=>{const a=t;a.percent=t.total>0?t.loaded/t.total*100:0,e.onProgress(a)});return q(()=>l.files,e=>{!s.value.length&&(e==null?void 0:e.length)&&(s.value=[...e])},{immediate:!0}),(e,t)=>{const a=D,L=j,R=$;return d(),p("div",X,[F(a,Q({"file-list":u(s),"onUpdate:fileList":t[0]||(t[0]=o=>w(s)?s.value=o:null),ref_key:"uploadRefs",ref:g},e.$attrs,{multiple:i.multiple,limit:i.limit,"show-file-list":i.showFileList,"on-progress":_,"on-success":x,"on-exceed":k,"on-error":B,accept:u(P),"on-change":m,"on-remove":A,"http-request":V}),W({_:2},[E(e.$slots,(o,c)=>({name:c,fn:y(S=>[z(e.$slots,c,J(M(S)))])}))]),1040,["file-list","multiple","limit","show-file-list","accept"]),!i.showFileList&&u(s).length?(d(),T(R,{key:0,modelValue:u(r),"onUpdate:modelValue":t[1]||(t[1]=o=>w(r)?r.value=o:null),title:"\u4E0A\u4F20\u8FDB\u5EA6","close-on-click-modal":!1,width:"500px",modal:!1,onClose:C},{default:y(()=>[f("div",Y,[(d(!0),p(G,null,E(u(s),(o,c)=>(d(),p("div",{key:c,class:"mb-5"},[f("div",null,H(o.name),1),f("div",Z,[F(L,{percentage:parseInt(String(o.percentage))},null,8,["percentage"])])]))),128))])]),_:1},8,["modelValue"])):K("",!0)])}}});export{ae as _};
