import{Q as H,R as S,D as $,C as M,t as N,w as z,F as I}from"./element-plus.5bcb7c8a.js";import{_ as Q}from"./index.4a09b22e.js";import{f as F,b as j}from"./index.850efb0d.js";import{_ as G}from"./picker.e4cc82a9.js";import{_ as P}from"./picker.9a1dad65.js";import{m as E}from"./@vueuse.a2407f20.js";import{D as T}from"./vuedraggable.2019ddfd.js";import{d as W,o as g,c as q,W as e,Q as o,u as m,j as J,U as _,a as d,P as K}from"./@vue.a11433a6.js";const L={class:"flex-1"},O=d("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A80*80px",-1),X={class:"bg-fill-light w-full p-4 mt-4"},Y={class:"flex-1 flex items-center"},Z={class:"drag-move cursor-move ml-auto"},c=999,de=W({__name:"attr",props:{isHidden:{type:Boolean},content:{}},emits:["update:isHidden"],setup(b,{emit:v}){const p=b,r=v,i=E(p,"isHidden",r),n=E(p,"content",r),h=()=>{var s;((s=n.value.data)==null?void 0:s.length)<c?n.value.data.push({image:"",title:"",link:{},isShow:!0}):F.msgError(`\u6700\u591A\u6DFB\u52A0${c}\u4E2A`)},A=s=>{var t;if(((t=n.value.data)==null?void 0:t.length)<=1)return F.msgError("\u6700\u5C11\u4FDD\u7559\u4E00\u4E2A");n.value.data.splice(s,1)};return(s,t)=>{const f=H,x=S,u=$,D=P,k=M,B=G,w=N,U=j,C=Q,y=z,R=I;return g(),q("div",null,[e(R,{"label-width":"70px"},{default:o(()=>[e(u,{label:"\u662F\u5426\u663E\u793A"},{default:o(()=>[e(x,{modelValue:m(i),"onUpdate:modelValue":t[0]||(t[0]=l=>J(i)?i.value=l:null)},{default:o(()=>[e(f,{label:!1},{default:o(()=>[_("\u663E\u793A")]),_:1}),e(f,{label:!0},{default:o(()=>[_("\u4E0D\u663E\u793A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u83DC\u5355\u8BBE\u7F6E"},{default:o(()=>[d("div",L,[O,e(m(T),{class:"draggable",modelValue:m(n).data,"onUpdate:modelValue":t[1]||(t[1]=l=>m(n).data=l),animation:"300",handle:".drag-move"},{item:o(({element:l,index:V})=>[(g(),K(C,{key:V,onClose:a=>A(V),class:"max-w-[400px]"},{default:o(()=>[d("div",X,[e(u,{label:"\u529F\u80FD\u56FE\u6807"},{default:o(()=>[d("div",null,[e(D,{modelValue:l.image,"onUpdate:modelValue":a=>l.image=a,"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1024),e(u,{label:"\u529F\u80FD\u6807\u9898",class:"mt-[18px]"},{default:o(()=>[e(k,{modelValue:l.title,"onUpdate:modelValue":a=>l.title=a,placeholder:"\u8BF7\u8F93\u5165\u529F\u80FD\u6807\u9898"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u94FE\u63A5\u5730\u5740",class:"mt-[18px]"},{default:o(()=>[e(B,{type:"mobile",modelValue:l.link,"onUpdate:modelValue":a=>l.link=a},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{class:"mt-[18px]",label:"\u662F\u5426\u663E\u793A"},{default:o(()=>[d("div",Y,[e(w,{"active-value":!0,"inactive-value":!1,modelValue:l.isShow,"onUpdate:modelValue":a=>l.isShow=a},null,8,["modelValue","onUpdate:modelValue"]),d("div",Z,[e(U,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])])]),_:1}),e(u,null,{default:o(()=>{var l;return[e(y,{disabled:((l=s.content.data)==null?void 0:l.length)>=c,type:"primary",onClick:h},{default:o(()=>[_(" \u6DFB\u52A0\u529F\u80FD ")]),_:1},8,["disabled"])]}),_:1})]),_:1})])}}});export{de as _};
