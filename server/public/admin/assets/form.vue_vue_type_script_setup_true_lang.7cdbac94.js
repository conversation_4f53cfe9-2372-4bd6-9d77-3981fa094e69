import{_ as ce,a1 as pe,a2 as _e,G as fe,D as Fe,I as ve,J as he,C as K,c as Be,a as Ee,t as ge,K as Ve,w as be,L as ye,F as Ae}from"./element-plus.5bcb7c8a.js";import{f as W,b as xe}from"./index.850efb0d.js";import{_ as Ce}from"./picker.9a1dad65.js";import{g as ke}from"./model.63e41af8.js";import{S as De}from"./sortablejs.98edf555.js";import{d as we,s as M,b as k,r as X,w as Y,i as Ue,a4 as Ie,o as i,c as p,W as e,Q as o,a as d,F as y,a7 as N,P as A,U as D,V as w,T as B,u as E,n as Me}from"./@vue.a11433a6.js";const Ne=d("div",{class:"text-xl font-medium mb-[20px]"},"\u57FA\u7840\u914D\u7F6E",-1),Se={class:"w-[460px]"},qe=d("div",{class:"form-tips"}," \u901A\u9053\u7684\u610F\u601D\u662F:\u6BD4\u5982\u9009OpenAI,\u5373(\u6A21\u578B\u914D\u7F6E)\u7684\u6A21\u578B\u9700\u8981\u7B26\u5408OpenAI\u63A5\u53E3\u53C2\u6570\u89C4\u8303\u3002 ",-1),Re={key:0,class:"form-tips"},Te=["href"],Oe={class:"w-[460px]"},$e={class:"w-[460px]"},Le={class:"w-[460px]"},Qe={key:0,class:"flex flex-wrap"},Pe={class:"flex items-center text-tx-regular text-xs"},je={class:"mr-[4px] mt-[2px]"},ze={class:"form-tips"},Ge={class:"form-tips"},He={class:"text-xl font-medium mb-[20px]"},Je={class:"inline-flex text-tx-secondary"},Ke={class:"flex-1 min-w-0 max-w-[1000px]"},We={class:""},Xe={class:"move-icon cursor-move"},Ye={class:"flex mt-4"},Ze={class:"w-[460px]"},eu={class:"w-[460px]"},uu=d("div",{class:"form-tips"}," \u586B\u5199\u524D\u7AEF\u5C31\u663E\u793A\u8BE5\u5B57\u6BB5\uFF0C\u4E0D\u586B\u5199\u9ED8\u8BA4\u663E\u793AAI\u6A21\u578B\u540D\u79F0 ",-1),lu={class:"w-[460px]"},au=d("div",{class:"form-tips"},"\u586B\u51990\u8868\u793A\u4E0D\u6D88\u8017\u7535\u529B\u503C",-1),cu=we({__name:"form",props:{modelValue:{type:Object,required:!0},type:{type:String},currentId:{type:[Number,String]},headerTitle:{type:String}},emits:["update:modelValue"],setup(C,{expose:Z,emit:ee}){const f=C,ue=ee,S=M(),l=k({get(){return f.modelValue},set(n){ue("update:modelValue",n)}}),q=M(),_=X({alias:"",name:"",status:1,price:""});Y(_,n=>{Number(f.type)!=1&&(l.value.models=[n])},{deep:!0}),Y(l,n=>{const[a]=n.models;a&&(_.value=a)},{immediate:!0});const le={logo:[{required:!0,message:"\u8BF7\u9009\u62E9\u56FE\u6807"}],channel:[{required:!0,message:"\u8BF7\u9009\u62E9AI\u901A\u9053"}],name:[{required:!0,message:"\u8BF7\u8F93\u5165AI\u540D\u79F0"}]},R=M(),ae=()=>{const n=R.value.$el.querySelector(".el-table__body tbody");De.create(n,{animation:150,handle:".move-icon",onEnd:({newIndex:a,oldIndex:v})=>{const m=l.value.models,h=m.splice(v,1)[0];m.splice(a,0,h),l.value.models=[],Me(()=>{l.value.models=m,l.value.models.forEach((r,U)=>{r.sort=U})})}})},te=()=>{l.value.models.push({name:"",alias:"",status:1,price:"",sort:l.value.models.length})},oe=n=>n.id&&n.status==1?W.confirm("\u8BE5\u6A21\u578B\u53EF\u80FD\u5DF2\u7ECF\u88AB\u4F7F\u7528\uFF0C\u7981\u7528\u540E\u5DF2\u7ECF\u9009\u62E9\u8BE5\u6A21\u578B\u7684\u7528\u6237\u5C06\u65E0\u6CD5\u6B63\u5E38\u4F7F\u7528\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C\uFF01"):!0,se=async(n,a)=>{a&&await W.confirm("\u8BE5\u6A21\u578B\u53EF\u80FD\u5DF2\u7ECF\u88AB\u4F7F\u7528\uFF0C\u5220\u9664\u540E\u5DF2\u7ECF\u9009\u62E9\u8BE5\u6A21\u578B\u7684\u7528\u6237\u5C06\u65E0\u6CD5\u6B63\u5E38\u4F7F\u7528\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C\uFF01"),l.value.models.splice(n,1)},F=X({chatModels:{},exampleModels:{chat:[],vector:[]},rankingModels:{},vectorModels:{},vlModels:{}}),T=k(()=>{if(Number(f.type)===1)return F.value.chatModels;if(Number(f.type)===2)return F.value.vectorModels;if(Number(f.type)===10)return F.value.vlModels;if(Number(f.type)===11)return F.value.rankingModels}),O=k(()=>Number(f.type)===1?F.value.exampleModels.chat:F.value.exampleModels.vector),$=(n,a)=>{const v=n?O.value.filter(m=>m.toLowerCase().includes(n.toLowerCase())):O.value;a(v.map(m=>({value:m})))},x=k(()=>T.value[l.value.channel]),ne=()=>{const n=v=>{const m={};return v.reduce((h,r)=>(r.type=="group"?Object.assign(h,n(r.config)):h[r.key]=r.default,h),m),m},a=n(x.value.configs);l.value.configs=a};return(async()=>{F.value=await ke()})(),Ue(()=>{setTimeout(()=>{Number(f.type)==1&&ae()},100)}),Z({validate:()=>{var n,a;return Promise.all([(n=S.value)==null?void 0:n.validate(),(a=q.value)==null?void 0:a.validate()])}}),(n,a)=>{const v=ce,m=fe,h=Ce,r=Fe,U=ve,de=he,g=K,re=Ie("QuestionFilled"),ie=Be,L=Ee,Q=pe,I=ge,P=xe,V=Ve,j=_e,z=K,G=be,me=ye,H=Ae;return i(),p("div",null,[e(m,{class:"!border-none",shadow:"never"},{default:o(()=>[e(v,{content:C.headerTitle,onBack:a[0]||(a[0]=s=>n.$router.back())},null,8,["content"])]),_:1}),e(H,{class:"mt-4",ref_key:"formRef",ref:S,model:l.value,"label-width":"120px",rules:le},{default:o(()=>[e(m,{shadow:"never",class:"!border-none"},{default:o(()=>{var s;return[Ne,e(r,{label:"\u56FE\u6807",prop:"logo"},{default:o(()=>[d("div",null,[e(h,{modelValue:l.value.logo,"onUpdate:modelValue":a[1]||(a[1]=u=>l.value.logo=u),limit:1},null,8,["modelValue"])])]),_:1}),e(r,{label:"AI\u901A\u9053",prop:"channel"},{default:o(()=>{var u,t,c;return[d("div",Se,[e(de,{class:"w-full",modelValue:l.value.channel,"onUpdate:modelValue":a[2]||(a[2]=b=>l.value.channel=b),disabled:!!C.currentId,placeholder:"\u8BF7\u9009\u62E9AI\u901A\u9053",onChange:ne},{default:o(()=>[(i(!0),p(y,null,N(T.value,(b,J)=>(i(),A(U,{value:J,label:b.name,key:J},null,8,["value","label"]))),128))]),_:1},8,["modelValue","disabled"]),qe,(u=x.value)!=null&&u.website?(i(),p("div",Re,[D(" \u5F00\u901A\u7F51\u5740\uFF1A"+w((t=x.value)==null?void 0:t.website)+" ",1),d("a",{class:"text-primary",target:"_blank",href:(c=x.value)==null?void 0:c.website},"\u524D\u5F80\u5F00\u901A",8,Te)])):B("",!0)])]}),_:1}),e(r,{label:"AI\u540D\u79F0",prop:"name"},{default:o(()=>[d("div",Oe,[e(g,{modelValue:l.value.name,"onUpdate:modelValue":a[3]||(a[3]=u=>l.value.name=u),modelModifiers:{trim:!0},placeholder:"\u8BF7\u8F93\u5165AI\u540D\u79F0",maxlength:"30","show-word-limit":""},null,8,["modelValue"])])]),_:1}),e(r,{label:"\u63CF\u8FF0",prop:"remarks"},{default:o(()=>[d("div",$e,[e(g,{modelValue:l.value.remarks,"onUpdate:modelValue":a[4]||(a[4]=u=>l.value.remarks=u),modelModifiers:{trim:!0},placeholder:"\u8BF7\u7528\u4E00\u53E5\u7B80\u77ED\u7684\u8BDD\u63CF\u8FF0\u4E00\u4E0B",type:"textarea",rows:4},null,8,["modelValue"])])]),_:1}),(i(!0),p(y,null,N((s=x.value)==null?void 0:s.configs,u=>(i(),A(r,{label:u.name,prop:u.key,key:u.key,rules:u.require?[{required:u.require,trigger:"change",validator(t,c,b){u.key&&!c?b(new Error(u.placeholder)):b()}}]:[]},{default:o(()=>[d("div",Le,[u.type==="group"?(i(),p("div",Qe,[(i(!0),p(y,null,N(u.config,t=>(i(),p("div",{class:"w-[50%] pr-[20px] pb-[20px]",key:t.key},[d("div",Pe,[d("span",je,w(t.name),1),e(L,{class:"box-item",effect:"dark",content:t.tips,placement:"top"},{default:o(()=>[e(ie,{size:"16px"},{default:o(()=>[e(re)]),_:1})]),_:2},1032,["content"])]),t.type==="slider"?(i(),A(Q,{key:0,modelValue:l.value.configs[t.key],"onUpdate:modelValue":c=>l.value.configs[t.key]=c,min:t.range[0],max:t.range[1],step:t.step},null,8,["modelValue","onUpdate:modelValue","min","max","step"])):B("",!0),t.type==="switch"?(i(),A(I,{key:1,modelValue:l.value.configs[t.key],"onUpdate:modelValue":c=>l.value.configs[t.key]=c},null,8,["modelValue","onUpdate:modelValue"])):B("",!0),t.type==="slider-input"?(i(),A(Q,{key:2,"show-input":"",modelValue:l.value.configs[t.key],"onUpdate:modelValue":c=>l.value.configs[t.key]=c,min:t.range[0],max:t.range[1],step:t.step},null,8,["modelValue","onUpdate:modelValue","min","max","step"])):B("",!0)]))),128))])):B("",!0),u.type==="input"?(i(),p(y,{key:1},[e(g,{modelValue:l.value.configs[u.key],"onUpdate:modelValue":t=>l.value.configs[u.key]=t,modelModifiers:{trim:!0},placeholder:u.placeholder||"\u8BF7\u8F93\u5165"},null,8,["modelValue","onUpdate:modelValue","placeholder"]),d("div",ze,w(u.tips),1)],64)):B("",!0),u.type==="textarea"?(i(),p(y,{key:2},[e(g,{modelValue:l.value.configs[u.key],"onUpdate:modelValue":t=>l.value.configs[u.key]=t,modelModifiers:{trim:!0},placeholder:u.placeholder||"\u8BF7\u8F93\u5165",type:"textarea",rows:4},null,8,["modelValue","onUpdate:modelValue","placeholder"]),d("div",Ge,w(u.tips),1)],64)):B("",!0)])]),_:2},1032,["label","prop","rules"]))),128)),e(r,{label:"\u662F\u5426\u542F\u7528",prop:"is_enable"},{default:o(()=>[e(I,{modelValue:l.value.is_enable,"onUpdate:modelValue":a[5]||(a[5]=u=>l.value.is_enable=u),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]}),_:1}),e(m,{shadow:"never",class:"!border-none mt-4"},{default:o(()=>[d("div",He,[D(" \u6A21\u578B\u914D\u7F6E "),e(L,{content:"\u6A21\u578B\u53EF\u81EA\u5B9A\u4E49\u6DFB\u52A0\uFF0C\u70B9\u51FB\u4E0B\u65B9\u7684\u65B0\u589E\u6309\u94AE",placement:"top",effect:"light",teleported:!0},{default:o(()=>[d("span",Je,[e(P,{name:"el-icon-QuestionFilled"})])]),_:1})]),d("div",Ke,[Number(C.type)==1?(i(),p(y,{key:0},[d("div",We,[e(me,{ref_key:"tableRef",ref:R,size:"large",class:"mt-4","row-key":"id",data:l.value.models},{default:o(()=>[e(V,{width:"50"},{default:o(()=>[d("div",Xe,[e(P,{name:"el-icon-Rank"})])]),_:1}),e(V,{label:"\u6A21\u578B\u540D\u79F0","min-width":"120"},{default:o(({row:s})=>[e(j,{class:"w-full",modelValue:s.name,"onUpdate:modelValue":u=>s.name=u,modelModifiers:{trim:!0},placeholder:"\u8BF7\u8F93\u5165AI\u6A21\u578B",clearable:"","fetch-suggestions":$},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(V,{label:"\u81EA\u5B9A\u4E49\u540D\u79F0","min-width":"120"},{default:o(({row:s})=>[e(z,{modelValue:s.alias,"onUpdate:modelValue":u=>s.alias=u,placeholder:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(V,{label:"1000\u5B57\u7B26\u6D88\u8017\u7535\u529B\u503C","min-width":"120"},{default:o(({row:s})=>[e(z,{modelValue:s.price,"onUpdate:modelValue":u=>s.price=u,type:"number",placeholder:"\u4E3A\u7A7A\u4E0D\u6D88\u8017\u7535\u529B\u503C"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(V,{label:"\u72B6\u6001","min-width":"100"},{default:o(({row:s})=>[e(I,{modelValue:s.status,"onUpdate:modelValue":u=>s.status=u,"before-change":()=>oe(s),"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","before-change"])]),_:1}),e(V,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:o(({row:s,$index:u})=>[e(G,{type:"danger",link:"",onClick:t=>se(u,s.id)},{default:o(()=>[D(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),d("div",Ye,[e(G,{type:"primary",onClick:te},{default:o(()=>[D("+ \u65B0\u589E")]),_:1})])],64)):(i(),A(H,{key:1,ref_key:"formRef1",ref:q,model:E(_),"label-width":"120px"},{default:o(()=>[e(r,{label:"AI\u6A21\u578B",rules:[{required:!0,message:"\u8BF7\u8F93\u5165AI\u6A21\u578B"}],prop:"name"},{default:o(()=>[d("div",Ze,[e(j,{class:"w-full",modelValue:E(_).name,"onUpdate:modelValue":a[6]||(a[6]=s=>E(_).name=s),modelModifiers:{trim:!0},placeholder:"\u8BF7\u8F93\u5165AI\u6A21\u578B",clearable:"","fetch-suggestions":$},null,8,["modelValue"])])]),_:1}),e(r,{label:"\u81EA\u5B9A\u4E49\u522B\u540D",prop:"alias"},{default:o(()=>[d("div",eu,[e(g,{modelValue:E(_).alias,"onUpdate:modelValue":a[7]||(a[7]=s=>E(_).alias=s),modelModifiers:{trim:!0},placeholder:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49\u522B\u540D",maxlength:"30","show-word-limit":""},null,8,["modelValue"]),uu])]),_:1}),e(r,{label:"\u6D88\u8017\u7535\u529B\u503C",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u6D88\u8017\u7535\u529B\u503C"}],prop:"price"},{default:o(()=>[d("div",lu,[e(g,{modelValue:E(_).price,"onUpdate:modelValue":a[8]||(a[8]=s=>E(_).price=s),modelModifiers:{trim:!0},type:"number"},null,8,["modelValue"]),au])]),_:1})]),_:1},8,["model"]))])]),_:1})]),_:1},8,["model"])])}}});export{cu as _};
