import{x as Q,y as W,C as H,D as X,I as Y,J as Z,w as ee,F as te,G as ae,K as oe,t as le,L as ne,M as ue}from"./element-plus.5bcb7c8a.js";import{_ as se}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{d as S,s as re,a0 as ie,r as w,aj as me,o as r,c as D,W as e,Q as o,u as a,a8 as pe,U as i,j as q,F as de,a7 as ce,R as d,P as c,V as _e,a as fe,T as Ce,n as x}from"./@vue.a11433a6.js";import{u as ye}from"./usePaging.b48cb079.js";import{_ as be}from"./edit.vue_vue_type_script_setup_true_lang.b85089a1.js";import{e as ve,f as ge,h as Ee}from"./ai_square.bf3e05a5.js";import{f as Fe}from"./index.850efb0d.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const he={class:"flex justify-end mt-4"},we=S({name:"aiSquareCate"}),ct=S({...we,setup(Ve){const C=re(),u=ie({name:"",status:"",type:""}),y=w(!1),g=w([]),A=[{name:"\u5168\u90E8",type:"",alis:"all_count"},{name:"AI\u7ED8\u753B",type:1,alis:"draw_count"},{name:"AI\u97F3\u4E50",type:2,alis:"music_count"},{name:"AI\u89C6\u9891",type:3,alis:"video_count"}],T={1:"AI\u7ED8\u753B",2:"AI\u97F3\u4E50",3:"AI\u89C6\u9891"},E=w(""),I=n=>{g.value=n.map(l=>l.id)},$=n=>{u.type=n,_()},U=async()=>{var n;y.value=!0,await x(),(n=C.value)==null||n.open("add")},P=async n=>{var l,b;y.value=!0,await x(),(l=C.value)==null||l.open("edit"),(b=C.value)==null||b.setFormData(n)},V=async n=>{await Fe.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await ve({id:n}),_()},R=n=>{ge({id:n})},{pager:m,getLists:_,resetPage:k,resetParams:K}=ye({fetchFun:Ee,params:u});return _(),(n,l)=>{const b=H,F=X,h=Y,L=Z,p=ee,N=te,B=ae,j=Q,M=W,s=oe,z=le,G=ne,J=se,f=me("perms"),O=ue;return r(),D("div",null,[e(B,{shadow:"never",class:"!border-none mt-4"},{default:o(()=>[e(N,{ref:"formRef",class:"mb-[-16px]",model:a(u),inline:!0},{default:o(()=>[e(F,{label:"\u5206\u7C7B\u540D\u79F0"},{default:o(()=>[e(b,{class:"w-[280px]",modelValue:a(u).name,"onUpdate:modelValue":l[0]||(l[0]=t=>a(u).name=t),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:"",onKeyup:pe(a(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(F,{label:"\u5206\u7C7B\u72B6\u6001"},{default:o(()=>[e(L,{class:"!w-[280px]",modelValue:a(u).status,"onUpdate:modelValue":l[1]||(l[1]=t=>a(u).status=t)},{default:o(()=>[e(h,{label:"\u5168\u90E8",value:""}),e(h,{label:"\u5F00\u542F",value:"1"}),e(h,{label:"\u5173\u95ED",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(F,null,{default:o(()=>[e(p,{type:"primary",onClick:a(k)},{default:o(()=>[i("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(p,{onClick:a(K)},{default:o(()=>[i("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(B,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[e(M,{modelValue:a(E),"onUpdate:modelValue":l[2]||(l[2]=t=>q(E)?E.value=t:null),onTabChange:$},{default:o(()=>[(r(),D(de,null,ce(A,t=>e(j,{label:t.name+`(${a(m).extend[t.alis]})`,name:t.type,index:t.type,key:t.type},null,8,["label","name","index"])),64))]),_:1},8,["modelValue"]),d((r(),c(p,{type:"primary",class:"mb-4",onClick:U},{default:o(()=>[i(" + \u65B0\u589E ")]),_:1})),[[f,["square.squareCategory/add"]]]),d((r(),c(p,{class:"mb-4",onClick:l[3]||(l[3]=t=>V(a(g))),disabled:!a(g).length},{default:o(()=>[i(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["square.squareCategory/del"]]]),d((r(),c(G,{size:"large",data:a(m).lists,onSelectionChange:I},{default:o(()=>[e(s,{type:"selection",width:"55"}),e(s,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name","min-width":"120"}),e(s,{label:"\u6240\u5C5E\u5E94\u7528",prop:"name","min-width":"120"},{default:o(({row:t})=>[i(_e(T[t.type]),1)]),_:1}),e(s,{label:"\u72B6\u6001","min-width":"100"},{default:o(({row:t})=>[d(e(z,{onChange:v=>R(t.id),modelValue:t.status,"onUpdate:modelValue":v=>t.status=v,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"]),[[f,["square.squareCategory/status"]]])]),_:1}),e(s,{label:"\u5DF2\u88AB\u5173\u8054","min-width":"100",prop:"relevance_num"}),e(s,{label:"\u6392\u5E8F",prop:"sort","min-width":"100"}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time",sortable:"","min-width":"100"}),e(s,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:o(({row:t})=>[d((r(),c(p,{type:"primary",link:"",onClick:v=>P(t)},{default:o(()=>[i(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[f,["square.squareCategory/edit"]]]),d((r(),c(p,{type:"danger",link:"",onClick:v=>V([t.id])},{default:o(()=>[i(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["square.squareCategory/del"]]])]),_:1})]),_:1},8,["data"])),[[O,a(m).loading]]),fe("div",he,[e(J,{modelValue:a(m),"onUpdate:modelValue":l[4]||(l[4]=t=>q(m)?m.value=t:null),onChange:a(_)},null,8,["modelValue","onChange"])])]),_:1}),a(y)?(r(),c(be,{key:0,ref_key:"editRef",ref:C,type:a(u).type||1,onSuccess:a(_),onClose:l[5]||(l[5]=t=>y.value=!1)},null,8,["type","onSuccess"])):Ce("",!0)])}}});export{ct as default};
