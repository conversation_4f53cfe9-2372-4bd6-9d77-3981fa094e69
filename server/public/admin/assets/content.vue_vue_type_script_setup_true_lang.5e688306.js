import r from"./decoration-img.16e6b284.js";import{d as c,b as t,o as i,c as d,a as p,W as m}from"./@vue.a11433a6.js";const _={class:"index-banner px-[15px] pt-[10px]"},l={class:"banner-image rounded-[10px] overflow-hidden"},g=c({__name:"content",props:{isHidden:{type:Boolean},content:{}},emits:["update:isHidden"],setup(o,{emit:u}){const n=o,a=t(()=>{const e=s.value;return Array.isArray(e)&&e[0]?e[0].image:""}),s=t(()=>n.content.data.filter(e=>e.isShow));return(e,h)=>(i(),d("div",_,[p("div",l,[m(r,{width:"100%",height:"140px",src:a.value,fit:"cover"},null,8,["src"])])]))}});export{g as _};
