import{D as L,C as h,I as S,J as R,t as U,F as N}from"./element-plus.5bcb7c8a.js";import{_ as O}from"./picker.9a1dad65.js";import{g as I,e as J,a as P}from"./draw_lora.39493e7e.js";import{P as q}from"./index.324d704f.js";import{u as Q}from"./useDictOptions.583d6eb9.js";import{d as T,s as v,r as W,b as j,a0 as z,o as p,c as E,W as a,Q as n,u as l,a as c,F as G,a7 as H,P as K}from"./@vue.a11433a6.js";const M={class:"edit-popup"},X={class:"w-[380px]"},Y=c("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u503C\u8D8A\u5927\u6392\u8D8A\u524D\u9762",-1),ne=T({__name:"edit",emits:["success","close"],setup(Z,{expose:B,emit:V}){const _=V,f=v(),i=v(),d=W("add"),A=j(()=>d.value=="edit"?"\u7F16\u8F91\u5FAE\u8C03\u6A21\u578B":"\u65B0\u589E\u5FAE\u8C03\u6A21\u578B"),o=z({id:"",title:"",model_name:"",cover:"",sort:0,status:1}),D={cover:[{required:!0,message:"\u8BF7\u4E0A\u4F20\u6A21\u578B\u5C01\u9762",trigger:["blur","change"]}],model_name:[{required:!0,message:"\u8BF7\u9009\u62E9\u6A21\u578B\u6587\u4EF6",trigger:["blur","change"]}]},{optionsData:F}=Q({loraList:{api:I,transformData(t){return t}}}),b=async()=>{var e,r;await((e=f.value)==null?void 0:e.validate());const t=JSON.parse(JSON.stringify(o));if(!t.title){const s=F.loraList.find(m=>m.name==t.model_name);s&&(t.title=s.alias)}d.value=="edit"?await J(t):await P(t),(r=i.value)==null||r.close(),_("success")},g=()=>{_("close")};return B({open:(t="add")=>{var e;d.value=t,(e=i.value)==null||e.open()},setFormData:async t=>{for(const e in o)t[e]!=null&&t[e]!=null&&(o[e]=t[e])}}),(t,e)=>{const r=O,s=L,m=h,C=S,w=R,k=U,y=N;return p(),E("div",M,[a(q,{ref_key:"popupRef",ref:i,title:l(A),async:!0,width:"550px",onConfirm:b,onClose:g},{default:n(()=>[a(y,{class:"ls-form",ref_key:"formRef",ref:f,rules:D,model:l(o),"label-width":"90px"},{default:n(()=>[a(s,{label:"\u6A21\u578B\u5C01\u9762",prop:"cover"},{default:n(()=>[a(r,{modelValue:l(o).cover,"onUpdate:modelValue":e[0]||(e[0]=u=>l(o).cover=u),limit:1},null,8,["modelValue"])]),_:1}),a(s,{label:"\u6A21\u578B\u540D\u79F0",prop:"title"},{default:n(()=>[a(m,{class:"ls-input",modelValue:l(o).title,"onUpdate:modelValue":e[1]||(e[1]=u=>l(o).title=u),placeholder:"\u4E0D\u586B\u5199\u5219\u4F7F\u7528\u9ED8\u8BA4\u540D\u79F0",clearable:""},null,8,["modelValue"])]),_:1}),a(s,{label:"\u6A21\u578B\u6587\u4EF6",prop:"model_name"},{default:n(()=>[c("div",X,[a(w,{modelValue:l(o).model_name,"onUpdate:modelValue":e[2]||(e[2]=u=>l(o).model_name=u),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B\u6587\u4EF6",class:"w-full"},{default:n(()=>[(p(!0),E(G,null,H(l(F).loraList,(u,x)=>(p(),K(C,{key:x,label:u.name,value:u.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),a(s,{label:"\u6392\u5E8F",prop:"sort"},{default:n(()=>[c("div",null,[a(m,{class:"ls-input",modelValue:l(o).sort,"onUpdate:modelValue":e[3]||(e[3]=u=>l(o).sort=u)},null,8,["modelValue"]),Y])]),_:1}),a(s,{label:"\u72B6\u6001",prop:"status"},{default:n(()=>[a(k,{modelValue:l(o).status,"onUpdate:modelValue":e[4]||(e[4]=u=>l(o).status=u),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{ne as _};
