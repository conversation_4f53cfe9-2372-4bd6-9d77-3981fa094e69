import{b as p}from"./element-plus.5bcb7c8a.js";import u from"./decoration-img.16e6b284.js";import{d as x,o as n,c as o,a as e,V as i,F as d,a7 as c,W as l,T as r,L as _}from"./@vue.a11433a6.js";const h={class:"index-menu px-[15px] pt-[15px]"},f={class:"text-xl font-medium mb-[15px]"},v={class:"flex items-center"},y=e("div",{class:"text-xl font-medium ml-[10px]"},"\u521B\u4F5C\u540D\u79F0",-1),g=e("div",{class:"mt-[9px] text-[#999] line-clamp-3"},"\u8FD9\u91CC\u662F\u521B\u4F5C\u63CF\u8FF0",-1),F=e("div",{class:"flex justify-end"},[e("div",{class:"text-tx-secondary text-sm"},"\u{1F525} 0")],-1),w={class:"flex items-center"},C={class:"flex-none"},k={class:"text-xl font-medium ml-[10px] line-clamp-2"},B={class:"flex-1 mt-[9px] text-[#999] line-clamp-3"},b={class:"flex justify-end"},D={class:"text-tx-secondary text-sm"},I=x({__name:"content",props:{isHidden:{type:Boolean},content:{}},emits:["update:isHidden"],setup(N,{emit:T}){return(t,V)=>{const m=p;return n(),o("div",h,[e("div",f,i(t.content.title),1),e("div",{class:"grid gap-4",style:_({"grid-template-columns":`repeat(${t.content.showType}, minmax(0, 1fr))`})},[t.content.dataType==1?(n(!0),o(d,{key:0},c(t.content.dataNum,(s,a)=>(n(),o("div",{class:"rounded-[5px] p-[15px] bg-white overflow-hidden",key:a},[e("div",v,[l(u,{radius:"50%",width:"40px",height:"40px"}),y]),g,F]))),128)):r("",!0),t.content.dataType==2?(n(!0),o(d,{key:1},c(t.content.data,(s,a)=>(n(),o("div",{class:"flex flex-col min-h-0 rounded-[5px] p-[15px] bg-white overflow-hidden",key:a},[e("div",w,[e("div",C,[l(m,{src:s.image,class:"w-[40px] h-[40px] rounded-[50%]"},null,8,["src"])]),e("div",k,i(s.name),1)]),e("div",B,i(s.tips),1),e("div",b,[e("div",D,"\u{1F525} "+i(s.all_use_count),1)])]))),128)):r("",!0)],4)])}}});export{I as _};
