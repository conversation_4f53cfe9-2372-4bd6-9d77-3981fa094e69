import{w as c}from"./element-plus.5bcb7c8a.js";import{_ as n}from"./index_arrow-right02.3a32185e.js";import{u as l}from"./index.850efb0d.js";import{d,o as r,c as i,a as t,V as p,W as _,Q as u,T as x,u as m,L as f,bk as h,bj as g}from"./@vue.a11433a6.js";import{_ as v}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const w=e=>(h("data-v-ddd07c54"),e=e(),g(),e),b={class:"w-[1160px] mx-auto title"},y={class:"flex justify-between items-center"},I={class:"flex flex-col items-stretch max-w-[610px] pt-[80px] pb-[40px]"},S={class:"font-medium text-[45px]"},B={class:"max-w-[850px] text-left text-lg mt-[40px]"},k={key:0,class:"mt-[40px] flex"},V=w(()=>t("div",{class:"flex justify-center items-center w-[50px] h-[50px] rounded-full bg-white"},[t("img",{src:n,class:"w-[24px] h-[24px]",alt:""})],-1)),j={class:"ml-4"},C=["src"],E=d({__name:"content",props:{prop:{}},setup(e){const{getImageUrl:s}=l();return(o,N)=>{const a=c;return r(),i("div",{class:"bg-center bg-cover",style:f({backgroundImage:`url(${m(s)(o.prop.bgImage)})`})},[t("div",b,[t("div",y,[t("div",I,[t("h1",S,p(o.prop.title),1),t("p",B,p(o.prop.desc),1),o.prop.isShowBtn?(r(),i("div",k,[t("div",null,[_(a,{type:"primary",class:"enter-btn",size:"large"},{default:u(()=>[V,t("span",j,p(o.prop.btnText),1)]),_:1})])])):x("",!0)]),t("div",null,[t("img",{src:m(s)(o.prop.rightImage),class:"w-[600px]",alt:""},null,8,C)])])])],4)}}});const xt=v(E,[["__scopeId","data-v-ddd07c54"]]);export{xt as default};
