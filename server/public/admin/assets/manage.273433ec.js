import{r as s}from"./index.850efb0d.js";function t(l){return s.get({url:"/skill.skill/lists",params:l},{ignoreCancelToken:!0})}function e(l){return s.get({url:"/skill.skill/detail",params:l})}function r(l){return s.post({url:"/skill.skill/add",params:l})}function n(l){return s.post({url:"/skill.skill/edit",params:l})}function u(l){return s.post({url:"/skill.skill/del",params:l})}function o(l){return s.post({url:"/skill.skill/status",params:l})}export{r as a,t as b,o as c,u as d,n as e,e as s};
