import{X as ee,C as le,D as ae,I as te,J as ue,w as oe,F as ne,G as se,K as ie,b as re,o as de,t as me,L as pe,B as ce,P as _e,M as fe}from"./element-plus.5bcb7c8a.js";import ve from"./popup.6359a000.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{d as ke,r as p,s as Fe,a0 as Ee,i as ge,a4 as Ce,aj as ye,o as c,c as Be,W as l,Q as a,a8 as K,u as r,a as u,U as d,R as b,P as k,V as C,T as R,j as he,n as Ve}from"./@vue.a11433a6.js";import{u as xe}from"./usePaging.b48cb079.js";import{k as De,a as we,b as Pe,t as Ue}from"./manage.86089de9.js";import{f as Te}from"./index.850efb0d.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./consumer.e39aaadf.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const Ae={class:"mt-4"},Ke={class:"flex items-center"},Re=u("span",{class:"mr-4"},"\u5934\u50CF: ",-1),Ie={class:"mt-[20px]"},Le=u("span",{class:"mr-4"}," \u6635\u79F0: ",-1),Se={class:"mt-[20px]"},Ne=u("span",{class:"mr-4"},"\u7F16\u53F7: ",-1),$e={class:"flex justify-end mt-4"},je=u("div",{class:"w-full text-left"},[u("div",{class:"text-lg font-medium"},"\u6210\u5458\u5904\u7406\u65B9\u5F0F")],-1),ze={class:"flex justify-end"},Tl=ke({__name:"index",setup(Me){const y=p(!1),x=Fe(),D=p(""),s=p({name:"",is_enable:"",user:""}),_=p(!1),f=p("all"),B=p(!1),w=Ee({id:-1}),{pager:v,getLists:F,resetPage:h,resetParams:I}=xe({fetchFun:De,params:s.value}),L=async n=>{await Te.customConfirm("\u786E\u5B9A\u5220\u9664 "," \u77E5\u8BC6\u5E93\u5417\uFF1F",n.name,"color:red"),await we({id:n.id}),F()},S=async n=>{await Pe({id:n})},N=async n=>{var t;D.value=n.id,y.value=!0,await Ve(),(t=x.value)==null||t.open()},$=async n=>{w.id=n.id,y.value=!1,_.value=!0},j=async()=>{B.value=!0;try{await Ue({id:D.value,user_id:w.id,type:f.value}),F(),_.value=!1}finally{B.value=!1}};return ge(()=>{F()}),(n,t)=>{const P=le,E=ae,V=te,z=ue,m=oe,M=ne,U=se,i=ie,O=re,q=de,G=ee,J=me,Q=Ce("RouterLink"),W=pe,X=be,H=ve,T=ce,Y=_e,g=ye("perms"),Z=fe;return c(),Be("div",null,[l(U,{class:"!border-none",shadow:"never"},{default:a(()=>[l(M,{ref:"formRef",class:"mb-[-16px]",model:s.value,inline:!0},{default:a(()=>[l(E,{label:"\u77E5\u8BC6\u5E93\u540D\u79F0"},{default:a(()=>[l(P,{class:"w-[280px]",modelValue:s.value.name,"onUpdate:modelValue":t[0]||(t[0]=e=>s.value.name=e),placeholder:"\u8BF7\u8F93\u5165\u77E5\u8BC6\u5E93\u540D\u79F0",clearable:"",onKeyup:K(r(h),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),l(E,{label:"\u521B\u5EFA\u4EBA"},{default:a(()=>[u("div",null,[l(P,{class:"w-[300px]",modelValue:s.value.user,"onUpdate:modelValue":t[1]||(t[1]=e=>s.value.user=e),placeholder:"\u8BF7\u8F93\u5165\u521B\u5EFA\u4EBA\u6635\u79F0/\u7528\u6237\u7F16\u53F7",clearable:"",onKeyup:K(r(h),["enter"])},null,8,["modelValue","onKeyup"])])]),_:1}),l(E,{label:"\u72B6\u6001"},{default:a(()=>[l(z,{class:"w-[280px]",modelValue:s.value.is_enable,"onUpdate:modelValue":t[2]||(t[2]=e=>s.value.is_enable=e)},{default:a(()=>[l(V,{label:"\u5168\u90E8",value:""}),l(V,{label:"\u5F00\u542F",value:1}),l(V,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),l(E,null,{default:a(()=>[l(m,{type:"primary",onClick:r(h)},{default:a(()=>[d("\u67E5\u8BE2")]),_:1},8,["onClick"]),l(m,{onClick:r(I)},{default:a(()=>[d("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),l(U,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[u("div",Ae,[b((c(),k(W,{size:"large",data:r(v).lists},{default:a(()=>[l(i,{label:"ID",prop:"id",width:"80"}),l(i,{label:"\u5C01\u9762",width:"100"},{default:a(({row:e})=>[l(O,{class:"w-[60px] h-[60px]",src:e.image},null,8,["src"])]),_:1}),l(i,{label:"\u77E5\u8BC6\u5E93\u540D\u79F0",prop:"name","min-width":"160"}),l(i,{label:"\u77E5\u8BC6\u5E93\u521B\u5EFA\u8005","min-width":"180"},{default:a(({row:e})=>[u("div",null,C(e.create_user),1)]),_:1}),l(i,{label:"\u5F53\u524D\u6240\u6709\u8005","min-width":"180"},{default:a(({row:e})=>[l(G,{placement:"right",width:"220px",trigger:"hover"},{reference:a(()=>{var o;return[d(C((o=e==null?void 0:e.user)==null?void 0:o.nickname),1)]}),default:a(()=>{var o,A;return[u("div",Ke,[Re,l(q,{size:50,src:(o=e==null?void 0:e.user)==null?void 0:o.avatar},null,8,["src"])]),u("div",Ie,[Le,u("span",null,C((A=e==null?void 0:e.user)==null?void 0:A.nickname),1)]),u("div",Se,[Ne,u("span",null,C(e==null?void 0:e.user.sn),1)])]}),_:2},1024)]),_:1}),l(i,{label:"\u72B6\u6001","min-width":"120"},{default:a(({row:e})=>[b(l(J,{modelValue:e.is_enable,"onUpdate:modelValue":o=>e.is_enable=o,"active-value":1,"inactive-value":0,onChange:o=>S(e.id)},null,8,["modelValue","onUpdate:modelValue","onChange"]),[[g,["kb.know/changeStatus"]]])]),_:1}),l(i,{label:"\u6700\u65B0\u66F4\u65B0\u65F6\u95F4",prop:"create_time","min-width":"150"}),l(i,{label:"\u64CD\u4F5C",prop:"id","min-width":"280",fixed:"right"},{default:a(({row:e})=>[e.create_type!=2?b((c(),k(m,{key:0,type:"primary",link:""},{default:a(()=>[l(Q,{to:{path:"/knowledge_base/knowledge_base/study_data",query:{id:e.id,name:e.name}}},{default:a(()=>[d(" \u6570\u636E\u5B66\u4E60 ")]),_:2},1032,["to"])]),_:2},1024)),[[g,["kb.know/files"]]]):R("",!0),b((c(),k(m,{type:"primary",link:"",onClick:o=>N(e)},{default:a(()=>[d(" \u8F6C\u79FB\u6240\u6709\u6743 ")]),_:2},1032,["onClick"])),[[g,["kb.know/del"]]]),b((c(),k(m,{type:"danger",link:"",onClick:o=>L(e)},{default:a(()=>[d(" \u5220\u9664")]),_:2},1032,["onClick"])),[[g,["kb.know/del"]]])]),_:1})]),_:1},8,["data"])),[[Z,r(v).loading]]),u("div",$e,[l(X,{modelValue:r(v),"onUpdate:modelValue":t[3]||(t[3]=e=>he(v)?v.value=e:null),onChange:r(F)},null,8,["modelValue","onChange"])])])]),_:1}),y.value?(c(),k(H,{key:0,ref_key:"userPickerRef",ref:x,type:"single",maxNum:1,disabled:!1,onClose:$},null,512)):R("",!0),l(Y,{modelValue:_.value,"onUpdate:modelValue":t[7]||(t[7]=e=>_.value=e),width:"450px",class:"!rounded-[12px]",center:"",draggable:"","destroy-on-close":"","close-on-click-modal":"false"},{header:a(()=>[je]),footer:a(()=>[u("div",ze,[l(m,{onClick:t[6]||(t[6]=e=>_.value=!1)},{default:a(()=>[d("\u53D6\u6D88")]),_:1}),l(m,{type:"primary",loading:B.value,onClick:j},{default:a(()=>[d("\u786E\u8BA4 ")]),_:1},8,["loading"])])]),default:a(()=>[u("div",null,[l(T,{modelValue:f.value,"onUpdate:modelValue":t[4]||(t[4]=e=>f.value=e),"true-label":"all",label:"\u540C\u6B65\u8F6C\u79FB\u6240\u6709\u8005\u7684\u6240\u6709\u6210\u5458\u3002",size:"large"},null,8,["modelValue"]),l(T,{modelValue:f.value,"onUpdate:modelValue":t[5]||(t[5]=e=>f.value=e),"true-label":"kb",label:"\u4E0D\u540C\u6B65\u8F6C\u79FB\u6240\u6709\u8005\u7684\u6240\u6709\u6210\u5458\uFF0C\u53EA\u8F6C\u79FB\u77E5\u8BC6\u5E93\u3002",size:"large"},null,8,["modelValue"])])]),_:1},8,["modelValue"])])}}});export{Tl as default};
