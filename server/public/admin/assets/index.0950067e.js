import{C as j,D as I,I as q,J as z,w as G,F as J,G as M,K as O,t as W,L as H,M as X}from"./element-plus.5bcb7c8a.js";import{_ as Y}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as Z,b as ee}from"./index.850efb0d.js";import{d as D,s as te,r as w,a0 as ae,aj as oe,o as s,c as k,W as e,Q as a,u as o,a8 as le,F as ne,a7 as ue,P as m,U as c,a as x,R as C,j as se,T as ie}from"./@vue.a11433a6.js";import{u as re}from"./usePaging.b48cb079.js";import{_ as me,d as pe,e as de,g as ce}from"./edit.vue_vue_type_script_setup_true_lang.9a27ea40.js";import{g as _e}from"./problem_category.9c1e9165.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const fe={class:"flex justify-end mt-4"},ge=D({name:"problemExample"}),nt=D({...ge,setup(ve){const b=te(),S=w(!0),n=ae({content:"",category_id:"",status:""}),B=w([]),L=async()=>{const{lists:u}=await _e();B.value=u},F=(u,l={})=>{var f;(f=b.value)==null||f.open(u,l)},P=async u=>{await Z.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await pe({id:u}),_()},U=u=>{de({id:u})},{pager:p,getLists:_,resetPage:y,resetParams:$}=re({fetchFun:ce,params:n});return _(),L(),(u,l)=>{const f=j,g=I,v=q,h=z,d=G,A=J,V=M,R=ee,i=O,Q=W,K=H,N=Y,E=oe("perms"),T=X;return s(),k("div",null,[e(V,{class:"!border-none",shadow:"never"},{default:a(()=>[e(A,{ref:"formRef",class:"mb-[-16px]",model:o(n),inline:!0},{default:a(()=>[e(g,{label:"\u5185\u5BB9\u641C\u7D22"},{default:a(()=>[e(f,{class:"w-[280px]",modelValue:o(n).content,"onUpdate:modelValue":l[0]||(l[0]=t=>o(n).content=t),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9\u5173\u952E\u8BCD",clearable:"",onKeyup:le(o(y),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(g,{label:"\u793A\u4F8B\u7C7B\u76EE"},{default:a(()=>[e(h,{class:"w-[280px]",modelValue:o(n).category_id,"onUpdate:modelValue":l[1]||(l[1]=t=>o(n).category_id=t)},{default:a(()=>[(s(!0),k(ne,null,ue(o(B),(t,r)=>(s(),m(v,{key:r,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"\u793A\u4F8B\u72B6\u6001"},{default:a(()=>[e(h,{class:"w-[280px]",modelValue:o(n).status,"onUpdate:modelValue":l[2]||(l[2]=t=>o(n).status=t)},{default:a(()=>[e(v,{label:"\u5168\u90E8",value:""}),e(v,{label:"\u5F00\u542F",value:1}),e(v,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:a(()=>[e(d,{type:"primary",onClick:o(y)},{default:a(()=>[c("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(d,{onClick:o($)},{default:a(()=>[c("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(V,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[x("div",null,[C((s(),m(d,{type:"primary",onClick:l[3]||(l[3]=t=>F("add"))},{icon:a(()=>[e(R,{name:"el-icon-Plus"})]),default:a(()=>[c(" \u65B0\u589E ")]),_:1})),[[E,["chat.chat_sample/add"]]])]),C((s(),m(K,{size:"large",class:"mt-4",data:o(p).lists},{default:a(()=>[e(i,{label:"\u793A\u4F8B\u7C7B\u76EE",prop:"category_name","min-width":"120"}),e(i,{label:"\u793A\u4F8B\u5185\u5BB9",prop:"content","min-width":"100"}),e(i,{label:"\u72B6\u6001","min-width":"100"},{default:a(({row:t})=>[e(Q,{modelValue:t.status,"onUpdate:modelValue":r=>t.status=r,"active-value":1,"inactive-value":0,onChange:r=>U(t.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(i,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(i,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:a(({row:t})=>[C((s(),m(d,{type:"primary",link:"",onClick:r=>F("edit",t)},{default:a(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[E,["chat.chat_sample/edit"]]]),C((s(),m(d,{type:"danger",link:"",onClick:r=>P(t.id)},{default:a(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[E,["chat.chat_sample/del"]]])]),_:1})]),_:1},8,["data"])),[[T,o(p).loading]]),x("div",fe,[e(N,{modelValue:o(p),"onUpdate:modelValue":l[4]||(l[4]=t=>se(p)?p.value=t:null),onChange:o(_)},null,8,["modelValue","onChange"])])]),_:1}),o(S)?(s(),m(me,{key:0,ref_key:"editRef",ref:b,onSuccess:o(_)},null,8,["onSuccess"])):ie("",!0)])}}});export{nt as default};
