import{H as X,C as Y,D as Z,I as ee,J as te,w as le,F as ae,G as oe,K as ue,L as se,M as ne}from"./element-plus.5bcb7c8a.js";import{_ as ie}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{P as re}from"./index.324d704f.js";import{f as C,_ as pe}from"./index.850efb0d.js";import{_ as me}from"./index.vue_vue_type_script_setup_true_lang.67cfcc66.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang.1e869df8.js";import{d as K,a0 as ce,s as _e,r as R,aj as fe,o as s,c as F,W as t,Q as l,u as a,a8 as A,F as E,a7 as Fe,U as p,a as n,R as P,P as d,T as U,V as m,j as ve}from"./@vue.a11433a6.js";import{d as $,a as be,b as he}from"./draw_records.b4956e4b.js";import{u as ge}from"./usePaging.b48cb079.js";import{_ as ye}from"./auditPop.vue_vue_type_script_setup_true_lang.cff594d5.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const ke={class:"mb-4"},Ce={class:"flex items-center overflow-hidden"},Ee={class:"ml-4 line-clamp-2"},De={class:"line-clamp-2 cursor-pointer"},we=n("div",{class:"mt-[20px]"},"\u7528\u6237\u8F93\u5165\u7FFB\u8BD1\uFF1A",-1),xe={class:"mt-[6px]"},Be={class:"flex items-center"},Ve={key:0},Te=["onClick"],Re=["onClick"],Ae={class:"flex justify-end mt-4"},Pe=K({name:"dalleRecord"}),Et=K({...Pe,setup(Ue){const u=ce({type:"",status:"",user_info:"",prompt:"",model:"dalle3",start_time:"",end_time:""}),D=_e(),L=R([]),g=R([]),S=[{label:"\u6587\u751F\u56FE",value:1},{label:"\u56FE\u751F\u56FE",value:2}],{pager:c,getLists:y,resetPage:k,resetParams:I}=ge({fetchFun:$,params:u}),z=async()=>{try{L.value=await be()}catch(r){console.log("\u83B7\u53D6\u7ED8\u753B\u6A21\u578B",r)}},M=r=>{g.value=r},N=r=>{D.value.open(r)},w=async r=>{await C.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await he({ids:r}),await C.msgSuccess("\u5220\u9664\u6210\u529F"),y()};return y(),z(),(r,o)=>{const x=Y,_=Z,f=ee,B=te,j=de,b=le,q=me,G=ae,V=oe,i=ue,T=pe,H=re,h=X,J=se,O=ie,Q=fe("perms"),W=ne;return s(),F("div",null,[t(V,{class:"!border-none",shadow:"never"},{default:l(()=>[t(G,{ref:"formRef",class:"mb-[-16px]",model:a(u),inline:!0},{default:l(()=>[t(_,{class:"w-[280px]",label:"\u7528\u6237\u4FE1\u606F"},{default:l(()=>[t(x,{modelValue:a(u).user_info,"onUpdate:modelValue":o[0]||(o[0]=e=>a(u).user_info=e),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237ID/\u7528\u6237\u6635\u79F0",clearable:"",onKeyup:A(a(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(_,{class:"w-[280px]",label:"\u63D0\u793A\u8BCD"},{default:l(()=>[t(x,{modelValue:a(u).prompt,"onUpdate:modelValue":o[1]||(o[1]=e=>a(u).prompt=e),placeholder:"\u8BF7\u8F93\u5165\u63D0\u793A\u8BCD",clearable:"",onKeyup:A(a(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(_,{class:"w-[280px]",label:"\u7ED8\u753B\u7C7B\u578B"},{default:l(()=>[t(B,{modelValue:a(u).type,"onUpdate:modelValue":o[2]||(o[2]=e=>a(u).type=e)},{default:l(()=>[t(f,{label:"\u5168\u90E8",value:""}),(s(),F(E,null,Fe(S,(e,v)=>t(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(_,{class:"w-[280px]",label:"\u751F\u6210\u7ED3\u679C"},{default:l(()=>[t(B,{modelValue:a(u).status,"onUpdate:modelValue":o[3]||(o[3]=e=>a(u).status=e)},{default:l(()=>[t(f,{label:"\u5168\u90E8",value:""}),t(f,{label:"\u751F\u6210\u4E2D",value:1}),t(f,{label:"\u751F\u6210\u5931\u8D25",value:2}),t(f,{label:"\u751F\u6210\u6210\u529F",value:3})]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"\u751F\u6210\u65F6\u95F4"},{default:l(()=>[t(j,{startTime:a(u).start_time,"onUpdate:startTime":o[4]||(o[4]=e=>a(u).start_time=e),endTime:a(u).end_time,"onUpdate:endTime":o[5]||(o[5]=e=>a(u).end_time=e)},null,8,["startTime","endTime"])]),_:1}),t(_,null,{default:l(()=>[t(b,{type:"primary",onClick:a(k)},{default:l(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),t(b,{onClick:a(I)},{default:l(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"]),t(q,{class:"ml-2.5","fetch-fun":a($),params:a(u),"page-size":a(c).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),t(V,{class:"!border-none mt-4",shadow:"never"},{default:l(()=>[n("div",ke,[t(b,{type:"default",plain:!0,disabled:!a(g).length,onClick:o[6]||(o[6]=e=>w(a(g).map(v=>v.id)))},{default:l(()=>[p(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])]),P((s(),d(J,{size:"large",data:a(c).lists,onSelectionChange:M},{default:l(()=>[t(i,{type:"selection",width:"55"}),t(i,{label:"ID",prop:"id","min-width":"80"}),t(i,{label:"\u7528\u6237\u4FE1\u606F","min-width":"150"},{default:l(({row:e})=>[n("div",Ce,[e.avatar?(s(),d(T,{key:0,class:"flex-none",src:e.avatar,width:48,height:48,"preview-src-list":[e.avatar],"preview-teleported":!0,"hide-on-click-modal":!0,fit:"contain"},null,8,["src","preview-src-list"])):U("",!0),n("div",Ee,m(e.nickname),1)])]),_:1}),t(i,{label:"\u7528\u6237\u8F93\u5165",prop:"prompt","min-width":"280"},{default:l(({row:e})=>[t(H,{ref:"popRef",title:"\u7528\u6237\u8F93\u5165",width:"700px",clickModalClose:"",cancelButtonText:"\u53D6\u6D88",confirmButtonText:"\u786E\u5B9A"},{trigger:l(()=>[n("div",De,m(e.prompt),1)]),default:l(()=>[n("div",null,m(e.prompt),1),e.prompt_en?(s(),F(E,{key:0},[we,n("div",xe,m(e.prompt_en),1)],64)):U("",!0)]),_:2},1536)]),_:1}),t(i,{label:"\u751F\u6210\u7ED3\u679C","min-width":"160"},{default:l(({row:e})=>[n("div",Be,[e.status==1?(s(),F("span",Ve,"\u751F\u6210\u4E2D...")):e.status==3?(s(),d(T,{key:1,class:"flex-none",src:e.thumbnail,width:64,height:64,"preview-src-list":[e.image],"preview-teleported":!0,"hide-on-click-modal":!0,fit:"contain"},null,8,["src","preview-src-list"])):(s(),F("span",{key:2,class:"text-error cursor-pointer",onClick:v=>a(C).alert("\u751F\u6210\u5931\u8D25: "+e.fail_reason,"\u5931\u8D25\u539F\u56E0")}," \u751F\u6210\u5931\u8D25 ",8,Te))])]),_:1}),t(i,{label:"\u6D88\u8017\u7535\u529B\u503C",prop:"use_tokens","min-width":"120"}),t(i,{label:"\u56FE\u7247\u5BA1\u6838","min-width":"180"},{default:l(({row:e})=>[n("div",null,[e.censor_status==1?(s(),d(h,{key:0,class:"mr-2",type:"success"},{default:l(()=>[p(m(e.censor_status_text),1)]),_:2},1024)):e.censor_status>=2?(s(),F(E,{key:1},[t(h,{class:"mr-2 cursor-pointer",type:"danger"},{default:l(()=>[p(m(e.censor_status_text),1)]),_:2},1024),n("span",{class:"text-error text-sm cursor-pointer",onClick:v=>N(e)}," \u67E5\u770B\u539F\u56E0 ",8,Re)],64)):e.censor_status==0?(s(),d(h,{key:2,class:"mr-2",type:"warning"},{default:l(()=>[p(m(e.censor_status_text),1)]),_:2},1024)):(s(),d(h,{key:3,class:"mr-2",type:"danger"},{default:l(()=>[p(m(e.censor_status_text),1)]),_:2},1024))])]),_:1}),t(i,{label:"\u751F\u6210\u65F6\u95F4",prop:"create_time","min-width":"180","show-tooltip-when-overflow":""}),t(i,{label:"\u8BF7\u6C42ip",prop:"ip","min-width":"140"}),t(i,{label:"\u64CD\u4F5C","min-width":"180",fixed:"right"},{default:l(({row:e})=>[P((s(),d(b,{type:"danger",link:"",onClick:v=>w([e.id])},{default:l(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[Q,["draw.draw_records/delete"]]])]),_:1})]),_:1},8,["data"])),[[W,a(c).loading]]),n("div",Ae,[t(O,{modelValue:a(c),"onUpdate:modelValue":o[7]||(o[7]=e=>ve(c)?c.value=e:null),onChange:a(y)},null,8,["modelValue","onChange"])])]),_:1}),t(ye,{ref_key:"auditRef",ref:D},null,512)])}}});export{Et as default};
