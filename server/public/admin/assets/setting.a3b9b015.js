import{_ as x}from"./index.88e852a7.js";import{t as y,D as C,C as V,F as b,G as h,w as k}from"./element-plus.5bcb7c8a.js";import{a as D,e as A}from"./share.de0c1776.js";import{f as S}from"./index.850efb0d.js";import{d as R,r as d,a0 as U,aj as N,o as _,c as I,W as o,Q as r,u as a,a as u,R as j,P as q,U as G}from"./@vue.a11433a6.js";import"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const P=u("div",{class:"text-xl font-medium mb-[20px]"},"\u5206\u4EAB\u5956\u52B1",-1),Q={class:"flex"},T=u("div",{class:"ml-[10px]"},"\u7535\u529B\u503C",-1),W={class:"flex"},$=u("div",{class:"ml-[10px]"},"\u6B21\u6709\u5956\u52B1",-1),De=R({__name:"setting",setup(z){const m=d(),e=d({is_open:1,one_award:1,day_num:5}),c=U({day_num:[{required:!0,message:"\u8BF7\u8F93\u5165\u6BCF\u5929\u6700\u591A\u5206\u4EAB\u51E0\u6B21\u6570\u6709\u5956\u52B1",trigger:"blur"}]}),s=async()=>{e.value=await D()};s();const f=async i=>{if(!i){console.log(i);return}try{await i.validate(),e.value.one_award>0?(await A(e.value),await s()):S.msgError("\u7535\u529B\u503C\u5FC5\u987B\u5927\u4E8E0")}catch(t){console.log(t)}};return(i,t)=>{const v=y,n=C,p=V,B=b,F=h,w=k,g=x,E=N("perms");return _(),I("div",null,[o(F,{shadow:"never",class:"!border-none"},{default:r(()=>[P,o(B,{ref_key:"ruleFormRef",ref:m,rules:a(c),model:a(e),"label-width":"120px"},{default:r(()=>[o(n,{label:"\u662F\u5426\u5F00\u542F"},{default:r(()=>[o(v,{modelValue:a(e).is_open,"onUpdate:modelValue":t[0]||(t[0]=l=>a(e).is_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),o(n,{label:"\u5206\u4EAB\u4E00\u6B21\u5956\u52B1",prop:"one_award"},{default:r(()=>[u("div",null,[u("div",Q,[u("div",null,[o(p,{placeholder:"\u8BF7\u8F93\u5165",modelValue:a(e).one_award,"onUpdate:modelValue":t[1]||(t[1]=l=>a(e).one_award=l)},null,8,["modelValue"])]),T])])]),_:1}),o(n,{label:"\u6BCF\u5929\u6700\u591A\u5206\u4EAB",prop:"day_num"},{default:r(()=>[u("div",W,[u("div",null,[o(p,{placeholder:"\u8BF7\u8F93\u5165",modelValue:a(e).day_num,"onUpdate:modelValue":t[2]||(t[2]=l=>a(e).day_num=l)},null,8,["modelValue"])]),$])]),_:1})]),_:1},8,["rules","model"])]),_:1}),o(g,null,{default:r(()=>[j((_(),q(w,{type:"primary",onClick:t[3]||(t[3]=l=>f(a(m)))},{default:r(()=>[G(" \u4FDD\u5B58 ")]),_:1})),[[E,["market.activityReward/setShareSetting"]]])]),_:1})])}}});export{De as default};
