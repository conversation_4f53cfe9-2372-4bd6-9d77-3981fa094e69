import{P as U}from"./index.324d704f.js";import{D as A,C as y,I as D,J as P,F as x}from"./element-plus.5bcb7c8a.js";import{_ as R}from"./picker.9a1dad65.js";import{d as q,s as i,a0 as f,o as p,P as _,Q as r,a as L,W as a,u as o,$ as I,c as S,F as $,a7 as M}from"./@vue.a11433a6.js";import{u as N,a as O}from"./consumer.e39aaadf.js";import{u as G}from"./useDictOptions.583d6eb9.js";import{f as J}from"./index.850efb0d.js";const h=q({__name:"addUserPop",emits:["close"],setup(Q,{expose:B,emit:W}){const m=i(),d=i(),l=f({avatar:"",nickname:"",mobile:"",email:"",real_name:"",password:"",password_confirm:"",group_ids:""}),{optionsData:F,refresh:c}=G({dataList:{api:N}}),V=f({avatar:[{required:!0,message:"\u8BF7\u9009\u62E9\u5934\u50CF",trigger:"change"}],nickname:[{required:!0,message:"\u8BF7\u586B\u5199\u6635\u79F0",trigger:"change"}],password:[{required:!0,message:"\u8BF7\u586B\u5199\u5BC6\u7801",trigger:"change"}],password_confirm:[{required:!0,message:"\u8BF7\u586B\u5199\u786E\u8BA4\u5BC6\u7801",trigger:"change"}]}),g=async()=>{var n;await((n=d.value)==null?void 0:n.validate()),await O({...l}),J.msgSuccess("\u65B0\u589E\u6210\u529F\uFF01"),m.value.close()};return B({open:()=>{m.value.open(),c()}}),(n,e)=>{const w=R,s=A,t=y,b=D,E=P,v=x,C=U;return p(),_(C,{ref_key:"popRef",ref:m,title:"\u521B\u5EFA\u7528\u6237",width:"500px",async:"",onConfirm:g,onClose:e[9]||(e[9]=u=>n.$emit("close"))},{default:r(()=>[L("div",null,[a(v,{"label-width":"90px",model:o(l),ref_key:"formRef",ref:d,rules:o(V),onSubmit:e[8]||(e[8]=I(()=>{},["prevent"]))},{default:r(()=>[a(s,{label:"\u7528\u6237\u5934\u50CF",prop:"avatar"},{default:r(()=>[a(w,{modelValue:o(l).avatar,"onUpdate:modelValue":e[0]||(e[0]=u=>o(l).avatar=u)},null,8,["modelValue"])]),_:1}),a(s,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:r(()=>[a(t,{placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0",modelValue:o(l).nickname,"onUpdate:modelValue":e[1]||(e[1]=u=>o(l).nickname=u)},null,8,["modelValue"])]),_:1}),a(s,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:r(()=>[a(t,{placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801",modelValue:o(l).mobile,"onUpdate:modelValue":e[2]||(e[2]=u=>o(l).mobile=u)},null,8,["modelValue"])]),_:1}),a(s,{label:"\u90AE\u7BB1",prop:"email"},{default:r(()=>[a(t,{placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1",modelValue:o(l).email,"onUpdate:modelValue":e[3]||(e[3]=u=>o(l).email=u)},null,8,["modelValue"])]),_:1}),a(s,{label:"\u771F\u5B9E\u540D\u79F0",prop:"real_name"},{default:r(()=>[a(t,{placeholder:"\u8BF7\u8F93\u5165\u771F\u5B9E\u540D\u79F0",modelValue:o(l).real_name,"onUpdate:modelValue":e[4]||(e[4]=u=>o(l).real_name=u)},null,8,["modelValue"])]),_:1}),a(s,{label:"\u7528\u6237\u5206\u7EC4"},{default:r(()=>[a(E,{modelValue:o(l).group_ids,"onUpdate:modelValue":e[5]||(e[5]=u=>o(l).group_ids=u),multiple:""},{default:r(()=>[(p(!0),S($,null,M(o(F).dataList,(u,k)=>(p(),_(b,{key:k,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u767B\u5F55\u5BC6\u7801",prop:"password"},{default:r(()=>[a(t,{placeholder:"\u8BF7\u8F93\u5165\u767B\u5F55\u5BC6\u7801",type:"password",modelValue:o(l).password,"onUpdate:modelValue":e[6]||(e[6]=u=>o(l).password=u),"show-password":""},null,8,["modelValue"])]),_:1}),a(s,{label:"\u786E\u8BA4\u5BC6\u7801",prop:"password_confirm"},{default:r(()=>[a(t,{placeholder:"\u8BF7\u8F93\u5165\u786E\u8BA4\u5BC6\u7801",type:"password","show-password":"",modelValue:o(l).password_confirm,"onUpdate:modelValue":e[7]||(e[7]=u=>o(l).password_confirm=u)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},512)}}});export{h as _};
