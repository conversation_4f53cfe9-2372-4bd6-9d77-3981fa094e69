import{r as t}from"./index.850efb0d.js";function a(r){return t.get({url:"/draw.draw_prompt_example/lists",params:r},{ignoreCancelToken:!0})}function p(r){return t.get({url:"/draw.draw_prompt_example/detail",params:r})}function o(r){return t.post({url:"/draw.draw_prompt_example/add",params:r})}function m(r){return t.post({url:"/draw.draw_prompt_example/edit",params:r})}function l(r){return t.post({url:"/draw.draw_prompt_example/delete",params:r})}function d(r){return t.post({url:"/draw.draw_prompt_example/status",params:r})}export{o as a,a as b,d as c,l as d,m as e,p as g};
