import{D as C,C as N,B as z,t as $,w as P}from"./element-plus.5bcb7c8a.js";import{_ as I}from"./index.4a09b22e.js";import{_ as R}from"./picker.e4cc82a9.js";import{_ as S}from"./picker.9a1dad65.js";import{f as V,b as T}from"./index.850efb0d.js";import{D as j}from"./vuedraggable.2019ddfd.js";import{k as Q}from"./lodash-es.c9433054.js";import{d as W,b as q,o as u,c as h,a as n,W as s,Q as l,P as m,T as t,U as v,V as G,u as _,j as H,F as J}from"./@vue.a11433a6.js";const K={class:"mb-[18px] max-w-[400px]"},L={class:"bg-fill-light w-full p-4 mt-4"},M={class:"upload-btn w-[80px] h-[80px]"},O={key:0,class:"text-xs leading-5"},X={class:"upload-btn w-[80px] h-[80px]"},Y=n("span",{class:"text-xs leading-5"}," \u9009\u4E2D ",-1),Z={class:"flex flex-1"},ee={class:"flex-1"},oe={class:"flex-1 flex items-center"},ae={class:"drag-move cursor-move ml-auto"},ce=W({__name:"menu-set",props:{modelValue:{},max:{default:9999},min:{default:-1},itemData:{default:()=>({name:"",selected:"",unselected:"",is_show:"1",link:{}})},type:{default:"nav"}},emits:["update:modelValue"],setup(x,{emit:k}){const i=x,g=k,d=q({get(){return i.modelValue},set(a){g("update:modelValue",a)}}),b=()=>{var a;((a=d.value)==null?void 0:a.length)<i.max?d.value.push(Q(i.itemData)):V.msgError(`\u6700\u591A\u6DFB\u52A0${i.max}\u4E2A`)},w=a=>{if(d.value.length<=i.min&&i.min>-1)return V.msgError(`\u6700\u5C11\u4FDD\u7559${i.min}\u4E2A`);d.value.splice(a,1)};return(a,r)=>{const p=T,f=S,c=C,y=N,E=z,D=R,F=$,A=I,U=P;return u(),h(J,null,[n("div",K,[s(_(j),{class:"draggable",modelValue:_(d),"onUpdate:modelValue":r[0]||(r[0]=e=>H(d)?d.value=e:null),animation:"300",handle:".drag-move"},{item:l(({element:e,index:B})=>[s(A,{onClose:o=>w(B),class:"max-w-[400px]"},{default:l(()=>[n("div",L,[e.unselected!==void 0||e.selected!==void 0?(u(),m(c,{key:0,label:"\u5BFC\u822A\u56FE\u6807"},{default:l(()=>[e.unselected!==void 0?(u(),m(f,{key:0,modelValue:e.unselected,"onUpdate:modelValue":o=>e.unselected=o,"upload-class":"bg-body","exclude-domain":"",size:"80px"},{upload:l(()=>[n("div",M,[s(p,{name:"el-icon-Plus",size:16}),a.type!=="menu"?(u(),h("span",O," \u672A\u9009\u4E2D ")):t("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])):t("",!0),e.selected!==void 0&&a.type!=="menu"?(u(),m(f,{key:1,modelValue:e.selected,"onUpdate:modelValue":o=>e.selected=o,"upload-class":"bg-body","exclude-domain":"",size:"80px"},{upload:l(()=>[n("div",X,[s(p,{name:"el-icon-Plus",size:16}),Y])]),_:2},1032,["modelValue","onUpdate:modelValue"])):t("",!0)]),_:2},1024)):t("",!0),e.name!==void 0?(u(),m(c,{key:1,label:"\u5BFC\u822A\u540D\u79F0"},{default:l(()=>[n("div",Z,[n("div",ee,[s(y,{class:"w-full",modelValue:e.name,"onUpdate:modelValue":o=>e.name=o,placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),a.type==="menu"?(u(),m(E,{key:0,class:"ml-2 !h-[32px]",modelValue:e.showName,"onUpdate:modelValue":o=>e.showName=o,size:"large"},{default:l(()=>[v(G(e.showName?"\u663E\u793A":"\u4E0D\u663E\u793A"),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])):t("",!0)])]),_:2},1024)):t("",!0),e.link!==void 0?(u(),m(c,{key:2,label:"\u94FE\u63A5\u5730\u5740"},{default:l(()=>[s(D,{modelValue:e.link,"onUpdate:modelValue":o=>e.link=o,type:"pc"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):t("",!0),e.is_show!==void 0?(u(),m(c,{key:3,label:"\u662F\u5426\u663E\u793A"},{default:l(()=>[n("div",oe,[s(F,{modelValue:e.is_show,"onUpdate:modelValue":o=>e.is_show=o,"active-value":"1","inactive-value":"0"},null,8,["modelValue","onUpdate:modelValue"]),n("div",ae,[s(p,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)):t("",!0)])]),_:2},1032,["onClose"])]),_:1},8,["modelValue"])]),_(d).length<a.max?(u(),m(c,{key:0,"label-width":"0"},{default:l(()=>[s(U,{type:"primary",onClick:b},{default:l(()=>[v(" \u6DFB\u52A0\u5BFC\u822A ")]),_:1})]),_:1})):t("",!0)],64)}}});export{ce as _};
