import{P as E}from"./index.324d704f.js";import{E as T}from"./element-plus.5bcb7c8a.js";import{_ as B}from"./index.4de0c800.js";import{u as j,b as M}from"./index.850efb0d.js";import{_ as J}from"./index.4a09b22e.js";import{D as K}from"./vuedraggable.2019ddfd.js";import{_ as O,a as Q}from"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import{d as W,r as i,a3 as X,b as U,w as q,E as V,n as G,a4 as $,o as H,c as Y,W as u,a9 as Z,Q as m,a as n,$ as P,K as I,U as x,R as ee,J as ae,L as le,X as te,bk as oe,bj as se}from"./@vue.a11433a6.js";import{g as ie}from"./@vueuse.a2407f20.js";import{_ as ne}from"./vue-drag-resize.527c6620.js";const ue=W({components:{Popup:E,Draggable:K,FileItem:O,Material:B,Preview:Q},props:{modelValue:{type:[String,Array],default:()=>[]},type:{type:String,default:"image"},size:{type:String,default:"100px"},fileSize:{type:String,default:"100px"},limit:{type:Number,default:1},disabled:{type:Boolean,default:!1},hiddenUpload:{type:Boolean,default:!1},uploadClass:{type:String,default:""},excludeDomain:{type:Boolean,default:!1},withName:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["change","update:modelValue"],setup(e,{emit:t}){const g=i(),_=i(),w=i(""),y=i(!1),l=i([]),d=i([]),r=i(!0),c=i(-1),{disabled:C,limit:p,modelValue:b}=X(e),{getImageUrl:f}=j(),o=U(()=>{switch(e.type){case"image":return"\u56FE\u7247";case"video":return"\u89C6\u9891";case"audio":return"\u97F3\u4E50";default:return""}}),v=U(()=>e.limit-l.value.length>0),h=U(()=>r.value?p.value==-1?null:p.value-l.value.length:1),S=i([]),L=ie(()=>{const a=d.value.map(s=>e.excludeDomain?s.url:s.uri);S.value=d.value.map(s=>({uri:e.excludeDomain?s.url:s.uri,name:s.name})),r.value?l.value=[...l.value,...a]:l.value.splice(c.value,1,a.shift()),k()},1e3,!1),N=a=>{var s;C.value||(a>=0?(r.value=!1,c.value=a):r.value=!0,(s=g.value)==null||s.open())},A=a=>{d.value=a},k=()=>{const a=p.value!=1?l.value:l.value[0]||"";t("update:modelValue",a),e.withName?t("change",S.value):t("change",a),z()},F=a=>{l.value.splice(a,1),S.value.splice(a,1),k()},R=a=>{w.value=e.excludeDomain?f(a):a,y.value=!0},z=()=>{G(()=>{var a;e.hiddenUpload&&(l.value=[]),(a=_.value)==null||a.clearSelect()})};return q(b,a=>{l.value=Array.isArray(a)?a:a==""?[]:[a]},{immediate:!0}),V("limit",e.limit),V("hiddenUpload",e.hiddenUpload),{popupRef:g,materialRef:_,fileList:l,tipsText:o,handleConfirm:L,meterialLimit:h,showUpload:v,showPopup:N,selectChange:A,deleteImg:F,previewUrl:w,showPreview:y,handlePreview:R,handleClose:z,getImageUrl:f}}});const D=e=>(oe("data-v-8faf5bf2"),e=e(),se(),e),re={class:"material-select"},de=["onClick"],pe={class:"operation-btns text-xs text-center"},me=D(()=>n("span",null,"\u4FEE\u6539",-1)),ce=["onClick"],fe=D(()=>n("span",null,"\u6DFB\u52A0",-1)),ve={class:"material-wrap"};function he(e,t,g,_,w,y){const l=$("file-item"),d=J,r=$("draggable"),c=M,C=B,p=T,b=E,f=$("preview");return H(),Y("div",re,[u(b,{ref:"popupRef",width:"830px","custom-class":"body-padding",title:`\u9009\u62E9${e.tipsText}`,"destroy-on-close":!0,onConfirm:e.handleConfirm,onClose:e.handleClose},Z({default:m(()=>[u(p,null,{default:m(()=>[n("div",ve,[u(C,{ref:"materialRef",type:e.type,"file-size":e.fileSize,limit:e.meterialLimit,data:e.data,onChange:e.selectChange},null,8,["type","file-size","limit","data","onChange"])])]),_:1})]),_:2},[e.hiddenUpload?void 0:{name:"trigger",fn:m(()=>[n("div",{class:"material-select__trigger clearfix",onClick:t[2]||(t[2]=P(()=>{},["stop"]))},[u(r,{class:"draggable",modelValue:e.fileList,"onUpdate:modelValue":t[0]||(t[0]=o=>e.fileList=o),animation:"300","item-key":"id"},{item:m(({element:o,index:v})=>[n("div",{class:I(["material-preview",{"is-disabled":e.disabled,"is-one":e.limit==1}]),onClick:h=>e.showPopup(v)},[u(d,{onClose:h=>e.deleteImg(v)},{default:m(()=>[u(l,{uri:e.excludeDomain?e.getImageUrl(o):o,"file-size":e.size,type:e.type},null,8,["uri","file-size","type"])]),_:2},1032,["onClose"]),n("div",pe,[me,x(" | "),n("span",{onClick:P(h=>e.handlePreview(o),["stop"])},"\u67E5\u770B",8,ce)])],10,de)]),_:1},8,["modelValue"]),ee(n("div",{class:I(["material-upload",{"is-disabled":e.disabled,"is-one":e.limit==1,[e.uploadClass]:!0}]),onClick:t[1]||(t[1]=o=>e.showPopup(-1))},[ae(e.$slots,"upload",{},()=>[n("div",{class:"upload-btn",style:le({width:e.size,height:e.size})},[u(c,{size:25,name:"el-icon-Plus"}),fe],4)],!0)],2),[[te,e.showUpload]])])]),key:"0"}]),1032,["title","onConfirm","onClose"]),u(f,{modelValue:e.showPreview,"onUpdate:modelValue":t[3]||(t[3]=o=>e.showPreview=o),url:e.previewUrl,type:e.type},null,8,["modelValue","url","type"])])}const Ve=ne(ue,[["render",he],["__scopeId","data-v-8faf5bf2"]]);export{Ve as _};
