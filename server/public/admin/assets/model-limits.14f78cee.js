import{K as D,C as E,t as y,L as F}from"./element-plus.5bcb7c8a.js";import{u as U}from"./vue-router.919c7bec.js";import{v as g}from"./@vueuse.a2407f20.js";import{a as C}from"./member.b1cf8de0.js";import{d as k,i as w,o as n,c as p,a as o,W as t,Q as l,V as i,T as h,u as V}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const M=o("div",{class:"font-medium text-xl mb-4"},"\u5BF9\u8BDD\u6A21\u578B\u9650\u5236",-1),N={class:"flex flex-col"},T={class:"font-medium"},z={key:0,class:"text-xs text-gray-500"},L=o("span",null,"\u6B21",-1),S=o("div",{class:"font-medium text-xl my-4"},"\u5411\u91CF\u6A21\u578B\u9650\u5236",-1),I={class:"flex flex-col"},K={class:"font-medium"},Q={key:0,class:"text-xs text-gray-500"},R=o("span",null,"\u6B21",-1),ye=k({__name:"model-limits",props:{modelValue:{}},emits:["update:modelValue"],setup(v,{emit:A}){const b=v,{query:d}=U(),x=A,{modelValue:m}=g(b,x),B=async()=>{try{const{chat_list:s,vector_list:_}=await C();m.value.model_list.chat_model=s,m.value.model_list.vector_model=_}catch(s){console.log("\u83B7\u53D6\u6A21\u578B\u5217\u8868\u5931\u8D25=>",s)}};return w(()=>{!(d!=null&&d.id)&&B()}),(s,_)=>{const u=D,c=E,r=y,f=F;return n(),p("div",null,[o("div",null,[M,t(f,{size:"large",data:V(m).model_list.chat_model},{default:l(()=>[t(u,{label:"\u6A21\u578B\u540D\u79F0",prop:"name","min-width":"200"},{default:l(({row:e})=>[o("div",N,[o("span",T,i(e.name),1),e.sub_model_name?(n(),p("span",z," \u5B50\u6A21\u578B: "+i(e.sub_model_name),1)):h("",!0)])]),_:1}),t(u,{label:"\u4F7F\u7528\u4E0A\u9650/\u5929","min-width":"150"},{default:l(({row:e})=>[t(c,{modelValue:e.day_limit,"onUpdate:modelValue":a=>e.day_limit=a,placeholder:"\u4E3A\u7A7A\u6216\u4E3A0\u8868\u793A\u4E0D\u9650\u5236",clearable:"",type:"number",class:"w-[250px]"},{append:l(()=>[L]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),t(u,{label:"\u4F1A\u5458\u514D\u8D39","min-width":"150"},{default:l(({row:e})=>[t(r,{modelValue:e.status,"onUpdate:modelValue":a=>e.status=a,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),o("div",null,[S,t(f,{size:"large",data:V(m).model_list.vector_model},{default:l(()=>[t(u,{label:"\u6A21\u578B\u540D\u79F0",prop:"name","min-width":"200"},{default:l(({row:e})=>[o("div",I,[o("span",K,i(e.name),1),e.sub_model_name?(n(),p("span",Q," \u5B50\u6A21\u578B: "+i(e.sub_model_name),1)):h("",!0)])]),_:1}),t(u,{label:"\u4F7F\u7528\u4E0A\u9650/\u5929","min-width":"150"},{default:l(({row:e})=>[t(c,{modelValue:e.day_limit,"onUpdate:modelValue":a=>e.day_limit=a,placeholder:"\u4E3A\u7A7A\u6216\u4E3A0\u8868\u793A\u4E0D\u9650\u5236",clearable:"",class:"w-[250px]"},{append:l(()=>[R]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),t(u,{label:"\u4F1A\u5458\u514D\u8D39","min-width":"150"},{default:l(({row:e})=>[t(r,{modelValue:e.status,"onUpdate:modelValue":a=>e.status=a,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])])])}}});export{ye as default};
