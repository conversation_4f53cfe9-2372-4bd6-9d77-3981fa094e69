import{L as x,w as R,K as S,G as T}from"./element-plus.5bcb7c8a.js";import{u as $,i as L}from"./consumer.e39aaadf.js";import{u as N}from"./useDictOptions.583d6eb9.js";import{_ as V}from"./addPop.vue_vue_type_script_setup_true_lang.154387bc.js";import{f as G}from"./index.850efb0d.js";import{d as P,s as v,r as b,aj as j,o as i,c as z,W as r,Q as t,R as u,P as a,U as c,u as n,a as h,T as A,n as K}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const O={class:"mt-4"},Se=P({__name:"index",setup(Q){const _=v(),d=b(!1),w=v(),{optionsData:y,refresh:C}=N({dataList:{api:$}}),f=b([]),E=e=>{f.value=e.map(o=>o.id)},g=async e=>{await G.confirm("\u786E\u8BA4\u5220\u9664\uFF1F"),await L({id:e}),C()},k=async e=>{d.value=!0,await K(),_.value.open({id:e.id,name:e.name})},D=()=>{d.value=!1,C()};return(e,o)=>{const s=R,p=S,B=T,m=j("perms");return i(),z("div",null,[r(B,{class:"!border-none",shadow:"never"},{default:t(()=>[u((i(),a(s,{type:"primary",onClick:o[0]||(o[0]=l=>k({id:"",name:""}))},{default:t(()=>[c("+ \u65B0\u589E\u5206\u7EC4")]),_:1})),[[m,["user.grouping/add"]]]),u((i(),a(s,{disabled:n(f).length==0,onClick:o[1]||(o[1]=l=>g(n(f)))},{default:t(()=>[c("\u5220\u9664")]),_:1},8,["disabled"])),[[m,["user.grouping/del"]]]),h("div",O,[r(n(x),{ref_key:"tableRef",ref:w,class:"mt-2",size:"large",data:n(y).dataList,onSelectionChange:E},{default:t(()=>[r(p,{type:"selection",width:"55"}),r(p,{label:"\u5206\u7EC4\u540D\u79F0",prop:"name","min-width":"120"}),r(p,{label:"\u7528\u6237\u6570",prop:"user_sum","min-width":"120"}),r(p,{label:"\u64CD\u4F5C",prop:"sn","min-width":"120"},{default:t(({row:l})=>[h("div",null,[u((i(),a(s,{type:"primary",link:"",onClick:F=>k(l)},{default:t(()=>[c("\u7F16\u8F91")]),_:2},1032,["onClick"])),[[m,["user.grouping/edit"]]]),u((i(),a(s,{type:"danger",link:"",onClick:F=>g([l.id])},{default:t(()=>[c("\u5220\u9664")]),_:2},1032,["onClick"])),[[m,["user.grouping/del"]]])])]),_:1})]),_:1},8,["data"])])]),_:1}),n(d)?(i(),a(V,{key:0,ref_key:"popRef",ref:_,onSuccess:D},null,512)):A("",!0)])}}});export{Se as default};
