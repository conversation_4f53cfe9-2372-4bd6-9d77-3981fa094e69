import{r as e}from"./index.850efb0d.js";function o(t){return e.get({url:"/creation.creationModel/lists",params:t},{ignoreCancelToken:!0})}function n(t){return e.get({url:"/creation.creationModel/export",params:t},{ignoreCancelToken:!0})}function a(t){return e.get({url:"/creation.creationModel/detail",params:t})}function i(t){return e.post({url:"/creation.creationModel/add",params:t})}function u(t){return e.post({url:"/creation.creationModel/edit",params:t})}function c(t){return e.post({url:"/creation.creationModel/del",params:t})}function l(t){return e.post({url:"/creation.creationModel/batchDel",params:t})}function s(t){return e.post({url:"/creation.creationModel/status",params:t})}function d(t){return e.get({url:"/creation.creationCategory/lists",params:t},{ignoreCancelToken:!0})}function C(t){return e.post({url:"/creation.creationCategory/add",params:t})}function g(t){return e.post({url:"/creation.creationCategory/edit",params:t})}function p(t){return e.post({url:"/creation.creationCategory/del",params:t})}function M(t){return e.post({url:"/creation.creationCategory/status",params:t})}export{C as a,M as b,i as c,p as d,u as e,a as f,d as g,o as h,n as i,c as j,l as k,s as l,g as p};
