import{Q as C,R as g,D as B,C as U,B as A,F}from"./element-plus.5bcb7c8a.js";import{_ as w}from"./picker.9a1dad65.js";import{u as I}from"./index.850efb0d.js";import{d as h,o as m,c as D,W as a,Q as l,P as i,a as n,U as p,T as u,u as b,K as x,bk as S,bj as N}from"./@vue.a11433a6.js";import{_ as j}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const R=t=>(S("data-v-5d599e0b"),t=t(),N(),t),O=["src"],Q=["src"],T=R(()=>n("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8: 540 * 900",-1)),$={class:"flex"},z=h({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},type:{type:String,default:"mobile"}},setup(t){const _=t,d=I(),s=c=>{_.content.poster=c};return(c,e)=>{const f=C,V=g,r=B,y=w,E=U,k=A,v=F;return m(),D("div",null,[a(v,{"label-width":"70px"},{default:l(()=>[t.type=="mobile"?(m(),i(r,{key:0,label:"\u6D77\u62A5\u80CC\u666F"},{default:l(()=>[a(V,{modelValue:t.content.default,"onUpdate:modelValue":e[0]||(e[0]=o=>t.content.default=o)},{default:l(()=>[n("div",null,[a(f,{label:1},{default:l(()=>[p("\u7CFB\u7EDF\u9ED8\u8BA4")]),_:1}),a(f,{label:2},{default:l(()=>[p("\u81EA\u5B9A\u4E49")]),_:1})])]),_:1},8,["modelValue"])]),_:1})):u("",!0),t.type=="mobile"&&t.content.default==1?(m(),i(r,{key:1,label:""},{default:l(()=>[n("img",{src:b(d).getImageUrl(t.content.defaultUrl1),alt:"",class:x(["w-[80px] h-[120px] mr-[30px] cursor-pointer p-[10px]",{actived:t.content.poster==1}]),onClick:e[1]||(e[1]=o=>s(1))},null,10,O),n("img",{src:b(d).getImageUrl(t.content.defaultUrl2),alt:"",class:x(["w-[80px] h-[120px] cursor-pointer p-[10px]",{actived:t.content.poster==2}]),onClick:e[2]||(e[2]=o=>s(2))},null,10,Q)]),_:1})):u("",!0),t.type=="mobile"&&t.content.default==2?(m(),i(r,{key:2,label:""},{default:l(()=>[n("div",null,[a(y,{modelValue:t.content.posterUrl,"onUpdate:modelValue":e[3]||(e[3]=o=>t.content.posterUrl=o),"exclude-domain":""},null,8,["modelValue"]),T])]),_:1})):u("",!0),t.type=="mobile"?(m(),i(r,{key:3,label:"\u9080\u8BF7\u6587\u6848"},{default:l(()=>[n("div",$,[a(E,{placeholder:"",modelValue:t.content.data.content,"onUpdate:modelValue":e[4]||(e[4]=o=>t.content.data.content=o),class:"w-[300px]"},null,8,["modelValue"]),a(k,{class:"ml-2","true-label":1,modelValue:t.content.showData,"onUpdate:modelValue":e[5]||(e[5]=o=>t.content.showData=o),"false-label":0},{default:l(()=>[p("\u663E\u793A")]),_:1},8,["modelValue"])])]),_:1})):u("",!0)]),_:1})])}}});const Qt=j(z,[["__scopeId","data-v-5d599e0b"]]);export{Qt as default};
