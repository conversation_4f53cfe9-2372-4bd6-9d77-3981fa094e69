import{r}from"./index.850efb0d.js";function c(e){return r.get({url:"/cardcode.cardCode/lists",params:e},{ignoreCancelToken:!0})}function o(){return r.get({url:"/cardcode.cardCode/getPackageList"})}function a(e){return r.post({url:"/cardcode.cardCode/add",params:e})}function t(e){return r.post({url:"/cardcode.cardCode/del",params:e})}function n(e){return r.get({url:"/cardcode.cardCode/detail",params:e})}function u(e){return r.get({url:"/cardcode.cardCodeRecord/lists",params:e},{ignoreCancelToken:!0})}function s(){return r.get({url:"/cardcode.cardCode/getConfig"})}function i(e){return r.post({url:"/cardcode.cardCode/setConfig",params:e})}export{n as a,a as b,u as c,o as d,c as e,t as f,s as g,i as h};
