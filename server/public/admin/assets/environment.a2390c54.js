import{z as v,b as f}from"./index.850efb0d.js";import{K as b,L as h,G as C}from"./element-plus.5bcb7c8a.js";import{d,a0 as E,i as B,o as s,c as k,W as e,Q as o,a as r,u as c,P as u}from"./@vue.a11433a6.js";import"./@vueuse.a2407f20.js";import"./lodash.9ffd80b1.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./@element-plus.1e23f767.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./lodash-es.c9433054.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";const x={class:"system-environment"},y=r("div",null,"\u670D\u52A1\u5668\u4FE1\u606F",-1),w={class:"mt-4"},A=r("div",null,"PHP\u73AF\u5883\u8981\u6C42",-1),F={class:"mt-4"},g=r("div",null,"\u76EE\u5F55\u6743\u9650",-1),P={class:"mt-4"},S=d({name:"environment"}),ve=d({...S,setup(q){const a=E({server:[],env:[],auth:[]}),_=()=>{v().then(n=>{a.server=n.server,a.env=n.env,a.auth=n.auth})};return B(()=>{_()}),(n,I)=>{const t=b,i=h,m=C,l=f;return s(),k("div",x,[e(m,{class:"!border-none",shadow:"never"},{default:o(()=>[y,r("div",w,[e(i,{data:c(a).server},{default:o(()=>[e(t,{prop:"param",label:"\u53C2\u6570"}),e(t,{prop:"value",label:"\u503C"})]),_:1},8,["data"])])]),_:1}),e(m,{shadow:"never",class:"!border-none mt-4"},{default:o(()=>[A,r("div",F,[e(i,{data:c(a).env},{default:o(()=>[e(t,{prop:"option",label:"\u9009\u9879"}),e(t,{prop:"require",label:"\u8981\u6C42"}),e(t,{label:"\u72B6\u6001"},{default:o(p=>[p.row.status?(s(),u(l,{key:0,name:"el-icon-Select",class:"text-success"})):(s(),u(l,{key:1,name:"el-icon-CloseBold",class:"text-danger"}))]),_:1}),e(t,{prop:"remark",label:"\u8BF4\u660E\u53CA\u5E2E\u52A9"})]),_:1},8,["data"])])]),_:1}),e(m,{shadow:"never",class:"!border-none mt-4"},{default:o(()=>[g,r("div",P,[e(i,{data:c(a).auth},{default:o(()=>[e(t,{prop:"dir",label:"\u9009\u9879"}),e(t,{prop:"require",label:"\u8981\u6C42"}),e(t,{label:"\u72B6\u6001"},{default:o(p=>[p.row.status?(s(),u(l,{key:0,name:"el-icon-Select",class:"text-success"})):(s(),u(l,{key:1,name:"el-icon-CloseBold",class:"text-danger"}))]),_:1}),e(t,{prop:"remark",label:"\u8BF4\u660E\u53CA\u5E2E\u52A9"})]),_:1},8,["data"])])]),_:1})])}}});export{ve as default};
