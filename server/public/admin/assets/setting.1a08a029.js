import{_ as x}from"./index.88e852a7.js";import{t as C,D as b,C as V,F as h,G as k,w as y}from"./element-plus.5bcb7c8a.js";import{a as D,e as S}from"./sign.5d177d14.js";import{f as R}from"./index.850efb0d.js";import{d as N,r as p,a0 as U,aj as I,o as _,c as j,W as o,Q as a,u as r,a as i,R as G,P,U as Q}from"./@vue.a11433a6.js";import"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const T=i("div",{class:"text-xl font-medium mb-[20px]"},"\u7B7E\u5230\u5956\u52B1",-1),W={class:"flex"},$=i("div",{class:"ml-[10px]"},"\u7535\u529B\u503C",-1),ke=N({__name:"setting",setup(q){const m=p(),e=p({is_open:1,one_award:1}),c=U({}),s=async()=>{e.value=await D()};s();const d=async n=>{if(!n){console.log(n);return}try{await n.validate(),e.value.one_award>0?(await S(e.value),await s()):R.msgError("\u7535\u529B\u503C\u5FC5\u987B\u5927\u4E8E0")}catch(t){console.log(t)}};return(n,t)=>{const f=C,u=b,v=V,w=h,B=k,g=y,F=x,E=I("perms");return _(),j("div",null,[o(B,{shadow:"never",class:"!border-none"},{default:a(()=>[T,o(w,{ref_key:"ruleFormRef",ref:m,rules:r(c),model:r(e),"label-width":"120px"},{default:a(()=>[o(u,{label:"\u662F\u5426\u5F00\u542F"},{default:a(()=>[o(f,{modelValue:r(e).is_open,"onUpdate:modelValue":t[0]||(t[0]=l=>r(e).is_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7B7E\u5230\u4E00\u6B21\u5956\u52B1",prop:"rewards"},{default:a(()=>[i("div",null,[i("div",W,[i("div",null,[o(v,{placeholder:"\u8BF7\u8F93\u5165",modelValue:r(e).one_award,"onUpdate:modelValue":t[1]||(t[1]=l=>r(e).one_award=l)},null,8,["modelValue"])]),$])])]),_:1})]),_:1},8,["rules","model"])]),_:1}),o(F,null,{default:a(()=>[G((_(),P(g,{type:"primary",onClick:t[2]||(t[2]=l=>d(r(m)))},{default:a(()=>[Q(" \u4FDD\u5B58 ")]),_:1})),[[E,["market.activityReward/setSignSetting"]]])]),_:1})])}}});export{ke as default};
