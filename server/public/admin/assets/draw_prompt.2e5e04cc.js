import{r as t}from"./index.850efb0d.js";function a(r){return t.get({url:"/draw.draw_prompt/lists",params:r},{ignoreCancelToken:!0})}function e(r){return t.post({url:"/draw.draw_prompt/add",params:r})}function p(r){return t.post({url:"/draw.draw_prompt/edit",params:r})}function d(r){return t.post({url:"/draw.draw_prompt/delete",params:r})}function u(r){return t.post({url:"/draw.draw_prompt/status",params:r})}export{e as a,u as b,d,p as e,a as g};
