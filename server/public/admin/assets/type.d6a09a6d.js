import{r as t}from"./index.850efb0d.js";function s(l){return t.get({url:"/skill.skillCategory/lists",params:l},{ignoreCancelToken:!0})}function e(l){return t.post({url:"/skill.skillCategory/add",params:l})}function i(l){return t.post({url:"/skill.skillCategory/edit",params:l})}function o(l){return t.post({url:"/skill.skillCategory/del",params:l})}function a(l){return t.post({url:"/skill.skillCategory/status",params:l})}export{e as a,a as c,o as d,i as e,s};
