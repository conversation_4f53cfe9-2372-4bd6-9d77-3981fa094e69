import{P as D}from"./index.324d704f.js";import{D as R,I as P,J as S,C as h,F as q,E as I}from"./element-plus.5bcb7c8a.js";import{u as U}from"./useDictOptions.583d6eb9.js";import{d as z,s as c,r as N,o as n,P as d,Q as u,W as r,u as l,a as O,V as J,c as L,F as Q,a7 as W}from"./@vue.a11433a6.js";import"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const De=z({__name:"correct-popup",emits:["confirm"],setup($,{expose:f,emit:_}){const E=_,s=c(),m=c(),e=N({name:"",kb_id:"",ask:"",reply:""}),k={kb_id:[{required:!0,message:"\u9009\u62E9\u77E5\u8BC6\u5E93"}],ask:[{required:!0,message:"\u8BF7\u8F93\u5165\u95EE\u9898"}],reply:[{required:!0,message:"\u8BF7\u8F93\u5165\u7B54\u6848"}]},b=o=>{var t;e.value={...e.value,ask:o.ask,reply:o.reply,name:o.name},(t=m.value)==null||t.open()},y=()=>{var o;(o=m.value)==null||o.close()},B=async()=>{var o;await((o=s.value)==null?void 0:o.validate()),E("confirm",e.value)},{optionsData:F}=U({knowledge:{api:()=>{},params:{type:1}}});return f({open:b,close:y}),(o,t)=>{const p=R,V=P,w=S,i=h,v=q,x=I,g=D;return n(),d(g,{ref_key:"popupRef",ref:m,fullscreen:"",center:"",title:"\u4FEE\u6B63\u95EE\u7B54",async:"",onConfirm:B},{default:u(()=>[r(x,null,{default:u(()=>[r(v,{ref_key:"formRef",ref:s,model:l(e),rules:k,"label-width":"120px"},{default:u(()=>[r(p,{label:"\u6240\u5C5E\u5E94\u7528"},{default:u(()=>[O("div",null,J(l(e).name),1)]),_:1}),r(p,{label:"\u9009\u62E9\u77E5\u8BC6\u5E93",prop:"kb_id"},{default:u(()=>[r(w,{class:"w-[240px]",modelValue:l(e).kb_id,"onUpdate:modelValue":t[0]||(t[0]=a=>l(e).kb_id=a)},{default:u(()=>[(n(!0),L(Q,null,W(l(F).knowledge,(a,C)=>(n(),d(V,{key:C,label:`${a.name}`,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(p,{label:"\u63D0\u95EE\u95EE\u9898",prop:"ask"},{default:u(()=>[r(i,{modelValue:l(e).ask,"onUpdate:modelValue":t[1]||(t[1]=a=>l(e).ask=a),placeholder:"\u8BF7\u8F93\u5165\u95EE\u9898",type:"textarea",resize:"none",rows:6,maxlength:"600","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1}),r(p,{label:"\u95EE\u9898\u7B54\u6848",prop:"reply"},{default:u(()=>[r(i,{modelValue:l(e).reply,"onUpdate:modelValue":t[2]||(t[2]=a=>l(e).reply=a),placeholder:"\u8BF7\u8F93\u5165\u7B54\u6848",type:"textarea",resize:"none",rows:20,clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},512)}}});export{De as default};
