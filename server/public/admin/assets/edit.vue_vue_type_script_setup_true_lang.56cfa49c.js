import{I as q,J as I,D as L,C as S,t as N,F as O}from"./element-plus.5bcb7c8a.js";import{b as T}from"./ai_key.2945027e.js";import{r as i,f as j}from"./index.850efb0d.js";import{P as z}from"./index.324d704f.js";import{d as J,s as F,r as C,b as Q,o as d,c as w,W as s,Q as r,u as l,P as _,F as K,a7 as V}from"./@vue.a11433a6.js";function te(a){return i.get({url:"/setting.KeyRule/lists",params:a},{ignoreCancelToken:!0})}function W(a){return i.get({url:"/setting.KeyRule/detail",params:a})}function G(a){return i.post({url:"/setting.KeyRule/add",data:a})}function H(a){return i.post({url:"/setting.KeyRule/edit",data:a})}function le(a){return i.post({url:"/setting.KeyRule/del",data:a})}function ae(a){return i.post({url:"/setting.KeyRule/status",data:a})}function oe(a){return i.post({url:"/setting.KeyRule/setConfig",data:a})}function se(){return i.get({url:"/setting.KeyRule/getConfig"})}const X={class:"edit-popup"},ne=J({__name:"edit",emits:["success"],setup(a,{expose:D,emit:k}){const x=k,R=F(),g=F(),v=C(""),f=C([]),u=C({id:"",type:"",rule:"",channel:"",model_id:"",prompt:"",status:1}),B={key:[{required:!0,message:"\u8BF7\u8F93\u5165APIKey",trigger:["blur"]}],rule:[{required:!0,message:"\u8BF7\u8F93\u5165\u505C\u7528\u89C4\u5219"}],prompt:[{required:!0,message:"\u8BF7\u8F93\u5165\u505C\u7528\u63D0\u793A"}]},h=Q(()=>[1,2].includes(Number(u.value.type))),E=async o=>{const e=f.value.find(n=>n.id===o);e&&(u.value.channel=e.channel)},A=async()=>{var o,e;try{await((o=R.value)==null?void 0:o.validate()),u.value.id==""?await G(u.value):u.value.id!=""&&await H(u.value),j.msgSuccess("\u64CD\u4F5C\u6210\u529F"),x("success"),(e=g.value)==null||e.close()}catch(n){return n}},P=async(o,e,n)=>{var c;if(e=="add")u.value={id:"",type:o,ai_key:"",rule:"",prompt:"",status:1},v.value="\u65B0\u589E\u89C4\u5219";else if(e=="edit"){const p=await W({id:n.id});Object.keys(u.value).map(m=>{var y;u.value[m]=(y=p[m])!=null?y:0}),u.value.type=o,v.value="\u7F16\u8F91\u89C4\u5219"}(c=g.value)==null||c.open(),M(o)},M=async o=>{try{const e=await T({type:o});f.value=e}catch(e){console.log(e)}};return D({open:P}),(o,e)=>{const n=q,c=I,p=L,m=S,y=N,U=O;return d(),w("div",X,[s(z,{ref_key:"popupRef",ref:g,title:l(v),async:!0,width:"550px",onConfirm:A},{default:r(()=>[s(U,{class:"ls-form",ref_key:"formRef",ref:R,rules:B,model:l(u),"label-width":"90px"},{default:r(()=>[s(p,{label:"\u63A5\u53E3\u7C7B\u578B"},{default:r(()=>[l(h)?(d(),_(c,{key:0,class:"w-[330px]",modelValue:l(u).model_id,"onUpdate:modelValue":e[0]||(e[0]=t=>l(u).model_id=t),onChange:E},{default:r(()=>[(d(!0),w(K,null,V(l(f),t=>(d(),_(n,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(d(),_(c,{key:1,class:"w-[330px]",modelValue:l(u).channel,"onUpdate:modelValue":e[1]||(e[1]=t=>l(u).channel=t)},{default:r(()=>[(d(!0),w(K,null,V(l(f),(t,b)=>(d(),_(n,{key:b,label:t,value:b},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]))]),_:1}),s(p,{label:"\u505C\u7528\u89C4\u5219",prop:"rule"},{default:r(()=>[s(m,{class:"w-[330px]",modelValue:l(u).rule,"onUpdate:modelValue":e[2]||(e[2]=t=>l(u).rule=t),placeholder:"\u8BF7\u8F93\u5165\u505C\u7528\u89C4\u5219",type:"textarea",autosize:{minRows:2,maxRows:4},clearable:""},null,8,["modelValue"])]),_:1}),s(p,{label:"\u505C\u7528\u63D0\u793A",prop:"prompt"},{default:r(()=>[s(m,{class:"w-[330px]",modelValue:l(u).prompt,"onUpdate:modelValue":e[3]||(e[3]=t=>l(u).prompt=t),placeholder:"\u8BF7\u8F93\u5165\u505C\u7528\u63D0\u793A",rows:4,clearable:""},null,8,["modelValue"])]),_:1}),s(p,{label:"\u72B6\u6001"},{default:r(()=>[s(y,{modelValue:l(u).status,"onUpdate:modelValue":e[4]||(e[4]=t=>l(u).status=t),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{ne as _,se as a,oe as b,le as d,te as g,ae as s};
