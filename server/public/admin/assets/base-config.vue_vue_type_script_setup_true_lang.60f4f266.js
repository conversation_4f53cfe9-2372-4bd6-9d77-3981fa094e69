import{Q as C,R as w,D as x,C as h,I as g,J as k}from"./element-plus.5bcb7c8a.js";import{_ as U}from"./picker.9a1dad65.js";import{a as R}from"./robot_square.168fdb05.js";import{u as y}from"./useDictOptions.583d6eb9.js";import{m as N}from"./@vueuse.a2407f20.js";import{d as q,o as n,c as F,W as e,Q as t,a as o,u as l,F as D,a7 as A,P as c,U as r,T as I}from"./@vue.a11433a6.js";const z={class:"pt-[10px]"},O=o("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A240*240px",-1),Q={class:"w-80"},T={class:"w-80"},G={class:"flex"},J=o("div",{class:"form-tips"},"\u53EA\u652F\u6301\u591A\u9009\u540C\u4E00\u79CD\u7C7B\u578B\u7684\u77E5\u8BC6\u5E93",-1),L={class:"flex-1 min-w-0"},M={class:"flex"},P={class:"w-80"},S=o("div",{class:"form-tips"},"\u5F15\u5BFC\u5E94\u7528\u7684\u804A\u5929\u65B9\u5411\uFF0C\u8BE5\u5185\u5BB9\u4F1A\u88AB\u56FA\u5B9A\u5728\u4E0A\u4E0B\u6587\u7684\u5F00\u5934\u3002",-1),W=o("div",{class:"form-tips"},"\u4E0D\u8BBE\u7F6E\u7684\u8BDD\uFF0C\u5BF9\u8BDD\u56FE\u6807\u9ED8\u8BA4\u62FF\u667A\u80FD\u4F53\u5C01\u9762",-1),j=o("div",{class:"form-tips"},"\u5728\u524D\u53F0\u663E\u793A\u5BF9\u8BDD\u4E0A\u4E0B\u6587\uFF0C\u9ED8\u8BA4\u663E\u793A",-1),H=o("div",{class:"form-tips"},"\u5728\u524D\u53F0\u663E\u793A\u5F15\u7528\u5185\u5BB9\uFF0C\u9ED8\u8BA4\u663E\u793A",-1),K={class:"w-80"},X=o("div",{class:"form-tips"},"\u7ED9\u516C\u5F00\u7684\u667A\u80FD\u4F53\u5206\u7C7B",-1),su=q({__name:"base-config",props:{modelValue:{}},emits:["update:modelValue"],setup(V,{emit:b}){const a=N(V,"modelValue",b),{optionsData:v}=y({robotCategory:{api:R}});return($,s)=>{const B=U,d=x,p=h,_=g,E=k,m=C,f=w;return n(),F("div",z,[e(d,{label:"\u667A\u80FD\u4F53\u56FE\u6807",prop:"image"},{default:t(()=>[o("div",null,[o("div",null,[e(B,{modelValue:l(a).image,"onUpdate:modelValue":s[0]||(s[0]=u=>l(a).image=u),limit:1},null,8,["modelValue"])]),O])]),_:1}),e(d,{label:"\u667A\u80FD\u4F53\u540D\u79F0",prop:"name"},{default:t(()=>[o("div",Q,[e(p,{modelValue:l(a).name,"onUpdate:modelValue":s[1]||(s[1]=u=>l(a).name=u),placeholder:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53\u540D\u79F0",clearable:""},null,8,["modelValue"])])]),_:1}),e(d,{label:"\u7B80\u4ECB",prop:"intro"},{default:t(()=>[o("div",T,[e(p,{modelValue:l(a).intro,"onUpdate:modelValue":s[2]||(s[2]=u=>l(a).intro=u),placeholder:"\u8BF7\u7B80\u5355\u63CF\u8FF0\u4E0B\u7ED9\u4F60\u7684\u667A\u80FD\u4F53",type:"textarea",autosize:{minRows:3,maxRows:6},maxlength:200,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1}),e(d,{label:"\u5173\u8054\u77E5\u8BC6\u5E93"},{default:t(()=>{var u;return[o("div",null,[o("div",G,[e(E,{class:"!w-[320px]","model-value":(u=l(a).knows)==null?void 0:u.map(i=>i.id),placeholder:"\u8BF7\u9009\u62E9\u5173\u8054\u77E5\u8BC6\u5E93",clearable:"",multiple:""},{default:t(()=>[(n(!0),F(D,null,A(l(a).knows,i=>(n(),c(_,{key:i.id,label:i.name,value:i.id},null,8,["label","value"]))),128))]),_:1},8,["model-value"])]),J])]}),_:1}),e(d,{label:"\u89D2\u8272\u8BBE\u5B9A",prop:"roles_prompt"},{default:t(()=>[o("div",L,[o("div",M,[o("div",P,[e(p,{modelValue:l(a).roles_prompt,"onUpdate:modelValue":s[3]||(s[3]=u=>l(a).roles_prompt=u),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u8BBE\u5B9A",type:"textarea",autosize:{minRows:4,maxRows:6},clearable:""},null,8,["modelValue"])])]),S])]),_:1}),e(d,{label:"\u5BF9\u8BDD\u56FE\u6807"},{default:t(()=>[o("div",null,[e(B,{disabled:"",modelValue:l(a).icons,"onUpdate:modelValue":s[4]||(s[4]=u=>l(a).icons=u)},null,8,["modelValue"]),W])]),_:1}),e(d,{label:"\u5BF9\u8BDD\u4E0A\u4E0B\u6587",prop:"is_show_context"},{default:t(()=>[o("div",null,[e(f,{modelValue:l(a).is_show_context,"onUpdate:modelValue":s[5]||(s[5]=u=>l(a).is_show_context=u)},{default:t(()=>[e(m,{label:1},{default:t(()=>[r(" \u663E\u793A ")]),_:1}),e(m,{label:0},{default:t(()=>[r(" \u5173\u95ED ")]),_:1})]),_:1},8,["modelValue"]),j])]),_:1}),e(d,{label:"\u5F15\u7528\u5185\u5BB9",prop:"is_show_quote"},{default:t(()=>[o("div",null,[e(f,{modelValue:l(a).is_show_quote,"onUpdate:modelValue":s[6]||(s[6]=u=>l(a).is_show_quote=u)},{default:t(()=>[e(m,{label:1},{default:t(()=>[r(" \u663E\u793A ")]),_:1}),e(m,{label:0},{default:t(()=>[r(" \u5173\u95ED ")]),_:1})]),_:1},8,["modelValue"]),H])]),_:1}),l(a).is_public?(n(),c(d,{key:0,label:"\u9009\u62E9\u5206\u7C7B",prop:"cate_id"},{default:t(()=>[o("div",K,[e(E,{modelValue:l(a).cate_id,"onUpdate:modelValue":s[7]||(s[7]=u=>l(a).cate_id=u),placeholder:"\u8BF7\u9009\u62E9\u5206\u7C7B",clearable:""},{default:t(()=>[e(_,{label:"\u5168\u90E8",value:0}),(n(!0),F(D,null,A(l(v).robotCategory,u=>(n(),c(_,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),X])]),_:1})):I("",!0)])}}});export{su as _};
