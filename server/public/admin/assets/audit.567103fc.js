import{H as X,Q as Y,R as Z,k as I,c as ee,o as te,G as ue,w as oe,D as se,C as ae,F as le,M as ie}from"./element-plus.5bcb7c8a.js";import{U as re,V as ne,O as _e}from"./@element-plus.1e23f767.js";import{P as ce}from"./index.324d704f.js";import de from"./edit-history-popup.91259923.js";import me from"./edit-detail-popup.901955a7.js";import{b as pe}from"./robot_square.168fdb05.js";import{a as fe}from"./robot_edit_log.ad074937.js";import{d as Fe,s as y,a0 as ve,r as h,b as x,o as _,c as p,W as u,Q as o,u as e,a,V as c,T as E,U as d,R as ye,P as z,F as Ee,a7 as Be,bk as Ae,bj as De}from"./@vue.a11433a6.js";import{_ as ge}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const b=F=>(Ae("data-v-b4e89dc6"),F=F(),De(),F),he={class:"audit-popup"},xe={key:0,class:"robot-info mb-4"},be={class:"flex items-center"},Ce={class:"flex-1"},ke={class:"text-lg font-medium mb-1"},we={class:"text-gray-500 text-sm"},Re={class:"flex items-center mt-2 text-sm text-gray-400"},Ve=b(()=>a("span",{class:"mx-2"},"|",-1)),Pe={class:"edit-history-summary mb-4"},Ie={class:"flex justify-between items-center"},ze=b(()=>a("span",{class:"font-medium"},"\u7F16\u8F91\u5386\u53F2",-1)),He={key:0},Se={class:"mb-3"},Le={class:"recent-edits"},Ne={class:"flex justify-between items-center"},Te={class:"flex items-center"},Ue={class:"text-sm"},je={key:0,class:"text-red-500 text-sm mt-1"},$e={key:1,class:"text-gray-500 text-center py-6"},Ge=b(()=>a("div",null,"\u6682\u65E0\u7F16\u8F91\u8BB0\u5F55",-1)),Me=Fe({__name:"audit",emits:["success","close"],setup(F,{expose:H,emit:S}){const C=S,k=y(),B=y(),A=y(),D=y(),l=ve({id:[],verify_status:1,verify_result:""}),i=h({}),n=h([]),g=h(!1),L={},N=x(()=>n.value.slice(0,3)),T=x(()=>n.value.length>0?n.value[0].create_time_text:"\u65E0"),U=x(()=>n.value.some(s=>s.is_auto_offline)),j=async s=>{if(!!s)try{g.value=!0,console.log("\u{1F50D} [\u5BA1\u6838\u5F39\u7A97] \u83B7\u53D6\u7F16\u8F91\u5386\u53F2\uFF0C\u667A\u80FD\u4F53ID:",s);const t=await fe({robot_id:s,page_size:10,page_no:1});n.value=t.lists||[],console.log("\u{1F4CA} [\u5BA1\u6838\u5F39\u7A97] \u7F16\u8F91\u5386\u53F2\u6570\u636E:",n.value)}catch(t){console.error("\u274C [\u5BA1\u6838\u5F39\u7A97] \u83B7\u53D6\u7F16\u8F91\u5386\u53F2\u5931\u8D25:",t),n.value=[]}finally{g.value=!1}},$=()=>{var s;if(!i.value.robot_id){I.warning("\u667A\u80FD\u4F53\u4FE1\u606F\u4E0D\u5B8C\u6574\uFF0C\u65E0\u6CD5\u67E5\u770B\u5386\u53F2\u8BB0\u5F55");return}(s=A.value)==null||s.open(i.value.robot_id,i.value.robot_name)},G=s=>{var t;(t=D.value)==null||t.open(s.id)},M=async()=>{var s,t;try{await((s=k.value)==null?void 0:s.validate()),console.log("\u{1F4DD} [\u5BA1\u6838\u5F39\u7A97] \u63D0\u4EA4\u5BA1\u6838\u6570\u636E:",l),await pe(l),I.success("\u5BA1\u6838\u5B8C\u6210"),(t=B.value)==null||t.close(),C("success")}catch(m){console.error("\u274C [\u5BA1\u6838\u5F39\u7A97] \u5BA1\u6838\u63D0\u4EA4\u5931\u8D25:",m)}},O=()=>{i.value={},n.value=[],l.verify_status=1,l.verify_result="",C("close")};return H({open:(s,t={})=>{var m;console.log("\u{1F680} [\u5BA1\u6838\u5F39\u7A97] \u6253\u5F00\u5BA1\u6838\u5F39\u7A97"),console.log("\u{1F4CB} [\u5BA1\u6838\u5F39\u7A97] \u5BA1\u6838ID\u5217\u8868:",s),console.log("\u{1F916} [\u5BA1\u6838\u5F39\u7A97] \u667A\u80FD\u4F53\u4FE1\u606F:",t),l.id=s,l.verify_status=1,l.verify_result="",i.value=t||{},t&&t.robot_id?j(t.robot_id):n.value=[],(m=B.value)==null||m.open()}}),(s,t)=>{const m=ee,Q=te,w=ue,R=oe,v=X,V=Y,q=Z,P=se,W=ae,J=le,K=ie;return _(),p("div",he,[u(ce,{ref_key:"popupRef",ref:B,title:"\u667A\u80FD\u4F53\u5BA1\u6838",async:!0,width:"800px",onConfirm:M,onClose:O},{default:o(()=>[e(i).robot_name?(_(),p("div",xe,[u(w,{shadow:"never"},{default:o(()=>[a("div",be,[u(Q,{src:e(i).robot_image,size:50,class:"mr-4"},{default:o(()=>[u(m,null,{default:o(()=>[u(e(re))]),_:1})]),_:1},8,["src"]),a("div",Ce,[a("h3",ke,c(e(i).robot_name),1),a("p",we,c(e(i).robot_intro||"\u6682\u65E0\u7B80\u4ECB"),1),a("div",Re,[a("span",null,"\u667A\u80FD\u4F53ID: "+c(e(i).robot_id),1),Ve,a("span",null,"\u521B\u5EFA\u8005: "+c(e(i).user_nickname||e(i).user_account),1)])])])]),_:1})])):E("",!0),a("div",Pe,[u(w,{shadow:"never"},{header:o(()=>[a("div",Ie,[ze,u(R,{type:"primary",link:"",onClick:$,disabled:!e(i).robot_id},{default:o(()=>[d(" \u67E5\u770B\u5B8C\u6574\u5386\u53F2 ")]),_:1},8,["disabled"])])]),default:o(()=>[ye((_(),p("div",null,[e(n).length>0?(_(),p("div",He,[a("div",Se,[u(v,{type:"info",class:"mr-2"},{default:o(()=>[d(" \u603B\u7F16\u8F91\u6B21\u6570: "+c(e(n).length),1)]),_:1}),u(v,{type:"warning",class:"mr-2"},{default:o(()=>[d(" \u6700\u8FD1\u7F16\u8F91: "+c(e(T)),1)]),_:1}),e(U)?(_(),z(v,{key:0,type:"danger"},{default:o(()=>[d(" \u66FE\u81EA\u52A8\u4E0B\u67B6 ")]),_:1})):E("",!0)]),a("div",Le,[(_(!0),p(Ee,null,Be(e(N),r=>(_(),p("div",{key:r.id,class:"edit-item"},[a("div",Ne,[a("div",Te,[u(v,{type:r.edit_type===1?"primary":"success",size:"small",class:"mr-2"},{default:o(()=>[d(c(r.edit_type_text),1)]),_:2},1032,["type"]),a("span",Ue,c(r.create_time_text),1)]),u(R,{type:"primary",link:"",size:"small",onClick:f=>G(r)},{default:o(()=>[d(" \u67E5\u770B\u8BE6\u60C5 ")]),_:2},1032,["onClick"])]),r.is_auto_offline?(_(),p("div",je,[u(m,{class:"mr-1"},{default:o(()=>[u(e(ne))]),_:1}),d(" \u81EA\u52A8\u4E0B\u67B6: "+c(r.offline_reason),1)])):E("",!0)]))),128))])])):(_(),p("div",$e,[u(m,{class:"text-2xl mb-2"},{default:o(()=>[u(e(_e))]),_:1}),Ge]))])),[[K,e(g)]])]),_:1})]),u(J,{class:"ls-form",ref_key:"formRef",ref:k,rules:L,model:e(l),"label-width":"90px"},{default:o(()=>[u(P,{label:"\u5BA1\u6838\u7ED3\u679C",prop:"verify_status"},{default:o(()=>[u(q,{modelValue:e(l).verify_status,"onUpdate:modelValue":t[0]||(t[0]=r=>e(l).verify_status=r)},{default:o(()=>[u(V,{label:1,size:"large"},{default:o(()=>[d("\u5BA1\u6838\u901A\u8FC7")]),_:1}),u(V,{label:2,size:"large"},{default:o(()=>[d("\u5BA1\u6838\u62D2\u7EDD")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(l).verify_status==2?(_(),z(P,{key:0,label:"\u62D2\u7EDD\u539F\u56E0",prop:"verify_result"},{default:o(()=>[u(W,{modelValue:e(l).verify_result,"onUpdate:modelValue":t[1]||(t[1]=r=>e(l).verify_result=r),type:"textarea",autosize:{minRows:8,maxRows:20},placeholder:"\u8BF7\u8F93\u5165\u62D2\u7EDD\u539F\u56E0"},null,8,["modelValue"])]),_:1})):E("",!0)]),_:1},8,["model"])]),_:1},512),u(de,{ref_key:"editHistoryPopupRef",ref:A,onClose:t[2]||(t[2]=r=>{var f;return(f=e(A))==null?void 0:f.close()})},null,512),u(me,{ref_key:"editDetailPopupRef",ref:D,onClose:t[3]||(t[3]=r=>{var f;return(f=e(D))==null?void 0:f.close()})},null,512)])}}});const zt=ge(Me,[["__scopeId","data-v-b4e89dc6"]]);export{zt as default};
