import{I as D,J as L,D as I,C as U,v as q,t as N,F as P}from"./element-plus.5bcb7c8a.js";import{r as i}from"./index.850efb0d.js";import{g as O}from"./problem_category.9c1e9165.js";import{P as j}from"./index.324d704f.js";import{d as z,s as E,r as _,o as f,c as F,W as l,Q as n,u,F as J,a7 as T,P as W,a as h}from"./@vue.a11433a6.js";function ue(o){return i.get({url:"/chat.chat_sample/lists",params:o})}function G(o){return i.post({url:"/chat.chat_sample/add",params:o})}function H(o){return i.post({url:"/chat.chat_sample/edit",params:o})}function oe(o){return i.post({url:"/chat.chat_sample/del",params:o})}function ae(o){return i.post({url:"/chat.chat_sample/status",params:o})}const K={class:"edit-popup"},M=h("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u636E\u8D8A\u5927\u8D8A\u6392\u524D\u9762",-1),le=z({__name:"edit",emits:["success","close"],setup(o,{expose:y,emit:w}){const B=w,v=E(),m=E(),d=_(""),g=_([]),e=_({id:"",category_id:"",content:"",sort:0,status:"1"}),V={category_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u793A\u4F8B\u7C7B\u76EE",trigger:["blur"]}],content:[{required:!0,message:"\u8BF7\u8F93\u5165\u793A\u4F8B\u5185\u5BB9",trigger:["blur"]}]},b=async()=>{const{lists:s}=await O();g.value=s},A=async()=>{var s,t;try{await((s=v.value)==null?void 0:s.validate()),e.value.id==""?await G(e.value):e.value.id!=""&&await H(e.value),(t=m.value)==null||t.close(),B("success")}catch(r){return r}},C=()=>{B("close")};return y({open:(s,t)=>{var r;b(),s=="add"?(e.value={id:"",category_id:"",content:"",sort:0,status:1},d.value="\u65B0\u589E\u95EE\u9898\u793A\u4F8B"):s=="edit"&&(Object.keys(e.value).map(c=>{e.value[c]=t[c]}),d.value="\u7F16\u8F91\u95EE\u9898\u793A\u4F8B"),(r=m.value)==null||r.open()}}),(s,t)=>{const r=D,c=L,p=I,x=U,S=q,k=N,Q=P;return f(),F("div",K,[l(j,{ref_key:"popupRef",ref:m,title:u(d),async:!0,width:"550px",onConfirm:A,onClose:C},{default:n(()=>[l(Q,{class:"ls-form",ref_key:"formRef",ref:v,rules:V,model:u(e),"label-width":"90px"},{default:n(()=>[l(p,{label:"\u793A\u4F8B\u7C7B\u76EE",prop:"category_id"},{default:n(()=>[l(c,{class:"w-full",placeholder:"\u8BF7\u9009\u62E9",modelValue:u(e).category_id,"onUpdate:modelValue":t[0]||(t[0]=a=>u(e).category_id=a)},{default:n(()=>[(f(!0),F(J,null,T(u(g),(a,R)=>(f(),W(r,{key:R,value:a.id,label:a.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u793A\u4F8B\u5185\u5BB9",prop:"content"},{default:n(()=>[l(x,{modelValue:u(e).content,"onUpdate:modelValue":t[1]||(t[1]=a=>u(e).content=a),type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"\u8BF7\u8F93\u5165\u793A\u4F8B\u5185\u5BB9",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(p,{label:"\u6392\u5E8F",prop:"sort"},{default:n(()=>[h("div",null,[l(S,{modelValue:u(e).sort,"onUpdate:modelValue":t[2]||(t[2]=a=>u(e).sort=a),min:0,max:9999},null,8,["modelValue"]),M])]),_:1}),l(p,{label:"\u72B6\u6001",prop:"sort"},{default:n(()=>[l(k,{modelValue:u(e).status,"onUpdate:modelValue":t[3]||(t[3]=a=>u(e).status=a),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{le as _,oe as d,ae as e,ue as g};
