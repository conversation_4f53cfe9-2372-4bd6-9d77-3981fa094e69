import{C as U,D as K,I as j,J as q,w as z,F as J,K as M,L as Q,M as W}from"./element-plus.5bcb7c8a.js";import{P as G}from"./index.324d704f.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{u as X}from"./usePaging.b48cb079.js";import{f as Y,d as A}from"./manage.86089de9.js";import{_ as Z}from"./data-view.vue_vue_type_script_setup_true_lang.c983bce2.js";import{f as _}from"./index.850efb0d.js";import{d as uu,s as eu,a0 as ou,b as g,r as tu,o as w,c as lu,W as u,Q as t,a as c,u as l,U as p,R as au,P as nu,V as k,j as su,F as ru}from"./@vue.a11433a6.js";const iu={class:"line-clamp-3"},Eu={class:"line-clamp-3"},du={class:"flex justify-end mt-4"},bu=uu({__name:"trainDataPop",props:{kb_id:{type:Number,default:-1},fd_id:{type:Number,default:-1}},emits:["close"],setup(v,{expose:y,emit:mu}){const f=v,B=eu(),r=ou({current:{},show:!1}),R=g({get(){return f.kb_id},set(n){return n}}),V=g({get(){return f.fd_id},set(n){return n}}),d=tu({kb_id:R,fd_id:V,keyword:"",status:""}),{pager:m,getLists:F,resetPage:h,resetParams:N}=X({fetchFun:Y,params:d.value}),L=async()=>{B.value.open(),F()},O=n=>{r.current=n,r.show=!0},P=async n=>{try{await _.confirm("\u5220\u9664\u6570\u636E\u4F1A\u5F71\u54CD\u5230\u7528\u6237\u524D\u53F0\u667A\u80FD\u4F53\u7684\u6B63\u5E38\u4F7F\u7528\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C\uFF01"),await A({uuid:n.uuid}),F()}catch(e){if(e.msg==="KB_RELATED_ROBOTS_ONLINE"){console.log("\u{1F50D} [\u540E\u53F0\u77E5\u8BC6\u5E93\u6570\u636E\u5220\u9664] \u68C0\u6D4B\u5230KB_RELATED_ROBOTS_ONLINE\u9519\u8BEF"),console.log("\u{1F4CA} [\u540E\u53F0\u77E5\u8BC6\u5E93\u6570\u636E\u5220\u9664] \u5173\u8054\u667A\u80FD\u4F53\u6570\u636E:",e.data);const i=(e.data||{}).related_robots||[];console.log("\u{1F4DD} [\u540E\u53F0\u77E5\u8BC6\u5E93\u6570\u636E\u5220\u9664] \u667A\u80FD\u4F53\u5217\u8868:",i);const s=i.filter(a=>a&&a.is_online);if(console.log("\u{1F7E2} [\u540E\u53F0\u77E5\u8BC6\u5E93\u6570\u636E\u5220\u9664] \u5DF2\u4E0A\u67B6\u667A\u80FD\u4F53:",s),s.length===0)throw console.warn("\u26A0\uFE0F [\u540E\u53F0\u77E5\u8BC6\u5E93\u6570\u636E\u5220\u9664] \u6CA1\u6709\u627E\u5230\u5DF2\u4E0A\u67B6\u7684\u667A\u80FD\u4F53\uFF0C\u4F46\u6536\u5230\u4E86KB_RELATED_ROBOTS_ONLINE\u9519\u8BEF"),e;const E=`\u6B64\u77E5\u8BC6\u5E93\u6570\u636E\u88AB\u4EE5\u4E0B\u5DF2\u4E0A\u67B6\u7684\u667A\u80FD\u4F53\u4F7F\u7528\uFF1A
${s.map(a=>`\u2022 ${a.name||"\u672A\u77E5\u667A\u80FD\u4F53"}`).join(`
`)}

\u5220\u9664\u540E\u8FD9\u4E9B\u667A\u80FD\u4F53\u5C06\u81EA\u52A8\u4ECE\u5E7F\u573A\u4E0B\u67B6\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F`;if(await _.confirm(E,"\u77E5\u8BC6\u5E93\u6570\u636E\u5220\u9664\u786E\u8BA4"))try{await A({uuid:n.uuid,confirm_offline:!0}),_.msgSuccess("\u6570\u636E\u5DF2\u5220\u9664\uFF0C\u5173\u8054\u7684\u667A\u80FD\u4F53\u5DF2\u4ECE\u5E7F\u573A\u4E0B\u67B6"),F()}catch(a){console.error("\u5220\u9664\u5931\u8D25:",a)}}else console.error("\u5220\u9664\u5931\u8D25:",e)}};return y({open:L}),(n,e)=>{const C=U,i=K,s=j,D=q,E=z,b=J,a=M,T=Q,x=H,$=G,S=W;return w(),lu(ru,null,[u($,{ref_key:"popRef",ref:B,width:"900px",title:"\u8BAD\u7EC3\u6570\u636E",onClose:e[3]||(e[3]=o=>n.$emit("close"))},{default:t(()=>[c("div",null,[u(b,{inline:""},{default:t(()=>[u(i,null,{default:t(()=>[u(C,{modelValue:l(d).keyword,"onUpdate:modelValue":e[0]||(e[0]=o=>l(d).keyword=o),class:"w-[250px]",placeholder:"\u8F93\u5165\u95EE\u9898/\u56DE\u7B54\u5185\u5BB9\u5173\u952E\u8BCD\u8FDB\u884C\u641C\u7D22"},null,8,["modelValue"])]),_:1}),u(i,null,{default:t(()=>[u(D,{modelValue:l(d).status,"onUpdate:modelValue":e[1]||(e[1]=o=>l(d).status=o)},{default:t(()=>[u(s,{label:"\u5168\u90E8",value:""}),u(s,{label:"\u7B49\u5F85\u5B66\u4E60",value:"0"}),u(s,{label:"\u5B66\u4E60\u4E2D",value:"1"}),u(s,{label:"\u5B66\u4E60\u5B8C\u6210",value:"2"}),u(s,{label:"\u5931\u8D25",value:"3"})]),_:1},8,["modelValue"])]),_:1}),u(i,null,{default:t(()=>[u(E,{onClick:l(h),type:"primary"},{default:t(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),u(E,{onClick:l(N)},{default:t(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1})]),c("div",null,[au((w(),nu(T,{class:"mt-4",size:"large",data:l(m).lists},{default:t(()=>[u(a,{label:"\u6587\u6863\u5185\u5BB9",prop:"question"},{default:t(({row:o})=>[c("div",iu,k(o.question),1)]),_:1}),u(a,{label:"\u8865\u5145\u5185\u5BB9",prop:"answer"},{default:t(({row:o})=>[c("div",Eu,k(o.answer),1)]),_:1}),u(a,{label:"\u5B66\u4E60\u72B6\u6001",prop:"status_msg"}),u(a,{label:"\u6D88\u8017\u7535\u529B\u503C",prop:"tokens"}),u(a,{label:"\u6700\u540E\u66F4\u65B0\u65F6\u95F4",prop:"update_time"}),u(a,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:t(({row:o})=>[u(E,{type:"primary",link:"",onClick:I=>O(o)},{default:t(()=>[p(" \u67E5\u770B ")]),_:2},1032,["onClick"]),u(E,{type:"danger",link:"",onClick:I=>P(o)},{default:t(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,l(m).loading]]),c("div",du,[u(x,{modelValue:l(m),"onUpdate:modelValue":e[2]||(e[2]=o=>su(m)?m.value=o:null),onChange:l(F)},null,8,["modelValue","onChange"])])])]),_:1},512),u(Z,{"model-value":l(r).current,"onUpdate:modelValue":e[4]||(e[4]=o=>l(r).current=o),show:l(r).show,"onUpdate:show":e[5]||(e[5]=o=>l(r).show=o)},null,8,["model-value","show"])],64)}}});export{bu as _};
