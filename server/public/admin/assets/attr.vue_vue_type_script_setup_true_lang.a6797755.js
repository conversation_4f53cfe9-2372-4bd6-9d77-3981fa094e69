import{Q as F,R as v,x,y as A,D as w,F as y}from"./element-plus.5bcb7c8a.js";import{_ as r}from"./menu-set.vue_vue_type_script_setup_true_lang.581140ce.js";import{u as C}from"./useMenu.4471f007.js";import{d as N,b as U,o as g,c as R,W as e,Q as l,u as t,j as k,U as p,F as D,a as I}from"./@vue.a11433a6.js";const T=I("div",{class:"title flex items-center before:w-[3px] before:h-[14px] before:block before:bg-primary before:mr-2"}," pc\u5BFC\u822A\u8BBE\u7F6E ",-1),O=N({__name:"attr",props:{modelValue:{type:Object,default:()=>({nav:[],menu:{}})}},emits:["update:modelValue"],setup(_,{emit:f}){const c=_,i=f,{selectActive:n}=C(),u=U({get(){return c.modelValue},set(m){i("update:modelValue",m)}});return(m,a)=>{const s=F,b=v,E=w,d=x,V=A,B=y;return g(),R(D,null,[T,e(B,{class:"mt-4","label-width":"70px"},{default:l(()=>[e(V,{modelValue:t(n),"onUpdate:modelValue":a[3]||(a[3]=o=>k(n)?n.value=o:null)},{default:l(()=>[e(d,{label:"\u4E3B\u5BFC\u822A\u8BBE\u7F6E",name:"nav"},{default:l(()=>[e(E,{label:"\u56FE\u6807\u663E\u793A"},{default:l(()=>[e(b,{modelValue:t(u).showNavIcon,"onUpdate:modelValue":a[0]||(a[0]=o=>t(u).showNavIcon=o),class:"ml-4"},{default:l(()=>[e(s,{label:!0},{default:l(()=>[p("\u663E\u793A")]),_:1}),e(s,{label:!1},{default:l(()=>[p("\u4E0D\u663E\u793A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(r,{modelValue:t(u).nav,"onUpdate:modelValue":a[1]||(a[1]=o=>t(u).nav=o),type:"nav"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u83DC\u5355\u8BBE\u7F6E",name:"menu"},{default:l(()=>[e(r,{modelValue:t(u).menu,"onUpdate:modelValue":a[2]||(a[2]=o=>t(u).menu=o),type:"menu"},null,8,["modelValue"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}});export{O as _};
