import{C as X,D as Y,I as Z,J as ee,w as te,F as ae,G as le,K as oe,b as ue,t as se,L as ie,M as ne}from"./element-plus.5bcb7c8a.js";import{_ as re}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{d as me,r as w,s as k,i as de,aj as pe,o as u,c as P,W as e,Q as a,a8 as ce,u as s,F as _e,a7 as fe,P as i,U as p,R as c,a as h,V as ve,j as ge,T as L,n as V}from"./@vue.a11433a6.js";import{u as Ce}from"./usePaging.b48cb079.js";import{_ as be}from"./addPop.vue_vue_type_script_setup_true_lang.1dc543a3.js";import{_ as we}from"./adjustClassPop.vue_vue_type_script_setup_true_lang.38ac3fd3.js";import{j as ye,h as Fe,k as Ee,l as ke,m as he}from"./music.24c0ba5c.js";import{f as j}from"./index.850efb0d.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./picker.9a1dad65.js";import"./index.4de0c800.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const Ve={class:"flex justify-end mt-4"},Bt=me({__name:"manage",setup(Be){const n=w({name:"",status:"",category_id:""}),r=w(!1),y=k(),B=k(),F=k(),S=w(),U=async()=>{r.value=!0,await V(),y.value.open()},$=async o=>{r.value=!0,await V(),y.value.open(o)},M=async()=>{r.value=!0,await V();const o=F.value.getSelectionRows().map(l=>l.id);B.value.open(o)},T=async()=>{S.value=await Fe()},I=async o=>{await Ee({id:o})},K=async o=>{await j.confirm("\u786E\u5B9A\u5220\u9664\uFF1F"),await ke({id:o}),_()},N=async()=>{const o=F.value.getSelectionRows().map(l=>l.id);await j.confirm("\u662F\u5426\u786E\u8BA4\u6279\u91CF\u5220\u9664\uFF01"),await he({id:o}),_()},E=w(!1),A=o=>{console.log(o),E.value=o.length!=0},{pager:g,getLists:_,resetPage:x,resetParams:q}=Ce({fetchFun:ye,params:n.value});return de(async()=>{await _(),await T()}),(o,l)=>{const z=X,C=Y,b=Z,D=ee,d=te,G=ae,R=le,m=oe,J=ue,O=se,Q=ie,W=re,f=pe("perms"),H=ne;return u(),P("div",null,[e(R,{class:"!border-none",shadow:"never"},{default:a(()=>[e(G,{ref:"formRef",class:"mb-[-16px]",model:n.value,inline:!0},{default:a(()=>[e(C,{label:"\u97F3\u4E50\u540D\u79F0"},{default:a(()=>[e(z,{class:"w-[280px]",modelValue:n.value.name,"onUpdate:modelValue":l[0]||(l[0]=t=>n.value.name=t),placeholder:"\u8BF7\u8F93\u5165\u97F3\u4E50\u540D\u79F0",clearable:"",onKeyup:ce(s(x),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(C,{label:"\u6240\u5C5E\u5206\u7C7B"},{default:a(()=>[e(D,{class:"w-[280px]",modelValue:n.value.category_id,"onUpdate:modelValue":l[1]||(l[1]=t=>n.value.category_id=t)},{default:a(()=>[e(b,{label:"\u65E0\u5206\u7C7B",value:0}),(u(!0),P(_e,null,fe(S.value,(t,v)=>(u(),i(b,{key:v,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(C,{label:"\u72B6\u6001"},{default:a(()=>[e(D,{class:"w-[280px]",modelValue:n.value.status,"onUpdate:modelValue":l[2]||(l[2]=t=>n.value.status=t)},{default:a(()=>[e(b,{label:"\u5F00\u542F",value:"1"}),e(b,{label:"\u5173\u95ED",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(C,null,{default:a(()=>[e(d,{type:"primary",onClick:s(x)},{default:a(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(d,{onClick:s(q)},{default:a(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(R,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[c((u(),i(d,{type:"primary",onClick:U},{default:a(()=>[p("+ \u65B0\u589E\u97F3\u4E50")]),_:1})),[[f,["digital.music/add"]]]),c((u(),i(d,{onClick:M,disabled:!E.value},{default:a(()=>[p("\u8C03\u6574\u5206\u7C7B")]),_:1},8,["disabled"])),[[f,["digital.music/batchEdit"]]]),c((u(),i(d,{onClick:N,disabled:!E.value},{default:a(()=>[p("\u6279\u91CF\u5220\u9664")]),_:1},8,["disabled"])),[[f,["digital.music/batchDel"]]]),c((u(),i(Q,{class:"mt-2",size:"large",data:s(g).lists,ref_key:"tableRef",ref:F,onSelectionChange:A},{default:a(()=>[e(m,{type:"selection",width:"55"}),e(m,{label:"\u5C01\u9762","min-width":"120"},{default:a(({row:t})=>[e(J,{"preview-teleported":!0,"preview-src-list":[t.cover],src:t.cover,class:"w-[80px]"},null,8,["preview-src-list","src"])]),_:1}),e(m,{label:"\u97F3\u4E50",prop:"name","min-width":"200"}),e(m,{label:"\u6240\u5C5E\u5206\u7C7B","min-width":"100"},{default:a(({row:t})=>[h("span",null,ve(t.category_name||"\u65E0\u5206\u7C7B"),1)]),_:1}),e(m,{label:"\u72B6\u6001",prop:"status","min-width":"100"},{default:a(({row:t})=>[c(e(O,{onChange:v=>I(t.id),modelValue:t.status,"onUpdate:modelValue":v=>t.status=v,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"]),[[f,["digital.music/status"]]])]),_:1}),e(m,{label:"\u6392\u5E8F",prop:"sort","min-width":"100"}),e(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"120"}),e(m,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:a(({row:t})=>[h("div",null,[c((u(),i(d,{type:"primary",link:"",onClick:v=>$(t)},{default:a(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[f,["digital.music/edit"]]]),c((u(),i(d,{onClick:v=>K(t.id),type:"danger",link:""},{default:a(()=>[p("\u5220\u9664")]),_:2},1032,["onClick"])),[[f,["digital.music/del"]]])])]),_:1})]),_:1},8,["data"])),[[H,s(g).loading]]),h("div",Ve,[e(W,{modelValue:s(g),"onUpdate:modelValue":l[3]||(l[3]=t=>ge(g)?g.value=t:null),onChange:s(_)},null,8,["modelValue","onChange"])])]),_:1}),r.value?(u(),i(be,{key:0,onClose:l[4]||(l[4]=t=>r.value=!1),onSuccess:l[5]||(l[5]=()=>{r.value=!1,s(_)()}),ref_key:"addPopRef",ref:y},null,512)):L("",!0),r.value?(u(),i(we,{key:1,ref_key:"adjustPopRef",ref:B,onSuccess:l[6]||(l[6]=()=>{r.value=!1,s(_)()})},null,512)):L("",!0)])}}});export{Bt as default};
