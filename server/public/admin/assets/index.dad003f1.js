import{x as u,y as c,G as f}from"./element-plus.5bcb7c8a.js";import{_ as y}from"./index.4de0c800.js";import{d as i,r as x,o as m,c as a,W as e,Q as p,u as b,j as v,F as g,a7 as E}from"./@vue.a11433a6.js";import{_ as C}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";const F={class:"material-index"},V=i({name:"material"}),k=i({...V,setup(z){const n=[{type:"image",name:"\u56FE\u7247"},{type:"video",name:"\u89C6\u9891"},{type:"audio",name:"\u97F3\u4E50"}],o=x("image");return(T,r)=>{const s=y,_=u,l=c,d=f;return m(),a("div",F,[e(d,{class:"!border-none",shadow:"never"},{default:p(()=>[e(l,{modelValue:b(o),"onUpdate:modelValue":r[0]||(r[0]=t=>v(o)?o.value=t:null)},{default:p(()=>[(m(),a(g,null,E(n,t=>e(_,{label:t.name,name:t.type,index:t.type,key:t.type,lazy:""},{default:p(()=>[e(s,{type:t.type,mode:"page","file-size":"120px",limit:-1,"page-size":20},null,8,["type"])]),_:2},1032,["label","name","index"])),64))]),_:1},8,["modelValue"])]),_:1})])}}});const gt=C(k,[["__scopeId","data-v-5e30f9ed"]]);export{gt as default};
