import{I,J as q,D as G,w as J,F as K,G as M,K as O,t as Q,L as W,M as H}from"./element-plus.5bcb7c8a.js";import{_ as X}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as Y,b as Z}from"./index.850efb0d.js";import{_ as ee}from"./index.vue_vue_type_script_setup_true_lang.67cfcc66.js";import{u as te}from"./usePaging.b48cb079.js";import{b as y,d as oe,c as ae}from"./draw_example.8e969625.js";import{_ as le}from"./edit.vue_vue_type_script_setup_true_lang.811dafe2.js";import{d as V,r as B,s as ne,a0 as se,aj as ie,o as s,c as re,W as e,Q as t,u as o,a as E,U as c,R as _,P as i,j as me,T as pe,n as ue}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const de={class:"w-[200px]"},ce={class:"flex justify-end mt-4"},_e=V({name:"drawPromptExample"}),lt=V({..._e,setup(fe){const C=B(!1),b=ne(),f=se({status:"",model:"sd"}),g=B([]),P=n=>{g.value=n},h=async(n,l)=>{var m;C.value=!0,await ue(),(m=b.value)==null||m.open(n,l)},k=async n=>{await Y.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await oe({id:n}),w()},$=n=>{ae({id:n})},{pager:r,getLists:w,resetPage:S,resetParams:D}=te({fetchFun:y,params:f});return w(),(n,l)=>{const m=I,R=q,x=G,p=J,T=ee,U=K,F=M,z=Z,u=O,A=Q,L=W,N=X,v=ie("perms"),j=H;return s(),re("div",null,[e(F,{class:"!border-none",shadow:"never"},{default:t(()=>[e(U,{ref:"formRef",class:"mb-[-16px]",model:o(f),inline:!0},{default:t(()=>[e(x,{label:"\u793A\u4F8B\u72B6\u6001"},{default:t(()=>[E("div",de,[e(R,{modelValue:o(f).status,"onUpdate:modelValue":l[0]||(l[0]=a=>o(f).status=a)},{default:t(()=>[e(m,{label:"\u5168\u90E8",value:""}),e(m,{label:"\u5F00\u542F",value:1}),e(m,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])])]),_:1}),e(x,null,{default:t(()=>[e(p,{type:"primary",onClick:o(S)},{default:t(()=>[c("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(p,{onClick:o(D)},{default:t(()=>[c("\u91CD\u7F6E")]),_:1},8,["onClick"]),e(T,{class:"ml-2.5","fetch-fun":o(y),params:o(f),"page-size":o(r).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),e(F,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[E("div",null,[_((s(),i(p,{type:"primary",onClick:l[1]||(l[1]=a=>h("add"))},{icon:t(()=>[e(z,{name:"el-icon-Plus"})]),default:t(()=>[c(" \u65B0\u589E ")]),_:1})),[[v,["draw.draw_prompt_example/add"]]]),_((s(),i(p,{type:"default",plain:!0,disabled:!o(g).length,onClick:l[2]||(l[2]=a=>k(o(g).map(d=>d.id)))},{default:t(()=>[c(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])),[[v,["draw.draw_prompt_example/delete"]]])]),_((s(),i(L,{size:"large",class:"mt-4",data:o(r).lists,onSelectionChange:P},{default:t(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"\u793A\u4F8B\u6807\u9898",prop:"prompt","min-width":"140"}),e(u,{label:"\u793A\u4F8B\u5185\u5BB9",prop:"prompt_en","min-width":"140"}),_((s(),i(u,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:a})=>[e(A,{modelValue:a.status,"onUpdate:modelValue":d=>a.status=d,"active-value":1,"inactive-value":0,onChange:d=>$(a.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})),[[v,["draw.draw_prompt_example/status"]]]),e(u,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(u,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:t(({row:a})=>[_((s(),i(p,{type:"primary",link:!0,onClick:d=>h("edit",a.id)},{default:t(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[v,["draw.draw_prompt_example/edit"]]]),_((s(),i(p,{type:"danger",link:!0,onClick:d=>k([a.id])},{default:t(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[v,["draw.draw_prompt_example/delete"]]])]),_:1})]),_:1},8,["data"])),[[j,o(r).loading]]),E("div",ce,[e(N,{modelValue:o(r),"onUpdate:modelValue":l[3]||(l[3]=a=>me(r)?r.value=a:null),onChange:o(w)},null,8,["modelValue","onChange"])])]),_:1}),o(C)?(s(),i(le,{key:0,ref_key:"editRef",ref:b,onSuccess:o(w),onClose:l[4]||(l[4]=a=>C.value=!1)},null,8,["onSuccess"])):pe("",!0)])}}});export{lt as default};
