import{a4 as O,C as J,D as Q,I as W,J as z,t as G,F as H}from"./element-plus.5bcb7c8a.js";import{_ as K}from"./picker.9a1dad65.js";import{P as M}from"./index.324d704f.js";import{u as X}from"./useDictOptions.583d6eb9.js";import{r as m}from"./index.850efb0d.js";import{r as Y}from"./role.51b5659b.js";import{e as Z}from"./post.0e53ce28.js";import{d as $}from"./department.735238be.js";import{d as ee,s as A,r as ue,b as le,a0 as y,o as i,c as V,W as o,Q as t,u as l,a as p,F as k,a7 as x,P as c,T as U}from"./@vue.a11433a6.js";function Be(s){return m.get({url:"/auth.admin/lists",params:s},{ignoreCancelToken:!0})}function ae(s){return m.post({url:"/auth.admin/add",params:s})}function oe(s){return m.post({url:"/auth.admin/edit",params:s})}function Ee(s){return m.post({url:"/auth.admin/delete",params:s})}function te(s){return m.get({url:"/auth.admin/detail",params:s})}const re={class:"edit-popup"},se=p("div",{class:"form-tips"},"\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A100*100px\uFF0C\u652F\u6301jpg\uFF0Cjpeg\uFF0Cpng\u683C\u5F0F",-1),de=p("div",{class:"form-tips"},"\u5141\u8BB8\u591A\u4EBA\u540C\u65F6\u5728\u7EBF\u767B\u5F55",-1),Ve=ee({__name:"edit",emits:["success","close"],setup(s,{expose:j,emit:h}){const v=h,w=A(),f=A(),_=ue("add"),q=le(()=>_.value=="edit"?"\u7F16\u8F91\u7BA1\u7406\u5458":"\u65B0\u589E\u7BA1\u7406\u5458"),u=y({id:"",account:"",name:"",dept_id:[],jobs_id:[],role_id:[],avatar:"",password:"",password_confirm:"",disable:0,multipoint_login:1,root:0}),g=(n,e,r)=>{u.password&&(e||r(new Error("\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801")),e!==u.password&&r(new Error("\u4E24\u6B21\u8F93\u5165\u5BC6\u7801\u4E0D\u4E00\u81F4!"))),r()},F=y({account:[{required:!0,message:"\u8BF7\u8F93\u5165\u8D26\u53F7",trigger:["blur"]}],name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:["blur"]}],role_id:[{type:"array",required:!0,message:"\u8BF7\u9009\u62E9\u89D2\u8272",trigger:["blur"]}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",trigger:["blur"]}],password_confirm:[{required:!0,message:"\u8BF7\u8F93\u5165\u786E\u8BA4\u5BC6\u7801",trigger:["blur"]},{validator:g,trigger:"blur"}]}),{optionsData:b}=X({role:{api:Y},jobs:{api:Z},dept:{api:$}}),R=async()=>{var n,e;await((n=w.value)==null?void 0:n.validate()),_.value=="edit"?await oe(u):await ae(u),(e=f.value)==null||e.close(),v("success")},S=(n="add")=>{var e;_.value=n,(e=f.value)==null||e.open()},T=async n=>{F.password=[],F.password_confirm=[{validator:g,trigger:"blur"}];const e=await te({id:n.id});for(const r in u)e[r]!=null&&e[r]!=null&&(u[r]=e[r])},I=()=>{v("close")};return j({open:S,setFormData:T}),(n,e)=>{const r=J,d=Q,N=K,P=O,B=W,C=z,D=G,L=H;return i(),V("div",re,[o(M,{ref_key:"popupRef",ref:f,title:l(q),async:!0,width:"550px",onConfirm:R,onClose:I},{default:t(()=>[o(L,{ref_key:"formRef",ref:w,model:l(u),"label-width":"84px",rules:l(F)},{default:t(()=>[o(d,{label:"\u8D26\u53F7",prop:"account"},{default:t(()=>[o(r,{modelValue:l(u).account,"onUpdate:modelValue":e[0]||(e[0]=a=>l(u).account=a),disabled:l(u).root==1,placeholder:"\u8BF7\u8F93\u5165\u8D26\u53F7",clearable:""},null,8,["modelValue","disabled"])]),_:1}),o(d,{label:"\u5934\u50CF"},{default:t(()=>[p("div",null,[p("div",null,[o(N,{modelValue:l(u).avatar,"onUpdate:modelValue":e[1]||(e[1]=a=>l(u).avatar=a),limit:1},null,8,["modelValue"])]),se])]),_:1}),o(d,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[o(r,{modelValue:l(u).name,"onUpdate:modelValue":e[2]||(e[2]=a=>l(u).name=a),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:""},null,8,["modelValue"])]),_:1}),o(d,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"dept_id"},{default:t(()=>[o(P,{class:"flex-1",modelValue:l(u).dept_id,"onUpdate:modelValue":e[3]||(e[3]=a=>l(u).dept_id=a),data:l(b).dept,clearable:"",multiple:"","node-key":"id",props:{value:"id",label:"name",disabled(a){return a.status!==1}},"check-strictly":"","default-expand-all":!0,placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u90E8\u95E8"},null,8,["modelValue","data","props"])]),_:1}),o(d,{label:"\u5C97\u4F4D",prop:"jobs_id"},{default:t(()=>[o(C,{class:"flex-1",modelValue:l(u).jobs_id,"onUpdate:modelValue":e[4]||(e[4]=a=>l(u).jobs_id=a),clearable:"",multiple:"",placeholder:"\u8BF7\u9009\u62E9\u5C97\u4F4D"},{default:t(()=>[(i(!0),V(k,null,x(l(b).jobs,(a,E)=>(i(),c(B,{key:E,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(d,{label:"\u89D2\u8272",prop:"role_id"},{default:t(()=>[o(C,{modelValue:l(u).role_id,"onUpdate:modelValue":e[5]||(e[5]=a=>l(u).role_id=a),disabled:l(u).root==1,class:"flex-1",multiple:"",placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272",clearable:""},{default:t(()=>[l(u).root==1?(i(),c(B,{key:0,label:"\u7CFB\u7EDF\u7BA1\u7406\u5458",value:0})):U("",!0),(i(!0),V(k,null,x(l(b).role,(a,E)=>(i(),c(B,{key:E,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),o(d,{label:"\u5BC6\u7801",prop:"password"},{default:t(()=>[o(r,{modelValue:l(u).password,"onUpdate:modelValue":e[6]||(e[6]=a=>l(u).password=a),"show-password":"",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801"},null,8,["modelValue"])]),_:1}),o(d,{label:"\u786E\u8BA4\u5BC6\u7801",prop:"password_confirm"},{default:t(()=>[o(r,{modelValue:l(u).password_confirm,"onUpdate:modelValue":e[7]||(e[7]=a=>l(u).password_confirm=a),"show-password":"",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u786E\u8BA4\u5BC6\u7801"},null,8,["modelValue"])]),_:1}),l(u).root!=1?(i(),c(d,{key:0,label:"\u7BA1\u7406\u5458\u72B6\u6001"},{default:t(()=>[o(D,{modelValue:l(u).disable,"onUpdate:modelValue":e[8]||(e[8]=a=>l(u).disable=a),"active-value":0,"inactive-value":1},null,8,["modelValue"])]),_:1})):U("",!0),o(d,{label:"\u591A\u5904\u767B\u5F55"},{default:t(()=>[p("div",null,[o(D,{modelValue:l(u).multipoint_login,"onUpdate:modelValue":e[9]||(e[9]=a=>l(u).multipoint_login=a),"active-value":1,"inactive-value":0},null,8,["modelValue"]),de])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title"])])}}});export{Ve as _,Be as a,oe as b,Ee as c};
