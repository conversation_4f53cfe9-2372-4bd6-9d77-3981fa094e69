import{b as g,w as b}from"./element-plus.5bcb7c8a.js";import{_ as y}from"./index_arrow-right02.3a32185e.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";import{u as w}from"./index.850efb0d.js";import{d as k,b as I,a4 as S,o as e,c as p,a as t,F as E,a7 as B,V as a,W as i,u as n,Q as m,bk as C,bj as j}from"./@vue.a11433a6.js";import{_ as A}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const _=o=>(C("data-v-2cbf1516"),o=o(),j(),o),N={class:"py-[40px]"},F={class:"w-[1160px] mx-auto"},L={class:"flex flex-col items-center"},V={class:"flex-1 my-[30px] px-[20px]"},D={class:"flex justify-center text-center"},z={class:"text-[24px] font-bold"},Q={class:"mt-[20px] text-[16px]"},U={class:"flex-1 px-[20px]"},W={class:"flex justify-center"},q={class:"mt-[90px]"},G=_(()=>t("div",{class:"flex justify-center items-center w-[50px] h-[50px] rounded-full bg-white"},[t("img",{src:y,class:"w-[24px] h-[24px]",alt:""})],-1)),H=_(()=>t("span",{class:"ml-4"},"\u9A6C\u4E0A\u4F53\u9A8C",-1)),J=k({__name:"content",props:{prop:{}},setup(o){const l=o,d=I(()=>l.prop.data.filter(r=>r.isShow)),{getImageUrl:x}=w();return(r,K)=>{const u=g,f=b,h=S("NuxtLink");return e(),p("div",N,[t("div",F,[(e(!0),p(E,null,B(n(d),(s,v)=>{var c;return e(),p("div",{class:"mb-[20px]",key:v},[t("div",L,[t("div",V,[t("div",D,[t("div",null,[t("div",z,a(s.title),1),t("div",Q,a(s.subtitle),1)])])]),t("div",U,[t("div",W,[i(u,{class:"w-[1200px]",fit:"cover",src:n(x)(s.image)},null,8,["src"])])]),t("div",q,[i(h,{to:(c=s.link)==null?void 0:c.path},{default:m(()=>[i(f,{type:"primary",class:"enter-btn hover-to-right",size:"large"},{default:m(()=>[G,H]),_:1})]),_:2},1032,["to"])])])])}),128))])])}}});const jt=A(J,[["__scopeId","data-v-2cbf1516"]]);export{jt as default};
