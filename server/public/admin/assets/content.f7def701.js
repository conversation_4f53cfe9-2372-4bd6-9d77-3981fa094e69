import{b as _}from"./index.850efb0d.js";import c from"./decoration-img.16e6b284.js";import{d as x,o as t,c as e,a as i,V as m,T as p,F as a,a7 as l,W as s}from"./@vue.a11433a6.js";import{_ as f}from"./vue-drag-resize.527c6620.js";import"./element-plus.5bcb7c8a.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";const u={class:"my-service"},h={key:0,class:"title px-[15px] py-[10px]"},y={key:1,class:"flex flex-wrap pt-[20px] pb-[10px]"},v={class:"mt-[7px]"},g={key:2},b={class:"ml-[10px] flex-1"},k=x({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(o){return(w,V)=>{const d=_;return t(),e("div",u,[o.content.showTitle?(t(),e("div",h,[i("div",null,m(o.content.title),1)])):p("",!0),o.content.style==1?(t(),e("div",y,[(t(!0),e(a,null,l(o.content.data,(r,n)=>(t(),e("div",{key:n,class:"flex flex-col items-center w-1/4 mb-[15px]"},[s(c,{width:"26px",height:"26px",src:r.image,alt:""},null,8,["src"]),i("div",v,m(r.name),1)]))),128))])):p("",!0),o.content.style==2?(t(),e("div",g,[(t(!0),e(a,null,l(o.content.data,(r,n)=>(t(),e("div",{key:n,class:"flex items-center border-b border-[#e5e5e5] h-[50px] px-[12px]"},[s(c,{width:"24px",height:"24px",src:r.image,alt:""},null,8,["src"]),i("div",b,m(r.name),1),i("div",null,[s(d,{name:"el-icon-ArrowRight"})])]))),128))])):p("",!0)])}}});const ct=f(k,[["__scopeId","data-v-070ef3cf"]]);export{ct as default};
