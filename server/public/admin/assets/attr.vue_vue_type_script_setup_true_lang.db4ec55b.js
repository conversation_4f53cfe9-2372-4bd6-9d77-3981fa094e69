import{C as c,D as i,F}from"./element-plus.5bcb7c8a.js";import{_ as r}from"./picker.9a1dad65.js";import{d as p,o as f,c as V,W as e,Q as o,a as m}from"./@vue.a11433a6.js";const B=m("div",{class:"form-tips"},"\u5EFA\u8BAE\u56FE\u7247\u5C3A\u5BF8\uFF1A200*200\u50CF\u7D20\uFF1B\u56FE\u7247\u683C\u5F0F\uFF1Ajpg\u3001png\u3001jpeg",-1),A=p({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(t){return(E,l)=>{const a=c,n=i,d=r,s=F;return f(),V("div",null,[e(s,{"label-width":"90px"},{default:o(()=>[e(n,{label:"\u5BA2\u670D\u6807\u9898"},{default:o(()=>[e(a,{class:"w-[400px]",modelValue:t.content.title,"onUpdate:modelValue":l[0]||(l[0]=u=>t.content.title=u)},null,8,["modelValue"])]),_:1}),e(n,{label:"\u670D\u52A1\u65F6\u95F4"},{default:o(()=>[e(a,{class:"w-[400px]",modelValue:t.content.time,"onUpdate:modelValue":l[1]||(l[1]=u=>t.content.time=u)},null,8,["modelValue"])]),_:1}),e(n,{label:"\u8054\u7CFB\u7535\u8BDD"},{default:o(()=>[e(a,{class:"w-[400px]",modelValue:t.content.mobile,"onUpdate:modelValue":l[2]||(l[2]=u=>t.content.mobile=u)},null,8,["modelValue"])]),_:1}),e(n,{label:"\u5BA2\u670D\u4E8C\u7EF4\u7801"},{default:o(()=>[m("div",null,[e(d,{excludeDomain:!0,modelValue:t.content.qrcode,"onUpdate:modelValue":l[3]||(l[3]=u=>t.content.qrcode=u)},null,8,["modelValue"]),B])]),_:1})]),_:1})])}}});export{A as _};
