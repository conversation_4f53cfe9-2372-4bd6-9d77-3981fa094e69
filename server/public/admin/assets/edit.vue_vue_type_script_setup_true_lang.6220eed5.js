import{Y as k,C as A,D as R,t as P,F as U}from"./element-plus.5bcb7c8a.js";import{a as q,e as S}from"./draw_prompt.2e5e04cc.js";import{g as L}from"./draw_prompt_category.b4dd6927.js";import{P as I}from"./index.324d704f.js";import{d as N,s as F,r as i,a0 as j,o as c,c as B,W as t,Q as p,u as o,P as z,a as d,F as O}from"./@vue.a11433a6.js";const Q={class:"edit-popup"},T={class:"w-full"},W=d("div",{class:"form-tips"}," \u6DFB\u52A0\u591A\u4E2A\u5173\u952E\u8BCD\uFF0C\u4E2D\u82F1\u6587\u4EE5&\u9694\u5F00\uFF0C\u6309\u56DE\u8F66\u6362\u884C ",-1),Y={class:"w-full"},G={class:"w-full"},H={class:"w-full"},J=d("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u636E\u8D8A\u5927\u8D8A\u6392\u524D\u9762",-1),oe=N({__name:"edit",emits:["success","close"],setup(K,{expose:C,emit:v}){const _=v,f=F(),n=F(),m=i(""),E=i([]),D=j({multiple:!1,checkStrictly:!0,label:"name",value:"id",children:"children",emitPath:!1}),e=i({id:"",category_id:"",prompt:"",prompt_en:"",model:"doubao",sort:0,status:"1"}),g={prompt:[{required:!0,message:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD",trigger:["blur"]}],prompt_en:[{required:!0,message:"\u8BF7\u8F93\u5165\u82F1\u6587\u5173\u952E\u8BCD",trigger:["blur"]}],category_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7C7B\u76EE",trigger:["blur"]}]},y=async()=>{const r=await L({model:"doubao"});E.value=r},V=async()=>{var r,u;try{await((r=f.value)==null?void 0:r.validate()),e.value.id==""?await q({...e.value,prompt:e.value.prompt.split(`
`)}):e.value.id!=""&&await S(e.value),(u=n.value)==null||u.close(),_("success")}catch(a){return a}},b=()=>{_("close")};return C({open:(r,u)=>{var a;y(),r=="add"?(e.value={id:"",category_id:"",prompt:"",model:"doubao",prompt_en:"",sort:0,status:1},m.value="\u65B0\u589E\u5173\u952E\u8BCD"):r=="edit"&&(Object.keys(e.value).map(s=>{e.value[s]=u[s]}),console.log(e.value,u),m.value="\u7F16\u8F91\u5173\u952E\u8BCD"),(a=n.value)==null||a.open()}}),(r,u)=>{const a=A,s=R,w=k,h=P,x=U;return c(),B("div",Q,[t(I,{ref_key:"popupRef",ref:n,title:o(m),async:!0,width:"550px",onConfirm:V,onClose:b},{default:p(()=>[t(x,{ref_key:"formRef",ref:f,rules:g,model:o(e),"label-width":"100px"},{default:p(()=>[o(e).id?(c(),B(O,{key:1},[t(s,{label:"\u82F1\u6587\u5173\u952E\u8BCD",prop:"prompt_en"},{default:p(()=>[d("div",Y,[t(a,{modelValue:o(e).prompt_en,"onUpdate:modelValue":u[1]||(u[1]=l=>o(e).prompt_en=l),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD"},null,8,["modelValue"])])]),_:1}),t(s,{label:"\u4E2D\u6587\u5173\u952E\u8BCD",prop:"prompt"},{default:p(()=>[d("div",G,[t(a,{modelValue:o(e).prompt,"onUpdate:modelValue":u[2]||(u[2]=l=>o(e).prompt=l),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD"},null,8,["modelValue"])])]),_:1})],64)):(c(),z(s,{key:0,label:"\u5173\u952E\u8BCD",prop:"prompt"},{default:p(()=>[d("div",T,[t(a,{modelValue:o(e).prompt,"onUpdate:modelValue":u[0]||(u[0]=l=>o(e).prompt=l),type:"textarea",autosize:{minRows:8,maxRows:20},placeholder:`\u8BF7\u8F93\u5165\u5173\u952E\u8BCD\uFF0C\u5982\uFF1ASurrealism&\u8D85\u73B0\u5B9E\u4E3B\u4E49\r
Baroque&\u5DF4\u6D1B\u514B\r
modern&\u73B0\u4EE3`},null,8,["modelValue"]),W])]),_:1})),t(s,{label:"\u6240\u5C5E\u7C7B\u76EE",prop:"category_id"},{default:p(()=>[t(w,{class:"w-full",modelValue:o(e).category_id,"onUpdate:modelValue":u[3]||(u[3]=l=>o(e).category_id=l),options:o(E),props:o(D),clearable:!0,filterable:!0},null,8,["modelValue","options","props"])]),_:1}),t(s,{label:"\u6392\u5E8F",prop:"sort"},{default:p(()=>[d("div",H,[t(a,{type:"text",modelValue:o(e).sort,"onUpdate:modelValue":u[4]||(u[4]=l=>o(e).sort=l),min:0,max:9999},null,8,["modelValue"]),J])]),_:1}),t(s,{label:"\u72B6\u6001",prop:"sort"},{default:p(()=>[t(h,{modelValue:o(e).status,"onUpdate:modelValue":u[5]||(u[5]=l=>o(e).status=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{oe as _};
