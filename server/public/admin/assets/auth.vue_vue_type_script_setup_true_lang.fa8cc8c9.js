import{B as j,S as q,D as H,E as I,F as O,M as Q}from"./element-plus.5bcb7c8a.js";import{a as U}from"./role.51b5659b.js";import{P as W}from"./index.324d704f.js";import{v as z}from"./index.850efb0d.js";import{m as G}from"./menu.94b807a3.js";import{d as J,s as f,r as u,a0 as X,o as k,c as Y,W as s,Q as i,R as Z,P as $,u as c,a as y,j as ee,n as x}from"./@vue.a11433a6.js";const te={class:"edit-popup"},re=J({__name:"auth",emits:["success","close"],setup(le,{expose:C,emit:b}){const _=b,o=f(),h=f(),d=f(),g=u(!1),r=u(!0),m=u(!1),v=u([]),p=u([]),a=X({id:"",name:"",desc:"",sort:0,menu_id:[]}),E={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:["blur"]}]},D=()=>{m.value=!0,G().then(e=>{p.value=e,v.value=z(e),x(()=>{w()}),m.value=!1})},R=()=>{var l,n;const e=(l=o.value)==null?void 0:l.getCheckedKeys(),t=(n=o.value)==null?void 0:n.getHalfCheckedKeys();return e==null||e.unshift.apply(e,t),e},w=()=>{a.menu_id.forEach(e=>{x(()=>{var t;(t=o.value)==null||t.setChecked(e,!0,!1)})})},F=e=>{const t=p.value;for(let l=0;l<t.length;l++)o.value.store.nodesMap[t[l].id].expanded=e},A=e=>{var t,l;e?(t=o.value)==null||t.setCheckedKeys(v.value.map(n=>n.id)):(l=o.value)==null||l.setCheckedKeys([])},B=async()=>{var e,t;await((e=h.value)==null?void 0:e.validate()),a.menu_id=R(),await U(a),(t=d.value)==null||t.close(),_("success")},K=()=>{_("close")},S=()=>{var e;(e=d.value)==null||e.open()},V=async e=>{for(const t in a)e[t]!=null&&e[t]!=null&&(a[t]=e[t])};return D(),C({open:S,setFormData:V}),(e,t)=>{const l=j,n=q,T=H,P=I,L=O,M=Q;return k(),Y("div",te,[s(W,{ref_key:"popupRef",ref:d,title:"\u5206\u914D\u6743\u9650",async:!0,width:"550px",onConfirm:B,onClose:K},{default:i(()=>[Z((k(),$(L,{class:"ls-form",ref_key:"formRef",ref:h,rules:E,model:c(a),"label-width":"60px"},{default:i(()=>[s(P,{class:"h-[400px] sm:h-[600px]"},{default:i(()=>[s(T,{label:"\u6743\u9650",prop:"menu_id"},{default:i(()=>[y("div",null,[s(l,{label:"\u5C55\u5F00/\u6298\u53E0",onChange:F}),s(l,{label:"\u5168\u9009/\u4E0D\u5168\u9009",onChange:A}),s(l,{modelValue:c(r),"onUpdate:modelValue":t[0]||(t[0]=N=>ee(r)?r.value=N:null),label:"\u7236\u5B50\u8054\u52A8"},null,8,["modelValue"]),y("div",null,[s(n,{ref_key:"treeRef",ref:o,data:c(p),props:{label:"name",children:"children"},"check-strictly":!c(r),"node-key":"id","default-expand-all":c(g),"show-checkbox":""},null,8,["data","check-strictly","default-expand-all"])])])]),_:1})]),_:1})]),_:1},8,["model"])),[[M,c(m)]])]),_:1},512)])}}});export{re as _};
