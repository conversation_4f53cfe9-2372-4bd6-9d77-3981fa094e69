import{x as T,y as N,D as P,C as I,t as M,w as S,F as j}from"./element-plus.5bcb7c8a.js";import{_ as K}from"./index.4a09b22e.js";import{_ as L}from"./picker.e4cc82a9.js";import{_ as O}from"./picker.9a1dad65.js";import{f as r,b as Q}from"./index.850efb0d.js";import{m as R}from"./@vueuse.a2407f20.js";import{D as W}from"./vuedraggable.2019ddfd.js";import{d as q,b as G,o as h,c as H,W as e,Q as o,a as s,u as m,K as J,P as X,U as g,T as Y,F as Z}from"./@vue.a11433a6.js";const ee=s("div",{class:"title flex items-center before:w-[3px] before:h-[14px] before:block before:bg-primary before:mr-2"},[g(" \u5E95\u90E8\u5BFC\u822A\u8BBE\u7F6E "),s("span",{class:"form-tips ml-[10px] !mt-0"}," \u81F3\u5C11\u6DFB\u52A02\u4E2A\u5BFC\u822A\uFF0C\u6700\u591A\u6DFB\u52A05\u4E2A\u5BFC\u822A ")],-1),le={class:"mb-[18px] max-w-[400px]"},oe={class:"bg-fill-light w-full p-4 mt-4"},ae={class:"upload-btn w-[60px] h-[60px]"},te=s("span",{class:"text-xs leading-5"}," \u672A\u9009\u4E2D ",-1),se={class:"upload-btn w-[60px] h-[60px]"},ue=s("span",{class:"text-xs leading-5"}," \u9009\u4E2D ",-1),ne={class:"flex-1 flex items-center"},de={class:"drag-move cursor-move ml-auto"},p=5,i=2,Ve=q({__name:"attr",props:{modelValue:{type:Object,default:()=>({list:[],style:{}})}},emits:["update:modelValue"],setup(V,{emit:F}){const u=R(V,"modelValue",F),x=G(()=>{var a;return((a=u.value.list)==null?void 0:a.filter(n=>n.is_show=="1"))||[]}),E=()=>{var a;((a=u.value.list)==null?void 0:a.length)<p?u.value.list.push({name:"",selected:"",unselected:"",is_show:"1",link:{}}):r.msgError(`\u6700\u591A\u6DFB\u52A0${p}\u4E2A`)},v=a=>{var n;if(((n=u.value.list)==null?void 0:n.length)<=i)return r.msgError(`\u6700\u5C11\u4FDD\u7559${i}\u4E2A`);u.value.list.splice(a,1)},A=a=>a.relatedContext.index!=0,C=a=>{if(x.value.length<i)return a.is_show="1",r.msgError(`\u6700\u5C11\u663E\u793A${i}\u4E2A`)};return(a,n)=>{const _=Q,b=O,d=P,w=I,B=L,k=M,D=K,U=S,y=T,$=N,z=j;return h(),H(Z,null,[ee,e(z,{class:"mt-4","label-width":"70px"},{default:o(()=>[e($,{"model-value":"content"},{default:o(()=>[e(y,{label:"\u5BFC\u822A\u56FE\u7247",name:"content"},{default:o(()=>{var f;return[s("div",le,[e(m(W),{class:"draggable",modelValue:m(u).list,"onUpdate:modelValue":n[0]||(n[0]=l=>m(u).list=l),animation:"300",draggable:".draggable",handle:".drag-move",move:A},{item:o(({element:l,index:c})=>[e(D,{onClose:t=>v(c),class:J(["max-w-[400px]",{draggable:c!=0}]),"show-close":c!==0},{default:o(()=>[s("div",oe,[e(d,{label:"\u5BFC\u822A\u56FE\u6807"},{default:o(()=>[e(b,{modelValue:l.unselected,"onUpdate:modelValue":t=>l.unselected=t,"upload-class":"bg-body","exclude-domain":"",size:"60px"},{upload:o(()=>[s("div",ae,[e(_,{name:"el-icon-Plus",size:16}),te])]),_:2},1032,["modelValue","onUpdate:modelValue"]),e(b,{modelValue:l.selected,"onUpdate:modelValue":t=>l.selected=t,"exclude-domain":"","upload-class":"bg-body",size:"60px"},{upload:o(()=>[s("div",se,[e(_,{name:"el-icon-Plus",size:16}),ue])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"\u5BFC\u822A\u540D\u79F0"},{default:o(()=>[e(w,{modelValue:l.name,"onUpdate:modelValue":t=>l.name=t,placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"\u94FE\u63A5\u5730\u5740"},{default:o(()=>[e(B,{type:"mobile","is-tab":!0,modelValue:l.link,"onUpdate:modelValue":t=>l.link=t,disabled:c==0},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024),e(d,{label:"\u662F\u5426\u663E\u793A"},{default:o(()=>[s("div",ne,[e(k,{disabled:c==0,modelValue:l.is_show,"onUpdate:modelValue":t=>l.is_show=t,"active-value":"1","inactive-value":"0",onChange:t=>C(l)},null,8,["disabled","modelValue","onUpdate:modelValue","onChange"]),s("div",de,[e(_,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose","show-close","class"])]),_:1},8,["modelValue"])]),((f=m(u).list)==null?void 0:f.length)<p?(h(),X(d,{key:0,"label-width":"0"},{default:o(()=>[e(U,{type:"primary",onClick:E},{default:o(()=>[g(" \u6DFB\u52A0\u5BFC\u822A ")]),_:1})]),_:1})):Y("",!0)]}),_:1})]),_:1})]),_:1})],64)}}});export{Ve as _};
