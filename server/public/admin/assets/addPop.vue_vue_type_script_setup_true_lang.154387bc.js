import{P as F}from"./index.324d704f.js";import{C,D as E,F as w}from"./element-plus.5bcb7c8a.js";import{d as P,s as b,r,o as x,P as B,Q as u,W as n,$ as D,u as l}from"./@vue.a11433a6.js";import{f as V,h as $}from"./consumer.e39aaadf.js";const h=P({__name:"addPop",emits:["success"],setup(k,{expose:p,emit:y}){const a=b(),t=r(0),o=r({name:""}),i=s=>{a.value.open(),s.id&&(t.value=s.id,o.value.name=s.name)},f=async()=>{t.value?await V({...o.value,id:t.value}):await $(o.value),a.value.close()};return p({open:i}),(s,e)=>{const d=C,_=E,c=w,v=F;return x(),B(v,{ref_key:"popRef",ref:a,async:"",title:`${l(t)?"\u7F16\u8F91":"\u65B0\u589E"}\u5206\u7EC4`,onConfirm:f,onClose:e[2]||(e[2]=m=>s.$emit("success"))},{default:u(()=>[n(c,{"label-width":"90px",onSubmit:e[1]||(e[1]=D(()=>{},["prevent"]))},{default:u(()=>[n(_,{label:"\u5206\u7EC4\u540D\u79F0",prop:"name",class:"is-required"},{default:u(()=>[n(d,{modelValue:l(o).name,"onUpdate:modelValue":e[0]||(e[0]=m=>l(o).name=m),placeholder:"\u8BF7\u8F93\u5165\u5206\u7EC4\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["title"])}}});export{h as _};
