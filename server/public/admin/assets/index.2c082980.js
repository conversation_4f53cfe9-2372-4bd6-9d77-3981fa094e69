import{x as S,y as $,b as j,w as z,G as L}from"./element-plus.5bcb7c8a.js";import{g,b as N}from"./index.850efb0d.js";import{_ as U}from"./session.vue_vue_type_script_setup_true_lang.2667f624.js";import{_ as v}from"./embedding.vue_vue_type_script_setup_true_lang.4ef3d3ba.js";import{c as q,e as G}from"./model.63e41af8.js";import{D as K}from"./vuedraggable.2019ddfd.js";import{d as E,s as d,r as y,a0 as Q,b as h,a4 as W,aj as H,o as p,c as w,W as n,Q as r,u as a,j as D,F as J,a7 as O,P as _,a as t,K as F,V as k,R as B,U as X}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";const Y={class:"mx-[10px] mb-[20px]"},Z={class:"flex items-center px-[15px] py-[25px] w-[300px] bg-[#f8f8f8] dark:bg-page rounded-[12px] h-full cursor-move hover:bg-primary-light-9 border border-solid border-page hover:border-primary"},ee={class:"mx-[16px] flex-1 min-w-0"},oe={class:"text-xl font-bold mb-[4px]"},te={class:"mx-[10px] mb-[20px] min-h-[100px]"},re={class:"flex items-center justify-center px-[15px] py-[25px] w-[300px] bg-[#f8f8f8] dark:bg-page rounded-[12px] h-full hover:bg-primary-light-9 border border-solid border-page hover:border-primary"},ae=t("div",{class:"text-xl font-bold ml-1"},"\u81EA\u5B9A\u4E49\u6A21\u578B",-1),se=E({name:"aiModel"}),Je=E({...se,setup(le){d();const s=y("chatModels"),c=Q([{name:"AI\u5BF9\u8BDD\u914D\u7F6E",label:"chatModels",component:d(U),type:1},{name:"\u5411\u91CF\u6A21\u578B\u914D\u7F6E",label:"vectorModels",component:d(v),type:2},{name:"\u91CD\u6392\u6A21\u578B\u914D\u7F6E",label:"rankingModels",component:d(v),type:11}]),M=h(()=>{var o;const l=c.findIndex(i=>i.label===s.value);return(o=c[l].type)!=null?o:1}),u=y({chatModels:[],vectorModels:[],rankingModels:[]}),m=h({get(){return u.value[s.value]},set(l){u.value[s.value]=l}}),f=async()=>{u.value=await q()},V=async()=>{const l=m.value.map((o,i)=>({id:o.id,sort:i}));try{await G({orders:l})}catch{f()}};return f(),(l,o)=>{const i=S,A=$,C=j,T=z,b=W("router-link"),I=N,P=L,x=H("perms");return p(),w("div",null,[n(P,{shadow:"never",class:"!border-none"},{default:r(()=>[n(A,{modelValue:a(s),"onUpdate:modelValue":o[0]||(o[0]=e=>D(s)?s.value=e:null)},{default:r(()=>[(p(!0),w(J,null,O(a(c),(e,R)=>(p(),_(i,{label:`${e.name}`,name:e.label,key:R,lazy:""},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),n(a(K),{class:"flex flex-wrap mx-[-10px]",modelValue:a(m),"onUpdate:modelValue":o[1]||(o[1]=e=>D(m)?m.value=e:null),animation:"300",onSort:V},{item:r(({element:e})=>[t("div",Y,[t("div",Z,[t("div",{class:F(["flex items-center flex-1 min-w-0",{"opacity-60":!e.is_enable}])},[n(C,{src:e.logo,class:"w-[44px] h-[44px]"},null,8,["src"]),t("div",ee,[t("div",oe,k(e.name),1),t("div",{class:F(["flex items-center text-tx-secondary before:mr-[6px] before:block before:w-[8px] before:h-[8px] before:bg-success before:rounded-[50%]",{"before:!bg-danger":!e.is_enable}])},k(e.is_enable?"\u5DF2\u542F\u7528":"\u5DF2\u505C\u7528"),3)])],2),B((p(),_(b,{to:{path:a(g)("setting.ai.models/edit"),query:{id:e.id,type:e.type}}},{default:r(()=>[n(T,{type:"primary",plain:""},{default:r(()=>[X("\u7F16\u8F91")]),_:1})]),_:2},1032,["to"])),[[x,["setting.ai.models/edit"]]])])])]),footer:r(()=>[t("div",te,[B((p(),_(b,{to:{path:a(g)("setting.ai.models/add"),query:{type:a(M)}}},{default:r(()=>[t("div",re,[n(I,{name:"el-icon-Plus",size:20}),ae])]),_:1},8,["to"])),[[x,["setting.ai.models/add"]]])])]),_:1},8,["modelValue"])]),_:1})])}}});export{Je as default};
