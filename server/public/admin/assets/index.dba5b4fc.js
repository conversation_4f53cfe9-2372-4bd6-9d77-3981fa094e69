import{H as me,C as pe,D as ce,I as _e,J as fe,w as ve,F as ye,G as ge,K as Ce,t as be,L as ke,M as Be}from"./element-plus.5bcb7c8a.js";import{_ as Ee}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{P as Fe}from"./index.324d704f.js";import{f as he,b as we,_ as De}from"./index.850efb0d.js";import{_ as Ve}from"./index.vue_vue_type_script_setup_true_lang.67cfcc66.js";import{_ as Ae}from"./index.vue_vue_type_script_setup_true_lang.1e869df8.js";import{d as O,a0 as M,s as V,r as F,aj as xe,o,c as G,W as t,Q as u,u as l,a8 as H,F as Se,a7 as $e,P as i,U as m,a as _,R as f,V as g,T as B,j as Te,n as h}from"./@vue.a11433a6.js";import{m as J,g as Re,n as qe,o as Ue}from"./ai_square.bf3e05a5.js";import{u as Pe}from"./usePaging.b48cb079.js";import{_ as Ke}from"./edit.vue_vue_type_script_setup_true_lang.372d4db5.js";import{_ as Le}from"./audit.vue_vue_type_script_setup_true_lang.14f594dd.js";import{_ as ze}from"./reply.vue_vue_type_script_setup_true_lang.14a31804.js";import{_ as Ie}from"./transfer-cate.vue_vue_type_script_setup_true_lang.94de7fc8.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./picker.9a1dad65.js";import"./index.4de0c800.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";const Ne={class:"mb-4"},je={class:"line-clamp-3 cursor-pointer"},Me={class:"mt-[6px]"},Ge={class:"line-clamp-3 cursor-pointer"},He={class:"mt-[6px]"},Je={class:"flex items-center"},Oe={class:"ml-4"},Qe={class:"flex justify-end mt-4"},We=O({name:"drawSquareLists"}),ta=O({...We,setup(Xe){const n=M({verify_status:"",user_info:"",keyword:"",category_id:"",is_show:"",source:"",start_time:"",end_time:""}),A=V(),q=V(),U=V(),P=V(),w=F(!1),x=F(!1),S=F(!1),$=F(!1),C=F([]),E=M({loading:!0,lists:[]}),{pager:k,getLists:b,resetPage:T,resetParams:Q}=Pe({fetchFun:J,params:n}),W=async()=>{E.loading=!0;try{const s=await Re({type:1});E.lists=s,E.loading=!1}catch(s){E.loading=!1,console.log("\u83B7\u53D6\u7ED8\u753B\u5206\u7C7B\u5931\u8D25=>",s)}},X=s=>{C.value=s},Y=async()=>{var s;w.value=!0,await h(),(s=A.value)==null||s.open("add")},Z=async s=>{var a;w.value=!0,await h(),(a=A.value)==null||a.open("edit",s)},ee=async s=>{try{await qe({id:s})}catch(a){console.log("\u4FEE\u6539\u72B6\u6001\u5931\u8D25=>",a)}},K=async s=>{var a;x.value=!0,await h(),(a=q.value)==null||a.open(s)},te=async s=>{var a;S.value=!0,await h(),(a=U.value)==null||a.open(s)},ae=async s=>{var a;$.value=!0,await h(),(a=P.value)==null||a.open(s)},L=async s=>{await he.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await Ue({id:s}),b()};return W(),b(),(s,a)=>{const z=pe,v=ce,r=_e,D=fe,le=Ae,c=ve,ue=Ve,se=ye,I=ge,oe=we,p=Ce,N=De,j=Fe,R=me,ne=be,ie=ke,re=Ee,y=xe("perms"),de=Be;return o(),G("div",null,[t(I,{class:"!border-none",shadow:"never"},{default:u(()=>[t(se,{ref:"formRef",class:"mb-[-16px]",model:l(n),inline:!0},{default:u(()=>[t(v,{label:"\u7528\u6237\u4FE1\u606F"},{default:u(()=>[t(z,{class:"w-[280px]",modelValue:l(n).user_info,"onUpdate:modelValue":a[0]||(a[0]=e=>l(n).user_info=e),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237ID/\u7528\u6237\u6635\u79F0",clearable:"",onKeyup:H(l(T),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(v,{label:"\u5173\u952E\u8BCD"},{default:u(()=>[t(z,{class:"w-[280px]",modelValue:l(n).keyword,"onUpdate:modelValue":a[1]||(a[1]=e=>l(n).keyword=e),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD",clearable:"",onKeyup:H(l(T),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(v,{label:"\u6240\u5C5E\u5206\u7C7B"},{default:u(()=>[t(D,{class:"w-[280px]",modelValue:l(n).category_id,"onUpdate:modelValue":a[2]||(a[2]=e=>l(n).category_id=e)},{default:u(()=>[t(r,{label:"\u5168\u90E8",value:""}),(o(!0),G(Se,null,$e(l(E).lists,e=>(o(),i(r,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"\u5BA1\u6838\u72B6\u6001"},{default:u(()=>[t(D,{class:"w-[280px]",modelValue:l(n).verify_status,"onUpdate:modelValue":a[3]||(a[3]=e=>l(n).verify_status=e)},{default:u(()=>[t(r,{label:"\u5168\u90E8",value:""}),t(r,{label:"\u5F85\u5BA1\u6838",value:0}),t(r,{label:"\u5BA1\u6838\u901A\u8FC7",value:1}),t(r,{label:"\u5BA1\u6838\u4E0D\u901A\u8FC7",value:2})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"\u662F\u5426\u663E\u793A"},{default:u(()=>[t(D,{class:"w-[280px]",modelValue:l(n).is_show,"onUpdate:modelValue":a[4]||(a[4]=e=>l(n).is_show=e)},{default:u(()=>[t(r,{label:"\u5168\u90E8",value:""}),t(r,{label:"\u663E\u793A",value:1}),t(r,{label:"\u9690\u85CF",value:0})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"\u6DFB\u52A0\u6765\u6E90"},{default:u(()=>[t(D,{class:"w-[280px]",modelValue:l(n).source,"onUpdate:modelValue":a[5]||(a[5]=e=>l(n).source=e)},{default:u(()=>[t(r,{label:"\u5168\u90E8",value:""}),t(r,{label:"\u540E\u53F0\u6DFB\u52A0",value:1}),t(r,{label:"\u524D\u53F0\u7528\u6237",value:2})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:u(()=>[t(le,{startTime:l(n).start_time,"onUpdate:startTime":a[6]||(a[6]=e=>l(n).start_time=e),endTime:l(n).end_time,"onUpdate:endTime":a[7]||(a[7]=e=>l(n).end_time=e)},null,8,["startTime","endTime"])]),_:1}),t(v,null,{default:u(()=>[t(c,{type:"primary",onClick:l(T)},{default:u(()=>[m("\u67E5\u8BE2")]),_:1},8,["onClick"]),t(c,{onClick:l(Q)},{default:u(()=>[m("\u91CD\u7F6E")]),_:1},8,["onClick"]),t(ue,{class:"ml-2.5","fetch-fun":l(J),params:l(n),"page-size":l(k).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),t(I,{class:"!border-none mt-4",shadow:"never"},{default:u(()=>[_("div",Ne,[f((o(),i(c,{type:"primary",onClick:Y},{icon:u(()=>[t(oe,{name:"el-icon-Plus"})]),default:u(()=>[m(" \u65B0\u589E\u7ED8\u753B ")]),_:1})),[[y,["draw.draw_square/add"]]]),f((o(),i(c,{type:"default",plain:!0,disabled:!l(C).length,onClick:a[8]||(a[8]=e=>K(l(C).map(d=>d.id)))},{default:u(()=>[m(" \u6279\u91CF\u5BA1\u6838 ")]),_:1},8,["disabled"])),[[y,["draw.draw_square/verifyStatus"]]]),f((o(),i(c,{type:"default",plain:!0,disabled:!l(C).length,onClick:a[9]||(a[9]=e=>ae(l(C).map(d=>d.id)))},{default:u(()=>[m(" \u79FB\u52A8\u5206\u7C7B ")]),_:1},8,["disabled"])),[[y,["draw.draw_square/removeCategory"]]]),f((o(),i(c,{type:"default",plain:!0,disabled:!l(C).length,onClick:a[10]||(a[10]=e=>L(l(C).map(d=>d.id)))},{default:u(()=>[m(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])),[[y,["draw.draw_square/del"]]])]),f((o(),i(ie,{size:"large",data:l(k).lists,onSelectionChange:X},{default:u(()=>[t(p,{type:"selection",width:"55"}),t(p,{label:"\u56FE\u7247","min-width":"130"},{default:u(({row:e})=>[t(N,{class:"flex-none",src:e==null?void 0:e.thumbnail,width:70,"preview-src-list":[e==null?void 0:e.image],"preview-teleported":!0,"hide-on-click-modal":!0,fit:"contain"},null,8,["src","preview-src-list"])]),_:1}),t(p,{label:"\u7528\u6237\u8F93\u5165/\u4E2D\u6587\u63D0\u793A\u8BCD",prop:"prompts_cn","min-width":"280"},{default:u(({row:e})=>[t(j,{ref:"popRef",title:"\u63D0\u793A\u8BCD",width:"700px",clickModalClose:"",cancelButtonText:"\u53D6\u6D88",confirmButtonText:"\u786E\u5B9A"},{trigger:u(()=>[_("div",je,g(e.prompts_cn||"-"),1)]),default:u(()=>[_("div",Me,g(e.prompts_cn||"-"),1)]),_:2},1536)]),_:1}),t(p,{label:"\u82F1\u6587\u63D0\u793A\u8BCD",prop:"prompts","min-width":"280"},{default:u(({row:e})=>[t(j,{ref:"popRef",title:"\u63D0\u793A\u8BCD",width:"700px",clickModalClose:"",cancelButtonText:"\u53D6\u6D88",confirmButtonText:"\u786E\u5B9A"},{trigger:u(()=>[_("div",Ge,g(e.prompts),1)]),default:u(()=>[_("div",He,g(e.prompts),1)]),_:2},1536)]),_:1}),t(p,{label:"\u6240\u5C5E\u5206\u7C7B",prop:"category_name","min-width":"120"}),t(p,{label:"\u6DFB\u52A0\u6765\u6E90",prop:"source_desc","min-width":"120"}),t(p,{label:"\u7528\u6237\u4FE1\u606F","min-width":"200"},{default:u(({row:e})=>[_("div",Je,[e!=null&&e.user_info.image?(o(),i(N,{key:0,class:"flex-none",src:e==null?void 0:e.user_info.image,width:48,height:48,"preview-src-list":[e==null?void 0:e.user_info.image],"preview-teleported":!0,"hide-on-click-modal":!0,fit:"contain"},null,8,["src","preview-src-list"])):B("",!0),_("span",Oe,g(e==null?void 0:e.user_info.name),1)])]),_:1}),t(p,{label:"\u5BA1\u6838\u72B6\u6001","min-width":"140"},{default:u(({row:e})=>[_("div",null,[e.verify_status==1?(o(),i(R,{key:0,class:"ml-2",type:"success"},{default:u(()=>[m(g(e.verify_status_desc),1)]),_:2},1024)):e.verify_status==0?(o(),i(R,{key:1,class:"ml-2",type:"warning"},{default:u(()=>[m(g(e.verify_status_desc),1)]),_:2},1024)):(o(),i(R,{key:2,class:"ml-2 cursor-pointer",type:"danger",onClick:d=>te(e.verify_result)},{default:u(()=>[m(g(e.verify_status_desc),1)]),_:2},1032,["onClick"]))])]),_:1}),f((o(),i(p,{label:"\u72B6\u6001","min-width":"100"},{default:u(({row:e})=>[t(ne,{onChange:d=>ee(e.id),modelValue:e.is_show,"onUpdate:modelValue":d=>e.is_show=d,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1})),[[y,["draw.draw_square/isShow"]]]),t(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time",sortable:"","min-width":"180","show-tooltip-when-overflow":""}),t(p,{label:"\u64CD\u4F5C","min-width":"180",fixed:"right"},{default:u(({row:e})=>[f((o(),i(c,{type:"primary",link:"",onClick:d=>Z(e.id)},{default:u(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["draw.draw_square/edit"]]]),e.verify_status?B("",!0):f((o(),i(c,{key:0,type:"primary",link:"",onClick:d=>K([e.id])},{default:u(()=>[m(" \u5BA1\u6838 ")]),_:2},1032,["onClick"])),[[y,["draw.draw_square/verifyStatus"]]]),f((o(),i(c,{type:"danger",link:"",onClick:d=>L([e.id])},{default:u(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["draw.draw_square/del"]]])]),_:1})]),_:1},8,["data"])),[[de,l(k).loading]]),_("div",Qe,[t(re,{modelValue:l(k),"onUpdate:modelValue":a[11]||(a[11]=e=>Te(k)?k.value=e:null),onChange:l(b)},null,8,["modelValue","onChange"])])]),_:1}),l(w)?(o(),i(Ke,{key:0,ref_key:"editRef",ref:A,onSuccess:l(b),onClose:a[12]||(a[12]=e=>w.value=!1)},null,8,["onSuccess"])):B("",!0),l(x)?(o(),i(Le,{key:1,ref_key:"auditRef",ref:q,onSuccess:l(b),onClose:a[13]||(a[13]=e=>x.value=!1)},null,8,["onSuccess"])):B("",!0),l(S)?(o(),i(ze,{key:2,ref_key:"replyRef",ref:U,onSuccess:l(b),onClose:a[14]||(a[14]=e=>S.value=!1)},null,8,["onSuccess"])):B("",!0),l($)?(o(),i(Ie,{key:3,type:1,ref_key:"transferRef",ref:P,onSuccess:l(b),onClose:a[15]||(a[15]=e=>$.value=!1)},null,8,["onSuccess"])):B("",!0)])}}});export{ta as default};
