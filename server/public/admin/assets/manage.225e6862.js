import{I as H,J as X,D as Y,w as Z,F as ee,G as te,K as ae,b as le,t as oe,L as se,M as ie}from"./element-plus.5bcb7c8a.js";import{_ as ue}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{u as ne}from"./usePaging.b48cb079.js";import{_ as re}from"./addPop.vue_vue_type_script_setup_true_lang.85452b86.js";import{_ as me}from"./adjustClassPop.vue_vue_type_script_setup_true_lang.a7f435c3.js";import{j as de,h as pe,k as ce,l as _e,m as fe}from"./sticker.587684d9.js";import{f as x}from"./index.850efb0d.js";import{d as ve,r as b,s as F,i as ge,aj as Ce,o as s,c as P,W as e,Q as a,F as be,a7 as we,P as i,u as r,U as d,R as p,a as h,V as ye,j as Ee,T as L,n as B}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./picker.9a1dad65.js";import"./index.4de0c800.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const ke={class:"flex justify-end mt-4"},Ft=ve({__name:"manage",setup(Fe){const c=b({name:"",status:"",category_id:""}),u=b(!1),w=F(),V=F(),y=F(),S=b(),j=async()=>{u.value=!0,await B(),w.value.open()},$=async o=>{u.value=!0,await B(),w.value.open(o)},U=async()=>{u.value=!0,await B();const o=y.value.getSelectionRows().map(l=>l.id);V.value.open(o)},T=async()=>{S.value=await pe()},N=async()=>{const o=y.value.getSelectionRows().map(l=>l.id);await x.confirm("\u662F\u5426\u786E\u8BA4\u6279\u91CF\u5220\u9664\uFF01"),await ce({id:o}),_()},A=async o=>{await _e({id:o})},I=async o=>{await x.confirm("\u786E\u5B9A\u5220\u9664\uFF1F"),await fe({id:o}),_()},E=b(!1),M=o=>{console.log(o),E.value=o.length!=0},{pager:g,getLists:_,resetPage:q,resetParams:z}=ne({fetchFun:de,params:c.value});return ge(async()=>{await _(),await T()}),(o,l)=>{const C=H,D=X,k=Y,m=Z,G=ee,R=te,n=ae,J=le,K=oe,O=se,Q=ue,f=Ce("perms"),W=ie;return s(),P("div",null,[e(R,{class:"!border-none",shadow:"never"},{default:a(()=>[e(G,{ref:"formRef",class:"mb-[-16px]",model:c.value,inline:!0},{default:a(()=>[e(k,{label:"\u6240\u5C5E\u5206\u7C7B"},{default:a(()=>[e(D,{class:"w-[280px]",modelValue:c.value.category_id,"onUpdate:modelValue":l[0]||(l[0]=t=>c.value.category_id=t)},{default:a(()=>[e(C,{label:"\u65E0\u5206\u7C7B",value:0}),(s(!0),P(be,null,we(S.value,(t,v)=>(s(),i(C,{key:v,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(k,{label:"\u72B6\u6001"},{default:a(()=>[e(D,{class:"w-[280px]",modelValue:c.value.status,"onUpdate:modelValue":l[1]||(l[1]=t=>c.value.status=t)},{default:a(()=>[e(C,{label:"\u5F00\u542F",value:"1"}),e(C,{label:"\u5173\u95ED",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(k,null,{default:a(()=>[e(m,{type:"primary",onClick:r(q)},{default:a(()=>[d("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(m,{onClick:r(z)},{default:a(()=>[d("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(R,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[p((s(),i(m,{type:"primary",onClick:j},{default:a(()=>[d("+ \u65B0\u589E\u8D34\u7EB8")]),_:1})),[[f,["digital.decals/add"]]]),p((s(),i(m,{onClick:U,disabled:!E.value},{default:a(()=>[d("\u6279\u91CF\u8C03\u6574")]),_:1},8,["disabled"])),[[f,["digital.decals/bathEdit"]]]),p((s(),i(m,{onClick:N,disabled:!E.value},{default:a(()=>[d("\u6279\u91CF\u5220\u9664")]),_:1},8,["disabled"])),[[f,["digital.decals/batchDel"]]]),p((s(),i(O,{class:"mt-2",size:"large",ref_key:"tableRef",ref:y,data:r(g).lists,onSelectionChange:M},{default:a(()=>[e(n,{type:"selection",width:"55"}),e(n,{label:"\u8D34\u7EB8","min-width":"150"},{default:a(({row:t})=>[e(J,{"preview-teleported":!0,"preview-src-list":[t.url],src:t.url,class:"w-[80px]"},null,8,["preview-src-list","src"])]),_:1}),e(n,{label:"\u6240\u5C5E\u5206\u7C7B","min-width":"100"},{default:a(({row:t})=>[h("span",null,ye(t.category_name||"\u65E0\u5206\u7C7B"),1)]),_:1}),e(n,{label:"\u8D34\u7EB8\u7C7B\u578B",prop:"type_desc","min-width":"100"}),e(n,{label:"\u72B6\u6001",prop:"status","min-width":"100"},{default:a(({row:t})=>[p(e(K,{onChange:v=>A(t.id),modelValue:t.status,"onUpdate:modelValue":v=>t.status=v,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"]),[[f,["digital.decals/status"]]])]),_:1}),e(n,{label:"\u6392\u5E8F",prop:"sort","min-width":"100"}),e(n,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"120"}),e(n,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:a(({row:t})=>[h("div",null,[p((s(),i(m,{type:"primary",link:"",onClick:v=>$(t)},{default:a(()=>[d(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[f,["digital.decals/edit"]]]),p((s(),i(m,{type:"danger",link:"",onClick:v=>I(t.id)},{default:a(()=>[d("\u5220\u9664")]),_:2},1032,["onClick"])),[[f,["digital.decals/del"]]])])]),_:1})]),_:1},8,["data"])),[[W,r(g).loading]]),h("div",ke,[e(Q,{modelValue:r(g),"onUpdate:modelValue":l[2]||(l[2]=t=>Ee(g)?g.value=t:null),onChange:r(_)},null,8,["modelValue","onChange"])])]),_:1}),u.value?(s(),i(re,{key:0,onSuccess:l[3]||(l[3]=()=>{u.value=!1,r(_)()}),onClose:l[4]||(l[4]=t=>u.value=!1),ref_key:"addPopRef",ref:w},null,512)):L("",!0),u.value?(s(),i(me,{key:1,onSuccess:l[5]||(l[5]=()=>{u.value=!1,r(_)()}),ref_key:"adjustPopRef",ref:V},null,512)):L("",!0)])}}});export{Ft as default};
