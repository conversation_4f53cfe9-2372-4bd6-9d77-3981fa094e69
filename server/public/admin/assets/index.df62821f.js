import{Y as O,C as Q,D as W,I as Y,J as H,w as X,F as Z,G as ee,K as te,t as oe,L as ae,M as le}from"./element-plus.5bcb7c8a.js";import{_ as ne}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as se,b as re}from"./index.850efb0d.js";import{_ as ie}from"./index.vue_vue_type_script_setup_true_lang.67cfcc66.js";import{d as S,s as ue,r as E,a0 as P,aj as me,o as r,c as pe,W as e,Q as o,u as t,a8 as de,a as h,U as c,R as _,P as u,j as ce,T as _e}from"./@vue.a11433a6.js";import{u as fe}from"./usePaging.b48cb079.js";import{g as x,d as Ce,b as ge}from"./draw_prompt.2e5e04cc.js";import{g as we}from"./draw_prompt_category.b4dd6927.js";import{_ as ve}from"./edit.vue_vue_type_script_setup_true_lang.424329ef.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const be={class:"w-[200px]"},Ee={class:"flex justify-end mt-4"},he=S({name:"drawPrompt"}),ct=S({...he,setup(ye){const y=ue(),$=E(!0),L=P({multiple:!1,checkStrictly:!0,label:"name",value:"id",children:"children",emitPath:!1}),s=P({prompt:"",category_id:"",model:"mj",status:""}),B=E([]),v=E([]),U=n=>{v.value=n},R=async()=>{const n=await we();B.value=n},F=(n,l={})=>{var g;(g=y.value)==null||g.open(n,l)},V=async n=>{await se.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await Ce({id:n}),C()},j=n=>{ge({id:n})},{pager:m,getLists:C,resetPage:k,resetParams:z}=fe({fetchFun:x,params:s});return C(),R(),(n,l)=>{const g=Q,w=W,K=O,b=Y,N=H,p=X,T=ie,A=Z,D=ee,I=re,i=te,q=oe,G=ae,J=ne,f=me("perms"),M=le;return r(),pe("div",null,[e(D,{class:"!border-none",shadow:"never"},{default:o(()=>[e(A,{ref:"formRef",class:"mb-[-16px]",model:t(s),inline:!0},{default:o(()=>[e(w,{label:"\u5185\u5BB9\u641C\u7D22"},{default:o(()=>[e(g,{class:"w-[200px]",modelValue:t(s).prompt,"onUpdate:modelValue":l[0]||(l[0]=a=>t(s).prompt=a),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9\u5173\u952E\u8BCD",clearable:"",onKeyup:de(t(k),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(w,{label:"\u793A\u4F8B\u7C7B\u76EE"},{default:o(()=>[e(K,{class:"w-[200px]",modelValue:t(s).category_id,"onUpdate:modelValue":l[1]||(l[1]=a=>t(s).category_id=a),options:t(B),props:t(L),clearable:!0},null,8,["modelValue","options","props"])]),_:1}),e(w,{label:"\u793A\u4F8B\u72B6\u6001"},{default:o(()=>[h("div",be,[e(N,{modelValue:t(s).status,"onUpdate:modelValue":l[2]||(l[2]=a=>t(s).status=a)},{default:o(()=>[e(b,{label:"\u5168\u90E8",value:""}),e(b,{label:"\u5F00\u542F",value:1}),e(b,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])])]),_:1}),e(w,null,{default:o(()=>[e(p,{type:"primary",onClick:t(k)},{default:o(()=>[c("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(p,{onClick:t(z)},{default:o(()=>[c("\u91CD\u7F6E")]),_:1},8,["onClick"]),e(T,{class:"ml-2.5","fetch-fun":t(x),params:t(s),"page-size":t(m).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),e(D,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[h("div",null,[_((r(),u(p,{type:"primary",onClick:l[3]||(l[3]=a=>F("add"))},{icon:o(()=>[e(I,{name:"el-icon-Plus"})]),default:o(()=>[c(" \u65B0\u589E ")]),_:1})),[[f,["draw.draw_prompt/add"]]]),_((r(),u(p,{type:"default",plain:!0,disabled:!t(v).length,onClick:l[4]||(l[4]=a=>V(t(v).map(d=>d.id)))},{default:o(()=>[c(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["draw.draw_prompt/delete"]]])]),_((r(),u(G,{size:"large",class:"mt-4",data:t(m).lists,onSelectionChange:U},{default:o(()=>[e(i,{type:"selection",width:"55"}),e(i,{label:"\u5173\u952E\u8BCD\u82F1\u6587",prop:"prompt_en","min-width":"100"}),e(i,{label:"\u5173\u952E\u8BCD\u4E2D\u6587",prop:"prompt","min-width":"100"}),e(i,{label:"\u6240\u5C5E\u7C7B\u522B",prop:"cate_name","min-width":"120"}),_((r(),u(i,{label:"\u72B6\u6001","min-width":"100"},{default:o(({row:a})=>[e(q,{modelValue:a.status,"onUpdate:modelValue":d=>a.status=d,"active-value":1,"inactive-value":0,onChange:d=>j(a.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})),[[f,["draw.draw_prompt/status"]]]),e(i,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(i,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:o(({row:a})=>[_((r(),u(p,{type:"primary",link:"",onClick:d=>F("edit",a)},{default:o(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[f,["draw.draw_prompt/edit"]]]),_((r(),u(p,{type:"danger",link:"",onClick:d=>V([a.id])},{default:o(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["draw.draw_prompt/delete"]]])]),_:1})]),_:1},8,["data"])),[[M,t(m).loading]]),h("div",Ee,[e(J,{modelValue:t(m),"onUpdate:modelValue":l[5]||(l[5]=a=>ce(m)?m.value=a:null),onChange:t(C)},null,8,["modelValue","onChange"])])]),_:1}),t($)?(r(),u(ve,{key:0,ref_key:"editRef",ref:y,onSuccess:t(C)},null,8,["onSuccess"])):_e("",!0)])}}});export{ct as default};
