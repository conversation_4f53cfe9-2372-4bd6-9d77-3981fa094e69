import p from"./decoration-img.16e6b284.js";import{u as s}from"./index.850efb0d.js";import{d as n,o as m,c,a as t,W as a,V as e,L as d,u as l}from"./@vue.a11433a6.js";import{_}from"./vue-drag-resize.527c6620.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";import"./element-plus.5bcb7c8a.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const u={class:"open-vip mt-[-30px] p-[10px]"},x={class:"flex justify-between items-center"},f={class:"flex"},h={class:"ml-2 text-white"},v={class:"font-bold"},g={class:"text-[12px]"},y={class:"bg-white text-[#445df4] py-[5px] px-[10px] rounded-full text-[12px]"},b=n({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(o){const i=s(),{getImageUrl:r}=i;return(w,S)=>(m(),c("div",u,[t("div",{class:"container rounded-t-[8px]",style:d({"background-image":`url(${l(r)(o.content.bg)})`})},[t("div",x,[t("div",f,[a(p,{width:"40px",height:"40px",src:o.content.icon,alt:""},null,8,["src"]),t("div",h,[t("div",v,e(o.content.title),1),t("div",g,e(o.content.sub_title),1)])]),t("div",y,e(o.content.btn),1)])],4)]))}});const mt=_(b,[["__scopeId","data-v-413d447a"]]);export{mt as default};
