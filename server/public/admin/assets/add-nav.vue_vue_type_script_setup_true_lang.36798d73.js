import{C as D,w as F}from"./element-plus.5bcb7c8a.js";import{_ as A}from"./index.4a09b22e.js";import{_ as B}from"./picker.e4cc82a9.js";import{_ as C}from"./picker.9a1dad65.js";import{f as c,b as E}from"./index.850efb0d.js";import{D as U}from"./vuedraggable.2019ddfd.js";import{d as w,b as N,o as i,c as $,a as e,W as n,Q as m,P as z,u as r,j as P,U as j}from"./@vue.a11433a6.js";const I={class:"bg-fill-light flex items-center w-full p-4 mb-4 cursor-move"},L={class:"upload-btn w-[60px] h-[60px]"},Q={class:"ml-3 flex-1"},R={class:"flex"},S=e("span",{class:"text-tx-regular flex-none mr-3"},"\u540D\u79F0",-1),T={class:"flex mt-[18px]"},W=e("span",{class:"text-tx-regular flex-none mr-3"},"\u94FE\u63A5",-1),X=w({__name:"add-nav",props:{modelValue:{type:Array,default:()=>[]},max:{type:Number,default:10},min:{type:Number,default:1},type:{type:String,default:"pc"}},emits:["update:modelValue"],setup(d,{emit:_}){const o=d,f=_,s=N({get(){return o.modelValue},set(a){f("update:modelValue",a)}}),V=()=>{var a;((a=o.modelValue)==null?void 0:a.length)<o.max?s.value.push({image:"",name:"\u5BFC\u822A\u540D\u79F0",link:{}}):c.msgError(`\u6700\u591A\u6DFB\u52A0${o.max}\u4E2A`)},x=a=>{var u;if(((u=o.modelValue)==null?void 0:u.length)<=o.min)return c.msgError(`\u6700\u5C11\u4FDD\u7559${o.min}\u4E2A`);s.value.splice(a,1)};return(a,u)=>{const g=E,h=C,v=D,b=B,y=A,k=F;return i(),$("div",null,[e("div",null,[n(r(U),{class:"draggable",modelValue:r(s),"onUpdate:modelValue":u[0]||(u[0]=l=>P(s)?s.value=l:null),animation:"300"},{item:m(({element:l,index:p})=>[(i(),z(y,{class:"max-w-[400px]",key:p,onClose:t=>x(p)},{default:m(()=>[e("div",I,[n(h,{modelValue:l.image,"onUpdate:modelValue":t=>l.image=t,"upload-class":"bg-body",size:"60px","exclude-domain":""},{upload:m(()=>[e("div",L,[n(g,{name:"el-icon-Plus",size:20})])]),_:2},1032,["modelValue","onUpdate:modelValue"]),e("div",Q,[e("div",R,[S,n(v,{modelValue:l.name,"onUpdate:modelValue":t=>l.name=t,placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),e("div",T,[W,n(b,{type:d.type,modelValue:l.link,"onUpdate:modelValue":t=>l.link=t},null,8,["type","modelValue","onUpdate:modelValue"])])])])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])]),e("div",null,[n(k,{type:"primary",onClick:V},{default:m(()=>[j("\u6DFB\u52A0")]),_:1})])])}}});export{X as _};
