import{Q as f,R as r,D as _,C as V,B as p,F as b}from"./element-plus.5bcb7c8a.js";import{_ as x}from"./add-nav.vue_vue_type_script_setup_true_lang.36798d73.js";import{d as E,o as F,c as y,W as e,Q as o,U as u,a as B}from"./@vue.a11433a6.js";const w={class:"flex-1"},D=E({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(t){return(C,l)=>{const d=f,m=r,n=_,s=V,c=p,i=b;return F(),y("div",null,[e(i,{"label-width":"70px"},{default:o(()=>[e(n,{label:"\u6392\u7248\u6837\u5F0F"},{default:o(()=>[e(m,{modelValue:t.content.style,"onUpdate:modelValue":l[0]||(l[0]=a=>t.content.style=a)},{default:o(()=>[e(d,{label:1},{default:o(()=>[u("\u6A2A\u6392")]),_:1}),e(d,{label:2},{default:o(()=>[u("\u7AD6\u6392")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u6807\u9898\u540D\u79F0"},{default:o(()=>[e(s,{class:"w-[400px]",modelValue:t.content.title,"onUpdate:modelValue":l[1]||(l[1]=a=>t.content.title=a)},null,8,["modelValue"]),e(c,{class:"ml-2",modelValue:t.content.showTitle,"onUpdate:modelValue":l[2]||(l[2]=a=>t.content.showTitle=a)},{default:o(()=>[u("\u663E\u793A")]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u83DC\u5355\u8BBE\u7F6E"},{default:o(()=>[B("div",w,[e(x,{type:"mobile",modelValue:t.content.data,"onUpdate:modelValue":l[3]||(l[3]=a=>t.content.data=a),max:999},null,8,["modelValue"])])]),_:1})]),_:1})])}}});export{D as _};
