import{Q as R,R as H,D as S,t as $,w as M,F as N}from"./element-plus.5bcb7c8a.js";import{_ as z}from"./index.4a09b22e.js";import{f as F,b as Q}from"./index.850efb0d.js";import{_ as j}from"./picker.e4cc82a9.js";import{_ as G}from"./picker.9a1dad65.js";import{m as V}from"./@vueuse.a2407f20.js";import{D as I}from"./vuedraggable.2019ddfd.js";import{d as P,o as g,c as T,W as e,Q as o,u as m,j as W,U as _,a as d,P as q}from"./@vue.a11433a6.js";const J={class:"flex-1"},K=d("div",{class:"form-tips"},"\u6700\u591A\u6DFB\u52A05\u5F20\uFF0C\u5EFA\u8BAE\u5C3A\u5BF8\uFF1A690*280px",-1),L={class:"bg-fill-light w-full p-4 mt-4"},O={class:"flex-1 flex items-center"},X={class:"drag-move cursor-move ml-auto"},c=5,ne=P({__name:"attr",props:{isHidden:{type:Boolean},content:{}},emits:["update:isHidden"],setup(v,{emit:b}){const r=v,p=b,i=V(r,"isHidden",p),t=V(r,"content",p),h=()=>{var u;((u=t.value.data)==null?void 0:u.length)<c?t.value.data.push({image:"",name:"",link:{},isShow:!0}):F.msgError(`\u6700\u591A\u6DFB\u52A0${c}\u5F20`)},A=u=>{var a;if(((a=t.value.data)==null?void 0:a.length)<=1)return F.msgError("\u6700\u5C11\u4FDD\u7559\u4E00\u5F20\u56FE\u7247");t.value.data.splice(u,1)};return(u,a)=>{const f=R,k=H,n=S,x=G,B=j,w=$,D=Q,C=z,U=M,y=N;return g(),T("div",null,[e(y,{"label-width":"70px"},{default:o(()=>[e(n,{label:"\u662F\u5426\u663E\u793A"},{default:o(()=>[e(k,{modelValue:m(i),"onUpdate:modelValue":a[0]||(a[0]=l=>W(i)?i.value=l:null)},{default:o(()=>[e(f,{label:!1},{default:o(()=>[_("\u663E\u793A")]),_:1}),e(f,{label:!0},{default:o(()=>[_("\u4E0D\u663E\u793A")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u56FE\u7247\u8BBE\u7F6E"},{default:o(()=>[d("div",J,[K,e(m(I),{class:"draggable",modelValue:m(t).data,"onUpdate:modelValue":a[1]||(a[1]=l=>m(t).data=l),animation:"300",handle:".drag-move"},{item:o(({element:l,index:E})=>[(g(),q(C,{key:E,onClose:s=>A(E),class:"max-w-[400px]"},{default:o(()=>[d("div",L,[e(n,{label:"\u56FE\u7247\u5C01\u9762"},{default:o(()=>[d("div",null,[e(x,{modelValue:l.image,"onUpdate:modelValue":s=>l.image=s,"upload-class":"bg-body",size:"100px","exclude-domain":!0},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1024),e(n,{label:"\u94FE\u63A5\u5730\u5740",class:"mt-[18px]"},{default:o(()=>[e(B,{type:"mobile",modelValue:l.link,"onUpdate:modelValue":s=>l.link=s},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(n,{class:"mt-[18px]",label:"\u662F\u5426\u663E\u793A"},{default:o(()=>[d("div",O,[e(w,{"active-value":!0,"inactive-value":!1,modelValue:l.isShow,"onUpdate:modelValue":s=>l.isShow=s},null,8,["modelValue","onUpdate:modelValue"]),d("div",X,[e(D,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])])]),_:1}),e(n,null,{default:o(()=>{var l;return[e(U,{disabled:((l=u.content.data)==null?void 0:l.length)>=c,type:"primary",onClick:h},{default:o(()=>[_(" \u6DFB\u52A0\u56FE\u7247 ")]),_:1},8,["disabled"])]}),_:1})]),_:1})])}}});export{ne as _};
