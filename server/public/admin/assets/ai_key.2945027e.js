import{r as e}from"./index.850efb0d.js";function s(t){return e.get({url:"/setting.KeyPool/lists",params:t})}function l(t){return e.get({url:"/setting.KeyPool/models",params:t})}function n(t){return e.post({url:"/setting.KeyPool/add",data:t})}function r(t){return e.post({url:"/setting.KeyPool/edit",data:t})}function u(t){return e.post({url:"/setting.KeyPool/del",data:t})}function i(t){return e.post({url:"/setting.KeyPool/status",data:t})}function a(t){return e.get({url:"/setting.KeyPool/detail",params:t})}export{n as a,l as b,s as c,u as d,r as e,a as g,i as s};
