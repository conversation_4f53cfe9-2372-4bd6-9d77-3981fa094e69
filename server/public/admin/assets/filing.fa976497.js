import{_ as x}from"./index.88e852a7.js";import{Q as U,R,C as j,D as N,w as z,F as G,G as I}from"./element-plus.5bcb7c8a.js";import{f as P,b as Q}from"./index.850efb0d.js";import{_ as $}from"./index.4a09b22e.js";import{_ as L}from"./picker.9a1dad65.js";import{a as S,s as T}from"./website.5920ffa5.js";import{d as h,r as W,aj as q,o as n,c as d,W as o,Q as e,F as H,a7 as J,u as F,P as f,a as t,U as s,R as K}from"./@vue.a11433a6.js";import"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";const M={class:"website-filing"},O=t("div",{class:"mb-5"},"\u7AD9\u70B9\u5E95\u90E8\u7248\u6743\u5907\u6848\u4FE1\u606F\u8BBE\u7F6E",-1),X={class:"py-4 bg-fill-lighter"},Y={class:"w-80"},Z={class:"w-80"},oo=t("div",{class:"form-tips"},"\u8DF3\u8F6C\u94FE\u63A5\u4E0D\u8BBE\u7F6E\uFF0C\u5219\u4E0D\u8DF3\u8F6C",-1),eo={class:"w-80"},to=t("div",{class:"form-tips"}," \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A\u5BBD60px*\u9AD860px\u3002jpg\uFF0Cjpeg\uFF0Cpng\u683C\u5F0F ",-1),uo={class:"w-80"},lo=h({name:"webFilling"}),ie=h({...lo,setup(ao){const u=W([{key:"",value:"",icon:""}]),p=async()=>{const i=await S();!i.length||(u.value=i)},v=()=>{u.value.push({key:"",value:"",icon:""})},E=i=>{if(u.value.length<=1)return P.msgError("\u81F3\u5C11\u4FDD\u7559\u4E00\u4E2A");u.value.splice(i,1)},g=async()=>{await T({config:u.value}),p()};return p(),(i,io)=>{const m=j,r=N,D=L,C=U,w=R,B=$,b=Q,c=z,y=G,V=I,k=x,A=q("perms");return n(),d("div",M,[o(V,{shadow:"never",class:"!border-none"},{default:e(()=>[O,o(y,{ref:"form",class:"ls-form","label-width":"100px"},{default:e(()=>[(n(!0),d(H,null,J(F(u),(l,_)=>(n(),f(B,{class:"mb-4",key:_,"show-close":F(u).length>1,onClose:a=>E(_)},{default:e(()=>[t("div",X,[o(r,{label:"\u663E\u793A\u540D\u79F0",prop:"key"},{default:e(()=>[t("div",Y,[t("div",null,[o(m,{modelValue:l.key,"onUpdate:modelValue":a=>l.key=a,placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])])])]),_:2},1024),o(r,{label:"\u8DF3\u8F6C\u94FE\u63A5",prop:"value"},{default:e(()=>[t("div",Z,[t("div",null,[o(m,{modelValue:l.value,"onUpdate:modelValue":a=>l.value=a,placeholder:"\u8BF7\u8F93\u5165\u94FE\u63A5\uFF0C\u4F8B\u5982\uFF1Ahttp://www.beian.gov.cn"},null,8,["modelValue","onUpdate:modelValue"])]),oo])]),_:2},1024),o(r,{label:"\u56FE\u6807",prop:"icon"},{default:e(()=>[t("div",eo,[o(D,{modelValue:l.icon,"onUpdate:modelValue":a=>l.icon=a,size:"60px",limit:1},null,8,["modelValue","onUpdate:modelValue"]),to])]),_:2},1024),o(r,{label:"\u56FE\u6807\u4F4D\u7F6E",prop:"icp_link"},{default:e(()=>[t("div",uo,[o(w,{"model-value":1},{default:e(()=>[o(C,{label:1,size:"large"},{default:e(()=>[s("\u5DE6\u8FB9")]),_:1})]),_:1})])]),_:1})])]),_:2},1032,["show-close","onClose"]))),128)),t("div",null,[o(c,{type:"primary",onClick:v},{default:e(()=>[o(b,{name:"el-icon-Plus"}),s(" \u6DFB\u52A0 ")]),_:1})])]),_:1},512)]),_:1}),K((n(),f(k,null,{default:e(()=>[o(c,{type:"primary",onClick:g},{default:e(()=>[s("\u4FDD\u5B58")]),_:1})]),_:1})),[[A,["setting.website/setCopyright"]]])])}}});export{ie as default};
