import{I as x,J as q,D as R,F as V}from"./element-plus.5bcb7c8a.js";import{P}from"./index.324d704f.js";import{p as T,a as A,b as I,g as N}from"./ai_square.bf3e05a5.js";import{d as J,s as m,a0 as _,o as i,c as y,W as l,Q as u,u as n,F as L,a7 as M,P as O}from"./@vue.a11433a6.js";const Q={class:"edit-popup"},G=J({__name:"transfer-cate",props:{type:{type:[Number,String],default:1}},emits:["success","close"],setup(g,{expose:C,emit:v}){const p=v,r=g,d=m(),c=m(),o=_({loading:!0,lists:[]}),a=_({category_id:"",ids:[]}),B={category_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u5206\u7C7B",trigger:["blur"]}]},b=async()=>{var e,t;await((e=d.value)==null?void 0:e.validate()),r.type==1?await T(a):r.type==2?await A(a):r.type==3&&await I(a),(t=c.value)==null||t.close(),p("success")},w=async()=>{o.loading=!0;try{const e=await N({type:r.type});o.lists=e,o.loading=!1}catch(e){o.loading=!1,console.log("\u83B7\u53D6\u5206\u7C7B\u5931\u8D25=>",e)}},F=e=>{var t;w(),a.ids=e,(t=c.value)==null||t.open()},E=async e=>{for(const t in a)e[t]!=null&&e[t]!=null&&(a[t]=e[t])},h=()=>{p("close")};return C({open:F,setFormData:E}),(e,t)=>{const f=x,k=q,D=R,S=V;return i(),y("div",Q,[l(P,{ref_key:"popupRef",ref:c,title:"\u6279\u91CF\u79FB\u52A8\u5206\u7C7B",async:!0,width:"550px",onConfirm:b,onClose:h},{default:u(()=>[l(S,{ref_key:"formRef",ref:d,model:n(a),"label-width":"84px",rules:B},{default:u(()=>[l(D,{label:"\u9009\u62E9\u5206\u7C7B",prop:"category_id"},{default:u(()=>[l(k,{class:"w-full",modelValue:n(a).category_id,"onUpdate:modelValue":t[0]||(t[0]=s=>n(a).category_id=s)},{default:u(()=>[l(f,{label:"\u5168\u90E8",value:""}),(i(!0),y(L,null,M(n(o).lists,s=>(i(),O(f,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},512)])}}});export{G as _};
