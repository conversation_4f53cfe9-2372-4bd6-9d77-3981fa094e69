import{C as R,D as Z,I as ee,J as te,w as ue,F as ae,B as le,K as ne,b as oe,t as _e,L as se,M as pe,H as ye,Q as Fe,R as ge,d as fe,f as Ae}from"./element-plus.5bcb7c8a.js";import{b as Y}from"./index.850efb0d.js";import{d as w,b as T,o as y,c as S,a as d,F as Q,a7 as W,K as he,V as Ee,u,r as k,a0 as ie,w as I,R as de,W as t,Q as s,a8 as me,P,j as B,U as H,T as G,s as ve,$ as Pe}from"./@vue.a11433a6.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{u as re}from"./usePaging.b48cb079.js";import{h as Ce,g as Se}from"./ai_creation.a70c015c.js";import{_ as X}from"./vue-drag-resize.527c6620.js";import{b as be}from"./manage.273433ec.js";import{s as Ve}from"./type.d6a09a6d.js";import{g as xe,a as Be}from"./robot_square.168fdb05.js";import{P as ke}from"./index.324d704f.js";var e=(o=>(o.SHOP_PAGES="shop",o.CREATIVE_PICKER="creative",o.ROLE_PICKER="role",o.AGENT="agent",o.CUSTOM_LINK="custom",o.MINI_PROGRAM="mini_program",o))(e||{});const De={class:"shop-pages h-[458px]"},we={class:"link-list flex flex-wrap"},Oe=["onClick"],Ie=w({__name:"shop-pages",props:{modelValue:{type:Object,default:()=>({})},type:{type:String,default:"pc"},isTab:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=[{name:"\u9996\u9875",path:"/",type:e.SHOP_PAGES},{name:"AI\u5BF9\u8BDD",path:"/dialogue/chat",type:e.SHOP_PAGES},{name:"AI\u521B\u4F5C",path:"/creation",type:e.SHOP_PAGES},{name:"AI\u667A\u80FD\u4F53",path:"/application/layout/robot",type:e.SHOP_PAGES},{name:"AI\u641C\u7D22",path:"/search",type:e.SHOP_PAGES},{name:"AIPPT",path:"/ai_ppt",type:e.SHOP_PAGES},{name:"\u667A\u80FD\u4F53\u5E7F\u573A",path:"/robot_square",type:e.SHOP_PAGES},{name:"SD\u7ED8\u753B",path:"/draw/sd",type:e.SHOP_PAGES},{name:"DALLE\u7ED8\u753B",path:"/draw/dalle",type:e.SHOP_PAGES},{name:"MJ\u7ED8\u753B",path:"/draw/mj",type:e.SHOP_PAGES},{name:"\u8C46\u5305\u7ED8\u753B",path:"/draw/doubao",type:e.SHOP_PAGES},{name:"AI\u97F3\u4E50",path:"/music",type:e.SHOP_PAGES},{name:"AI\u89C6\u9891",path:"/video",type:e.SHOP_PAGES},{name:"\u5145\u503C\u4E2D\u5FC3",path:"/user/recharge",type:e.SHOP_PAGES},{name:"\u4F1A\u5458\u4E2D\u5FC3",path:"/user/member",type:e.SHOP_PAGES},{name:"\u6211\u7684\u4F5C\u54C1",path:"/user/works",type:e.SHOP_PAGES},{path:"/mind_map",name:"\u601D\u7EF4\u5BFC\u56FE",type:e.SHOP_PAGES},{path:"/user/promotion/distribution",name:"\u5206\u9500\u63A8\u5E7F",type:e.SHOP_PAGES},{path:"/app_center",name:"\u5E94\u7528\u4E2D\u5FC3",type:e.SHOP_PAGES}],n=[{name:"\u9996\u9875",path:"/pages/index/index",type:e.SHOP_PAGES,canTab:!0},{name:"AI\u5BF9\u8BDD",path:"/packages/pages/dialogue/dialogue",type:e.SHOP_PAGES,canTab:!0},{name:"AI\u521B\u4F5C",path:"/pages/ai_creation/ai_creation",type:e.SHOP_PAGES,canTab:!0},{name:"AI\u667A\u80FD\u4F53",path:"/pages/kb/kb",type:e.SHOP_PAGES,canTab:!0},{name:"AI\u97F3\u4E50",path:"/packages/pages/music/music",type:e.SHOP_PAGES,canTab:!0},{name:"AI\u89C6\u9891",path:"/packages/pages/video/video",type:e.SHOP_PAGES,canTab:!0},{name:"AI\u641C\u7D22",path:"/packages/pages/ai_search/ai_search",type:e.SHOP_PAGES,canTab:!0},{name:"AIPPT",path:"/packages/pages/ai_ppt/ai_ppt",type:e.SHOP_PAGES,canTab:!0},{name:"SD\u7ED8\u753B",path:"/packages/pages/draw/sd",type:e.SHOP_PAGES,canTab:!0},{name:"DALLE\u7ED8\u753B",path:"/packages/pages/draw/dalle",type:e.SHOP_PAGES,canTab:!0},{name:"MJ\u7ED8\u753B",path:"/packages/pages/draw/mj",type:e.SHOP_PAGES,canTab:!0},{name:"\u8C46\u5305\u7ED8\u753B",path:"/packages/pages/draw/doubao",type:e.SHOP_PAGES,canTab:!0},{name:"\u4E2A\u4EBA\u4E2D\u5FC3",path:"/pages/user/user",type:e.SHOP_PAGES,canTab:!0},{name:"\u4E2A\u4EBA\u4FE1\u606F",path:"/packages/pages/user_set/user_set",type:e.SHOP_PAGES,canTab:!1},{name:"\u5145\u503C\u4E2D\u5FC3",path:"/packages/pages/recharge/recharge",type:e.SHOP_PAGES},{name:"\u4F1A\u5458\u4E2D\u5FC3",path:"/packages/pages/member_center/member_center",type:e.SHOP_PAGES},{name:"\u667A\u80FD\u4F53\u5E7F\u573A",path:"/packages/pages/robot_square/robot_square",type:e.SHOP_PAGES,canTab:!0},{name:"\u8054\u7CFB\u5BA2\u670D",path:"/packages/pages/customer_service/customer_service",type:e.SHOP_PAGES},{name:"\u5173\u4E8E\u6211\u4EEC",path:"/packages/pages/as_us/as_us",type:e.SHOP_PAGES},{name:"\u9690\u79C1\u534F\u8BAE",path:"/packages/pages/agreement/agreement",type:e.SHOP_PAGES},{name:"\u8D2D\u4E70\u8BB0\u5F55",path:"/packages/pages/buy_record/buy_record",type:e.SHOP_PAGES},{name:"\u6211\u7684\u4F5C\u54C1",path:"/packages/pages/user_works/user_works",type:e.SHOP_PAGES},{path:"/packages/pages/mind_map/mind_map",name:"\u601D\u7EF4\u5BFC\u56FE",type:e.SHOP_PAGES,canTab:!0},{path:"/packages/pages/promotion_center/promotion_center",name:"\u5206\u9500\u63A8\u5E7F",type:e.SHOP_PAGES,canTab:!0},{path:"/packages/pages/invite_poster/invite_poster",name:"\u9080\u8BF7\u6D77\u62A5",type:e.SHOP_PAGES},{path:"/packages/pages/redeem_code/redeem_code",name:"\u5361\u5BC6\u5151\u6362",type:e.SHOP_PAGES,canTab:!0},{path:"/packages/pages/notification/notification",name:"\u6D88\u606F\u901A\u77E5",type:e.SHOP_PAGES},{path:"/packages/pages/task_reward/task_reward",name:"\u4EFB\u52A1\u5956\u52B1",type:e.SHOP_PAGES,canTab:!0},{path:"/packages/pages/app_center/app_center",name:"\u5E94\u7528\u4E2D\u5FC3",type:e.SHOP_PAGES,canTab:!0}],a=T(()=>_.type=="pc"?r:n.filter(p=>_.isTab?p.canTab:!0)),F=p=>{A("update:modelValue",p)};return(p,i)=>(y(),S("div",De,[d("div",we,[(y(!0),S(Q,null,W(u(a),(v,f)=>(y(),S("div",{class:he(["link-item border border-br px-5 py-[5px] rounded-[3px] cursor-pointer mr-[10px] mb-[10px]",{"border-primary text-primary":o.modelValue.path==v.path&&o.modelValue.name==v.name}]),key:f,onClick:E=>F(v)},Ee(v.name),11,Oe))),128))])]))}}),Ge={class:"detail"},He={class:"w-[150px]"},Re={class:"flex row-center"},Te={class:"flex justify-end mt-5"},$e=w({__name:"creative-picker",props:{modelValue:{type:Object,default:()=>({})},type:{type:String,default:"pc"}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=k({path:"/pages/news_detail/news_detail",name:"",query:{},type:e.CREATIVE_PICKER}),n=k([]),a=ie({name:"",category_id:""}),{pager:F,getLists:p,resetPage:i,resetParams:v}=re({fetchFun:Ce,params:a}),f=T(()=>_.type=="pc"?"/creation/produce":"/packages/pages/create/create"),E=async()=>{const{lists:m}=await Se({page_type:0});n.value=m},C=m=>m==Number(r.value.id),b=m=>{let c=null;_.type=="pc"?c={modelId:m.id,cateId:0}:c={id:m.id},r.value={id:m.id,name:m.name,path:f.value,query:c,type:e.CREATIVE_PICKER},A("update:modelValue",r.value)};return I(()=>_.modelValue,m=>{if(m.type!=e.CREATIVE_PICKER)return r.value={id:"",name:"",path:f.value,type:e.SHOP_PAGES};r.value=m},{immediate:!0}),E(),p(),(m,c)=>{const $=R,x=Z,D=ee,L=te,q=Y,K=ue,U=ae,N=le,g=ne,M=oe,O=_e,j=se,z=ce,J=pe;return de((y(),S("div",Ge,[t(U,{ref:"formRef",model:u(a),inline:!0},{default:s(()=>[t(x,{label:"\u521B\u4F5C\u540D\u79F0"},{default:s(()=>[t($,{class:"w-[180px]",modelValue:u(a).name,"onUpdate:modelValue":c[0]||(c[0]=l=>u(a).name=l),placeholder:"\u8BF7\u8F93\u5165\u521B\u4F5C\u540D\u79F0",clearable:"",onKeyup:me(u(i),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(x,{label:"\u521B\u4F5C\u5206\u7C7B"},{default:s(()=>[d("div",He,[t(L,{modelValue:u(a).category_id,"onUpdate:modelValue":c[1]||(c[1]=l=>u(a).category_id=l)},{default:s(()=>[t(D,{label:"\u5168\u90E8",value:""}),(y(!0),S(Q,null,W(u(n),(l,V)=>(y(),P(D,{key:V,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(x,{label:""},{default:s(()=>[t(K,{type:"primary",onClick:u(i)},{default:s(()=>[t(q,{name:"el-icon-Search"})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),t(j,{ref:"table",data:u(F).lists,style:{width:"100%"},height:"356px",size:"small","row-key":"id"},{default:s(()=>[t(g,{label:"\u9009\u62E9","min-width":"50"},{default:s(({row:l})=>[d("div",Re,[t(N,{"model-value":C(l.id),size:"large",onChange:V=>b(l)},null,8,["model-value","onChange"])])]),_:1}),t(g,{label:"\u56FE\u6807","min-width":"100"},{default:s(({row:l})=>[t(M,{src:l.image,class:"w-[44px] h-[44px]"},null,8,["src"])]),_:1}),t(g,{label:"\u6A21\u578B\u540D\u79F0",prop:"name","min-width":"120"}),t(g,{label:"\u6A21\u578B\u63CF\u8FF0",prop:"tips","min-width":"150"}),t(g,{label:"\u6240\u5C5E\u7C7B\u76EE",prop:"category_name","min-width":"120"}),t(g,{label:"\u72B6\u6001","min-width":"100"},{default:s(({row:l})=>[t(O,{disabled:!0,modelValue:l.status,"onUpdate:modelValue":V=>l.status=V,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"]),d("div",Te,[t(z,{modelValue:u(F),"onUpdate:modelValue":c[2]||(c[2]=l=>B(F)?F.value=l:null),onChange:c[3]||(c[3]=l=>u(p)())},null,8,["modelValue"])])])),[[J,u(F).loading]])}}});const Le=X($e,[["__scopeId","data-v-2468f380"]]),qe={class:"detail"},Ke={class:"w-[150px]"},Ue={class:"flex row-center"},Ne={class:"flex justify-end mt-5"},Me=w({__name:"role-picker",props:{modelValue:{type:Object,default:()=>({})},type:{type:String,default:"pc"}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=k({path:"/pages/news_detail/news_detail",name:"",query:{},type:e.ROLE_PICKER}),n=k([]),a=ie({name:"",category_id:""}),{pager:F,getLists:p,resetPage:i,resetParams:v}=re({fetchFun:be,params:a}),f=T(()=>_.type=="pc"?"/dialogue/role":"/packages/pages/dialogue_role/dialogue_role"),E=async()=>{const{lists:m}=await Ve({page_type:0});n.value=m},C=m=>m==Number(r.value.id),b=m=>{const c={id:m.id};r.value={id:m.id,name:m.name,path:f.value,query:c,type:e.ROLE_PICKER},A("update:modelValue",r.value)};return I(()=>_.modelValue,m=>{if(m.type!=e.ROLE_PICKER)return r.value={id:"",name:"",path:f.value,type:e.SHOP_PAGES};r.value=m},{immediate:!0}),E(),p(),(m,c)=>{const $=R,x=Z,D=ee,L=te,q=Y,K=ue,U=ae,N=le,g=ne,M=oe,O=_e,j=se,z=ce,J=pe;return de((y(),S("div",qe,[t(U,{ref:"formRef",model:u(a),inline:!0},{default:s(()=>[t(x,{label:"\u89D2\u8272\u540D\u79F0"},{default:s(()=>[t($,{class:"w-[180px]",modelValue:u(a).name,"onUpdate:modelValue":c[0]||(c[0]=l=>u(a).name=l),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0",clearable:"",onKeyup:me(u(i),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(x,{label:"\u89D2\u8272\u5206\u7C7B"},{default:s(()=>[d("div",Ke,[t(L,{modelValue:u(a).category_id,"onUpdate:modelValue":c[1]||(c[1]=l=>u(a).category_id=l)},{default:s(()=>[t(D,{label:"\u5168\u90E8",value:""}),(y(!0),S(Q,null,W(u(n),(l,V)=>(y(),P(D,{key:V,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(x,{label:""},{default:s(()=>[t(K,{type:"primary",onClick:u(i)},{default:s(()=>[t(q,{name:"el-icon-Search"})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),t(j,{ref:"table",data:u(F).lists,style:{width:"100%"},height:"356px",size:"small","row-key":"id"},{default:s(()=>[t(g,{label:"\u9009\u62E9","min-width":"50"},{default:s(({row:l})=>[d("div",Ue,[t(N,{"model-value":C(l.id),size:"large",onChange:V=>b(l)},null,8,["model-value","onChange"])])]),_:1}),t(g,{label:"\u56FE\u6807","min-width":"100"},{default:s(({row:l})=>[t(M,{src:l.image,class:"w-[44px] h-[44px]"},null,8,["src"])]),_:1}),t(g,{label:"\u6A21\u578B\u540D\u79F0",prop:"name","min-width":"120"}),t(g,{label:"\u6A21\u578B\u63CF\u8FF0",prop:"tips","min-width":"150"}),t(g,{label:"\u6240\u5C5E\u7C7B\u76EE",prop:"category_name","min-width":"120"}),t(g,{label:"\u72B6\u6001","min-width":"100"},{default:s(({row:l})=>[t(O,{disabled:!0,modelValue:l.status,"onUpdate:modelValue":V=>l.status=V,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"]),d("div",Ne,[t(z,{modelValue:u(F),"onUpdate:modelValue":c[2]||(c[2]=l=>B(F)?F.value=l:null),onChange:c[3]||(c[3]=l=>u(p)())},null,8,["modelValue"])])])),[[J,u(F).loading]])}}});const je=X(Me,[["__scopeId","data-v-d6d59ccb"]]),ze={class:"detail"},Je={class:"w-[150px]"},Qe={class:"flex row-center"},We={class:"flex justify-end mt-5"},Xe=w({__name:"agent-picker",props:{modelValue:{type:Object,default:()=>({})},type:{type:String,default:"pc"}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=k({path:"/pages/news_detail/news_detail",name:"",query:{},type:e.AGENT}),n=k([]),a=ie({name:"",cid:""}),{pager:F,getLists:p,resetPage:i,resetParams:v}=re({fetchFun:xe,params:a}),f=T(()=>_.type=="pc"?"/robot_square/chat":"/packages/pages/square_chat/square_chat"),E=async()=>{const m=await Be({page_type:0});n.value=m},C=m=>m==Number(r.value.id),b=m=>{let c=null;_.type=="pc"?c={square_id:m.id}:c={square_id:m.id},r.value={id:m.id,name:m.name,path:f.value,query:c,type:e.AGENT},A("update:modelValue",r.value)};return I(()=>_.modelValue,m=>{if(m.type!=e.AGENT)return r.value={id:"",name:"",path:f.value,type:e.SHOP_PAGES};r.value=m},{immediate:!0}),E(),p(),(m,c)=>{const $=R,x=Z,D=ee,L=te,q=Y,K=ue,U=ae,N=le,g=ne,M=oe,O=ye,j=se,z=ce,J=pe;return de((y(),S("div",ze,[t(U,{ref:"formRef",model:u(a),inline:!0},{default:s(()=>[t(x,{label:"\u667A\u80FD\u4F53\u540D\u79F0"},{default:s(()=>[t($,{class:"w-[180px]",modelValue:u(a).name,"onUpdate:modelValue":c[0]||(c[0]=l=>u(a).name=l),placeholder:"\u8BF7\u8F93\u5165\u667A\u80FD\u4F53\u540D\u79F0",clearable:"",onKeyup:me(u(i),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(x,{label:"\u667A\u80FD\u4F53\u5206\u7C7B"},{default:s(()=>[d("div",Je,[t(L,{modelValue:u(a).cid,"onUpdate:modelValue":c[1]||(c[1]=l=>u(a).cid=l)},{default:s(()=>[t(D,{label:"\u5168\u90E8",value:""}),(y(!0),S(Q,null,W(u(n),(l,V)=>(y(),P(D,{key:V,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(x,{label:""},{default:s(()=>[t(K,{type:"primary",onClick:u(i)},{default:s(()=>[t(q,{name:"el-icon-Search"})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),t(j,{ref:"table",data:u(F).lists,style:{width:"100%"},height:"356px",size:"small","row-key":"id"},{default:s(()=>[t(g,{label:"\u9009\u62E9","min-width":"50"},{default:s(({row:l})=>[d("div",Qe,[t(N,{"model-value":C(l.id),size:"large",onChange:V=>b(l)},null,8,["model-value","onChange"])])]),_:1}),t(g,{label:"\u56FE\u6807","min-width":"100"},{default:s(({row:l})=>[t(M,{src:l.image,class:"w-[44px] h-[44px]"},null,8,["src"])]),_:1}),t(g,{label:"\u667A\u80FD\u4F53\u540D\u79F0",prop:"name","min-width":"120"}),t(g,{label:"\u6240\u5C5E\u5206\u7C7B","min-width":"180",prop:"category"}),t(g,{label:"\u72B6\u6001","min-width":"100"},{default:s(({row:l})=>[d("div",null,[l.verify_status==1?(y(),P(O,{key:0,class:"ml-2",type:"success"},{default:s(()=>[H(" \u5BA1\u6838\u901A\u8FC7 ")]),_:1})):l.verify_status==0?(y(),P(O,{key:1,class:"ml-2",type:"warning"},{default:s(()=>[H(" \u5BA1\u6838\u4E2D ")]),_:1})):(y(),P(O,{key:2,class:"ml-2 cursor-pointer",type:"danger"},{default:s(()=>[H(" \u5BA1\u6838\u5931\u8D25 ")]),_:1}))])]),_:1})]),_:1},8,["data"]),d("div",We,[t(z,{modelValue:u(F),"onUpdate:modelValue":c[2]||(c[2]=l=>B(F)?F.value=l:null),onChange:c[3]||(c[3]=l=>u(p)())},null,8,["modelValue"])])])),[[J,u(F).loading]])}}});const Ye=X(Xe,[["__scopeId","data-v-7b77e456"]]),Ze={class:"custom-link h-[428px] mt-[30px]"},et={class:"flex flex-wrap"},tt=d("div",{class:"mt-[5px]"},"\u81EA\u5B9A\u4E49\u94FE\u63A5",-1),ut={class:"ml-4 flex-1 min-w-[100px]"},at=d("div",{class:"form-tips"}," \u8BF7\u586B\u5199\u5B8C\u6574\u7684\u5E26\u6709\u201Chttps://\u201D\u6216\u201Chttp://\u201D\u7684\u94FE\u63A5\u5730\u5740\u3002\u5728\u5C0F\u7A0B\u5E8F\u4E2D\u8DF3\u8F6C\uFF0C\u94FE\u63A5\u7684\u57DF\u540D\u5FC5\u987B\u5728\u5FAE\u4FE1\u516C\u4F17\u5E73\u53F0\u8BBE\u7F6E\u4E1A\u52A1\u57DF\u540D ",-1),lt=w({__name:"custom-link",props:{modelValue:{type:Object,default:()=>({})},type:{type:String,default:"pc"}},emits:["update:modelValue"],setup(o,{emit:h}){const _=h,A=r=>{_("update:modelValue",{path:r,type:e.CUSTOM_LINK})};return(r,n)=>{const a=R;return y(),S("div",Ze,[d("div",et,[tt,d("div",ut,[t(a,{"model-value":o.modelValue.path,placeholder:"\u8BF7\u8F93\u5165\u94FE\u63A5\u5730\u5740",onInput:A},null,8,["model-value"]),at])])])}}}),nt={class:"mini-program h-[455px]"},ot=d("div",{class:"text-xl font-medium"},"\u8DF3\u8F6C\u5C0F\u7A0B\u5E8F",-1),st={class:"flex flex-wrap items-center mt-4"},pt=d("div",{class:"w-[86px] text-right"},"\u5C0F\u7A0B\u5E8FAPPID",-1),it={class:"ml-4 flex-1 min-w-[100px]"},dt={class:"flex flex-wrap items-center mt-4"},mt=d("div",{class:"w-[86px] text-right"},"\u5C0F\u7A0B\u5E8F\u8DEF\u5F84",-1),ct={class:"ml-4 flex-1 min-w-[100px]"},rt={class:"flex flex-wrap items-center mt-4"},_t=d("div",{class:"w-[86px] text-right"},"\u4F20\u9012\u53C2\u6570",-1),Et={class:"ml-4 flex-1 min-w-[100px]"},yt=d("div",{class:"form-tips ml-[100px] max-w-[320px]"},[d("div",null,"\u793A\u4F8B\uFF1Aid=2&ustm=jiny&name=234"),d("div",{class:"text-error"}," \u6CE8\u610F\uFF1A\u4E0D\u5141\u8BB8\u8F93\u5165\u4E2D\u6587\u3001\u7279\u6B8A\u5B57\u7B26\u7B49\u3002\u5982\u679C\u51FA\u73B0\u5BF9\u4E0D\u8D77\uFF0C\u5F53\u524D\u9875\u9762\u65E0\u6CD5\u8BBF\u95EE\uFF0C\u5927\u6982\u7387\u662F\u8DF3\u8F6C\u53C2\u6570\u7684\u95EE\u9898\uFF01\uFF01 ")],-1),Ft={class:"flex flex-wrap items-center mt-4"},gt=d("div",{class:"w-[86px] text-right"},"\u5C0F\u7A0B\u5E8F\u7248\u672C",-1),ft={class:"ml-4 flex-1 min-w-[100px]"},At=d("div",null,[d("div",{class:"form-tips ml-[100px] max-w-[320px]"},[d("div",{class:"mt-4"}," 1. \u5C0F\u7A0B\u5E8FAPPID\u548C\u5C0F\u7A0B\u5E8F\u8DEF\u5F84\u94FE\u63A5\u5730\u5740\uFF0C\u5C0F\u7A0B\u5E8F\u8DEF\u5F84\u94FE\u63A5\u5730\u5740\u8BF7\u586B\u5199\u5C0F\u7A0B\u5E8F\u7684\u9875\u9762\u8DEF\u5F84\uFF0C\u5982\uFF1Apages/index/index "),d("div",{class:"mt-2"},[d("span",null,"2. \u5982\u679C\u662FH5(\u6D4F\u89C8\u5668)\u4E2D\u9700\u8981\u8DF3\u8F6C\u5230\u5C0F\u7A0B\u5E8F\uFF0C\u5219\u9700\u8981\u4EE5\u4E0B\u914D\u7F6E--->"),d("a",{href:"https://mp.weixin.qq.com/",class:"text-primary",target:"_blank",rel:"nofollow"}," \u5C0F\u7A0B\u5E8F\u7BA1\u7406\u540E\u53F0 -> \u8BBE\u7F6E -> \u9690\u79C1\u4E0E\u5B89\u5168 -> \u660E\u6587 scheme \u62C9\u8D77\u6B64\u5C0F\u7A0B\u5E8F \uFF08\u70B9\u51FB\u8DF3\u8F6C\u53BB\u914D\u7F6E\uFF09 ")])])],-1),ht=w({__name:"mini-program",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=(n,a)=>{console.log("123123",n,a),A("update:modelValue",{..._.modelValue,name:"\u5C0F\u7A0B\u5E8F\u8DF3\u8F6C",query:{..._.modelValue.query,[n]:a},type:e.MINI_PROGRAM})};return I(()=>_.modelValue,n=>{var a;(a=n.query)!=null&&a.env_version||r("env_version","release")},{immediate:!0}),(n,a)=>{var v,f,E,C;const F=R,p=Fe,i=ge;return y(),S("div",nt,[ot,d("div",st,[pt,d("div",it,[t(F,{class:"max-w-[320px]","model-value":(v=o.modelValue.query)==null?void 0:v.appId,placeholder:"\u8BF7\u8F93\u5165\u5C0F\u7A0B\u5E8FappId",onInput:a[0]||(a[0]=b=>r("appId",b))},null,8,["model-value"])])]),d("div",dt,[mt,d("div",ct,[t(F,{class:"max-w-[320px]","model-value":(f=o.modelValue.query)==null?void 0:f.path,placeholder:"\u8BF7\u8F93\u5165\u5C0F\u7A0B\u5E8F\u8DEF\u5F84\u94FE\u63A5\u5730\u5740",onInput:a[1]||(a[1]=b=>r("path",b))},null,8,["model-value"])])]),d("div",rt,[_t,d("div",Et,[t(F,{class:"max-w-[320px]","model-value":(E=o.modelValue.query)==null?void 0:E.query,placeholder:"\u8BF7\u8F93\u5165\u5C0F\u7A0B\u5E8F\u8DF3\u8F6C\u53C2\u6570(\u9009\u586B)",onInput:a[2]||(a[2]=b=>r("query",b))},null,8,["model-value"])])]),yt,d("div",Ft,[gt,d("div",ft,[t(i,{"model-value":(C=o.modelValue.query)==null?void 0:C.env_version,onChange:a[3]||(a[3]=b=>r("env_version",b))},{default:s(()=>[t(p,{label:"release"},{default:s(()=>[H("\u6B63\u5F0F\u7248")]),_:1}),t(p,{label:"trial"},{default:s(()=>[H("\u4F53\u9A8C\u7248")]),_:1}),t(p,{label:"develop"},{default:s(()=>[H("\u5F00\u53D1\u7248")]),_:1})]),_:1},8,["model-value"])])]),At])}}}),vt={class:"link flex"},Pt={class:"flex-1 pl-4"},Ct=w({__name:"index",props:{modelValue:{type:Object,required:!0},type:{type:String,default:"pc"},isTab:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=k([{name:"\u57FA\u7840\u9875\u9762",type:e.SHOP_PAGES,link:{}},{name:"\u521B\u4F5C\u9009\u62E9",type:e.CREATIVE_PICKER,link:{}},{name:"\u667A\u80FD\u4F53\u9009\u62E9",type:e.AGENT,link:{}},{name:"\u89D2\u8272\u9009\u62E9",type:e.ROLE_PICKER,link:{}},{name:"\u81EA\u5B9A\u4E49\u94FE\u63A5",type:e.CUSTOM_LINK,link:{}},{name:"\u8DF3\u8F6C\u5C0F\u7A0B\u5E8F",type:e.MINI_PROGRAM,link:{}}]),n=T({get(){var p;return(p=r.value.find(i=>i.type==a.value))==null?void 0:p.link},set(p){r.value.forEach(i=>{i.type==a.value&&(i.link=p)})}}),a=k(e.SHOP_PAGES),F=p=>{a.value=p};return I(n,p=>{!p.type||A("update:modelValue",p)}),I(()=>_.modelValue,p=>{a.value=p.type,n.value=p},{immediate:!0}),(p,i)=>{const v=fe,f=Ae;return y(),S("div",vt,[t(f,{"default-active":u(a),class:"w-[160px] min-h-[350px] link-menu",onSelect:F},{default:s(()=>[(y(!0),S(Q,null,W(u(r),(E,C)=>(y(),P(v,{index:E.type,key:C},{default:s(()=>[d("span",null,Ee(E.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"]),d("div",Pt,[u(e).SHOP_PAGES==u(a)?(y(),P(Ie,{key:0,modelValue:u(n),"onUpdate:modelValue":i[0]||(i[0]=E=>B(n)?n.value=E:null),type:o.type,"is-tab":o.isTab},null,8,["modelValue","type","is-tab"])):G("",!0),u(e).CREATIVE_PICKER==u(a)?(y(),P(Le,{key:1,modelValue:u(n),"onUpdate:modelValue":i[1]||(i[1]=E=>B(n)?n.value=E:null),type:o.type},null,8,["modelValue","type"])):G("",!0),u(e).AGENT==u(a)&&o.type!="pc"?(y(),P(Ye,{key:2,modelValue:u(n),"onUpdate:modelValue":i[2]||(i[2]=E=>B(n)?n.value=E:null),type:o.type},null,8,["modelValue","type"])):G("",!0),u(e).ROLE_PICKER==u(a)?(y(),P(je,{key:3,modelValue:u(n),"onUpdate:modelValue":i[3]||(i[3]=E=>B(n)?n.value=E:null),type:o.type},null,8,["modelValue","type"])):G("",!0),u(e).CUSTOM_LINK==u(a)?(y(),P(lt,{key:4,modelValue:u(n),"onUpdate:modelValue":i[4]||(i[4]=E=>B(n)?n.value=E:null),type:o.type},null,8,["modelValue","type"])):G("",!0),u(e).MINI_PROGRAM==u(a)?(y(),P(ht,{key:5,modelValue:u(n),"onUpdate:modelValue":i[5]||(i[5]=E=>B(n)?n.value=E:null)},null,8,["modelValue"])):G("",!0)])])}}});const St=X(Ct,[["__scopeId","data-v-e9e3e178"]]),bt=w({__name:"picker",props:{modelValue:{type:Object},disabled:{type:Boolean,default:!1},type:{type:String,default:"pc"},isTab:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(o,{emit:h}){const _=o,A=h,r=ve(),n=k({path:"",type:e.SHOP_PAGES}),a=()=>{A("update:modelValue",{...n.value,isTab:_.isTab})},F=T(()=>{var p,i;switch((p=_.modelValue)==null?void 0:p.type){case e.SHOP_PAGES:return _.modelValue.name;case e.CREATIVE_PICKER:return _.modelValue.name;case e.ROLE_PICKER:return _.modelValue.name;case e.AGENT:return _.modelValue.name;case e.CUSTOM_LINK:return _.modelValue.path;default:return(i=_.modelValue)==null?void 0:i.name}});return I(()=>_.modelValue,p=>{p!=null&&p.type&&(n.value=p)},{immediate:!0}),(p,i)=>{const v=Y,f=R;return y(),S("div",{class:"link-picker flex-1",onClick:i[2]||(i[2]=E=>{var C;return!o.disabled&&((C=u(r))==null?void 0:C.open())})},[t(f,{"model-value":u(F),placeholder:"\u8BF7\u9009\u62E9\u94FE\u63A5",readonly:"",disabled:o.disabled},{suffix:s(()=>{var E;return[(E=o.modelValue)!=null&&E.path?(y(),P(v,{key:1,name:"el-icon-Close",onClick:i[0]||(i[0]=Pe(C=>!o.disabled&&A("update:modelValue",{}),["stop"]))})):(y(),P(v,{key:0,name:"el-icon-ArrowRight"}))]}),_:1},8,["model-value","disabled"]),t(ke,{ref_key:"popupRef",ref:r,width:"900px",title:"\u94FE\u63A5\u9009\u62E9",onConfirm:a},{default:s(()=>[t(St,{modelValue:u(n),"onUpdate:modelValue":i[1]||(i[1]=E=>B(n)?n.value=E:null),type:o.type,"is-tab":o.isTab},null,8,["modelValue","type","is-tab"])]),_:1},512)])}}});const Tt=X(bt,[["__scopeId","data-v-775f24fc"]]);export{Tt as _};
