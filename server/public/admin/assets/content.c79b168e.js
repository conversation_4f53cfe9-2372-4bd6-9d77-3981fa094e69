import{w as l}from"./element-plus.5bcb7c8a.js";import _ from"./decoration-img.16e6b284.js";import{d as u,b as x,o as p,c as r,a as t,L as h,F as f,a7 as v,W as a,T as y,V as m,Q as w,U as g,u as S,bk as b,bj as k}from"./@vue.a11433a6.js";import{_ as B}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const I=o=>(b("data-v-fe4d7642"),o=o(),k(),o),V={class:"entrance pt-[40px]"},C={class:"flex w-[1200px] mx-auto justify-center"},D=["to"],N={key:0,class:"mb-[10px]"},E={class:"text-2xl font-medium"},F=I(()=>t("div",{class:"line w-[100%] mt-4"},null,-1)),T={class:"my-4 text-sm h-[80px] leading-[20px] line-clamp-4 px-[20px]"},j={class:"enter-btn"},L=u({__name:"content",props:{prop:{}},setup(o){const s=o,c=x(()=>s.prop.data.filter(i=>i.isShow));return(i,z)=>{const n=l;return p(),r("div",V,[t("div",C,[t("div",{class:"grid flex-wrap",style:h({"grid-template-columns":`repeat(${s.prop.showType}, minmax(0, 1fr))`})},[(p(!0),r(f,null,v(S(c),(e,d)=>(p(),r("div",{class:"flex-1 mb-[40px]",key:d},[t("div",{class:"chat-card h-full",to:e.path},[e.icon?(p(),r("div",N,[a(_,{src:e.icon,width:"58px",height:"58px",radius:"8px"},null,8,["src"])])):y("",!0),t("div",E,m(e.title),1),F,t("div",T,m(e.desc),1),t("div",j,[a(n,{link:"",type:"primary"},{default:w(()=>[g("\u7ACB\u5373\u524D\u5F80 >")]),_:1})])],8,D)]))),128))],4)])])}}});const gt=B(L,[["__scopeId","data-v-fe4d7642"]]);export{gt as default};
