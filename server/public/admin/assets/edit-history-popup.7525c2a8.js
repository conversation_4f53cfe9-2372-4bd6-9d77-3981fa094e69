import{a7 as tt,a8 as et,O as ot,H as at,w as lt,G as st,I as ut,J as it,D as nt,F as _t,K as dt,L as rt,P as mt,M as ct}from"./element-plus.5bcb7c8a.js";import{_ as pt}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{b as ft,_ as Ft}from"./index.850efb0d.js";import{b as vt,a as bt}from"./robot_edit_log.ad074937.js";import{u as Et}from"./usePaging.b48cb079.js";import ht from"./detail.cf20575a.js";import{d as I,r as m,a0 as H,o as C,P as B,Q as e,u as a,a as l,W as t,U as _,V as n,T as gt,j as D,R as yt,bk as Ct,bj as Bt}from"./@vue.a11433a6.js";import{_ as Dt}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const v=b=>(Ct("data-v-c222ad75"),b=b(),Bt(),b),kt={class:"flex justify-between items-center"},Vt=v(()=>l("span",null,"\u7F16\u8F91\u7EDF\u8BA1",-1)),wt={class:"stat-item"},At={class:"stat-value"},xt=v(()=>l("div",{class:"stat-label"},"\u603B\u7F16\u8F91\u6B21\u6570",-1)),Pt={class:"stat-item"},Rt={class:"stat-value"},Ht=v(()=>l("div",{class:"stat-label"},"\u81EA\u52A8\u4E0B\u67B6\u6B21\u6570",-1)),It={class:"stat-item"},St={class:"stat-value"},Yt=v(()=>l("div",{class:"stat-label"},"\u4ECA\u65E5\u7F16\u8F91",-1)),jt={class:"stat-item"},Lt={class:"stat-value"},Ut=v(()=>l("div",{class:"stat-label"},"\u672C\u5468\u7F16\u8F91",-1)),$t={class:"flex justify-between mb-4"},Mt={class:"flex items-center"},Nt={class:"font-medium"},Tt={class:"text-xs text-gray-500"},Ot={class:"flex justify-end mt-4"},qt=I({name:"EditHistoryPopup"}),zt=I({...qt,emits:["close"],setup(b,{expose:S,emit:Y}){const j=Y,c=m(!1),k=m(0),V=m(""),p=m(!1),w=m(),i=H({robot_id:0,edit_type:"",is_auto_offline:"",start_time:"",end_time:""}),y=m([]),f=H({total_edits:0,auto_offline_count:0,today_edits:0,week_edits:0}),{pager:F,getLists:A,resetPage:x,resetParams:L}=Et({fetchFun:bt,params:i}),U=(u,s="")=>{k.value=u,V.value=s,i.robot_id=u,c.value=!0,A(),$()},$=async()=>{try{const u=await vt({robot_id:k.value});Object.assign(f,u)}catch(u){console.error("\u52A0\u8F7D\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:",u)}},M=u=>{u&&u.length===2?(i.start_time=u[0],i.end_time=u[1]):(i.start_time="",i.end_time="")},N=u=>{var s;(s=w.value)==null||s.open(u)},T=()=>{c.value=!1,j("close")};return S({open:U}),(u,s)=>{const d=lt,E=tt,O=et,q=st,z=ft,h=ut,P=it,g=nt,G=ot,J=_t,K=Ft,r=dt,R=at,Q=rt,W=pt,X=mt,Z=ct;return C(),B(X,{modelValue:a(c),"onUpdate:modelValue":s[6]||(s[6]=o=>D(c)?c.value=o:null),title:`\u7F16\u8F91\u5386\u53F2 - ${a(V)||"\u667A\u80FD\u4F53"}`,width:"80%","before-close":T,"destroy-on-close":""},{default:e(()=>[a(p)?(C(),B(q,{key:0,class:"!border-none mb-4",shadow:"never"},{header:e(()=>[l("div",kt,[Vt,t(d,{size:"small",onClick:s[0]||(s[0]=o=>p.value=!1)},{default:e(()=>[_("\u9690\u85CF")]),_:1})])]),default:e(()=>[t(O,{gutter:20},{default:e(()=>[t(E,{span:6},{default:e(()=>[l("div",wt,[l("div",At,n(a(f).total_edits),1),xt])]),_:1}),t(E,{span:6},{default:e(()=>[l("div",Pt,[l("div",Rt,n(a(f).auto_offline_count),1),Ht])]),_:1}),t(E,{span:6},{default:e(()=>[l("div",It,[l("div",St,n(a(f).today_edits),1),Yt])]),_:1}),t(E,{span:6},{default:e(()=>[l("div",jt,[l("div",Lt,n(a(f).week_edits),1),Ut])]),_:1})]),_:1})]),_:1})):gt("",!0),l("div",$t,[l("div",null,[t(d,{type:"primary",onClick:a(x)},{icon:e(()=>[t(z,{name:"el-icon-Refresh"})]),default:e(()=>[_(" \u5237\u65B0 ")]),_:1},8,["onClick"])]),l("div",null,[t(d,{onClick:s[1]||(s[1]=o=>p.value=!a(p))},{default:e(()=>[_(n(a(p)?"\u9690\u85CF\u7EDF\u8BA1":"\u663E\u793A\u7EDF\u8BA1"),1)]),_:1})])]),t(J,{model:a(i),inline:!0,class:"mb-4"},{default:e(()=>[t(g,{label:"\u7F16\u8F91\u7C7B\u578B"},{default:e(()=>[t(P,{modelValue:a(i).edit_type,"onUpdate:modelValue":s[2]||(s[2]=o=>a(i).edit_type=o),placeholder:"\u8BF7\u9009\u62E9",clearable:""},{default:e(()=>[t(h,{label:"\u667A\u80FD\u4F53\u7F16\u8F91",value:1}),t(h,{label:"\u77E5\u8BC6\u5E93\u7F16\u8F91\u89E6\u53D1",value:2})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"\u662F\u5426\u81EA\u52A8\u4E0B\u67B6"},{default:e(()=>[t(P,{modelValue:a(i).is_auto_offline,"onUpdate:modelValue":s[3]||(s[3]=o=>a(i).is_auto_offline=o),placeholder:"\u8BF7\u9009\u62E9",clearable:""},{default:e(()=>[t(h,{label:"\u662F",value:1}),t(h,{label:"\u5426",value:0})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"\u65F6\u95F4\u8303\u56F4"},{default:e(()=>[t(G,{modelValue:a(y),"onUpdate:modelValue":s[4]||(s[4]=o=>D(y)?y.value=o:null),type:"datetimerange","range-separator":"\u81F3","start-placeholder":"\u5F00\u59CB\u65F6\u95F4","end-placeholder":"\u7ED3\u675F\u65F6\u95F4",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:M},null,8,["modelValue"])]),_:1}),t(g,null,{default:e(()=>[t(d,{type:"primary",onClick:a(x)},{default:e(()=>[_("\u67E5\u8BE2")]),_:1},8,["onClick"]),t(d,{onClick:a(L)},{default:e(()=>[_("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),yt((C(),B(Q,{data:a(F).lists},{default:e(()=>[t(r,{label:"\u667A\u80FD\u4F53\u4FE1\u606F","min-width":"200"},{default:e(({row:o})=>[l("div",Mt,[t(K,{src:o.robot_image,width:40,height:40,class:"mr-2"},null,8,["src"]),l("div",null,[l("div",Nt,n(o.robot_name||"\u5DF2\u5220\u9664"),1),l("div",Tt,"ID: "+n(o.robot_id),1)])])]),_:1}),t(r,{label:"\u7F16\u8F91\u7528\u6237",prop:"user_nickname","min-width":"120"}),t(r,{label:"\u7F16\u8F91\u7C7B\u578B","min-width":"120"},{default:e(({row:o})=>[t(R,{type:o.edit_type===1?"primary":"warning"},{default:e(()=>[_(n(o.edit_type===1?"\u667A\u80FD\u4F53\u7F16\u8F91":"\u77E5\u8BC6\u5E93\u7F16\u8F91\u89E6\u53D1"),1)]),_:2},1032,["type"])]),_:1}),t(r,{label:"\u662F\u5426\u81EA\u52A8\u4E0B\u67B6","min-width":"120"},{default:e(({row:o})=>[t(R,{type:o.is_auto_offline?"danger":"success"},{default:e(()=>[_(n(o.is_auto_offline?"\u662F":"\u5426"),1)]),_:2},1032,["type"])]),_:1}),t(r,{label:"\u7F16\u8F91\u65F6\u95F4",prop:"create_time","min-width":"180"}),t(r,{label:"\u64CD\u4F5C",width:"100",fixed:"right"},{default:e(({row:o})=>[t(d,{type:"primary",link:"",onClick:Gt=>N(o)},{default:e(()=>[_(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Z,a(F).loading]]),l("div",Ot,[t(W,{modelValue:a(F),"onUpdate:modelValue":s[5]||(s[5]=o=>D(F)?F.value=o:null),onChange:a(A)},null,8,["modelValue","onChange"])]),t(ht,{ref_key:"detailPopupRef",ref:w},null,512)]),_:1},8,["modelValue","title"])}}});const Se=Dt(zt,[["__scopeId","data-v-c222ad75"]]);export{Se as default};
