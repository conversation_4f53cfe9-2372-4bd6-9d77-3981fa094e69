import{w as B}from"./element-plus.5bcb7c8a.js";import x from"./decoration-img.16e6b284.js";import{d as F,b as f,o as n,c as p,a as t,F as b,a7 as y,W as r,U as u,V as c,Q as C,u as h,L as v,bk as E,bj as k}from"./@vue.a11433a6.js";import{_ as A}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const i=e=>(E("data-v-942a8f68"),e=e(),k(),e),g=i(()=>t("div",{class:"bg-[#f7cb64] text-btn-text px-[15px] pt-[15px] pb-[40px] mb-[-25px]"},[u(" \u5269\u4F59\u6761\u6570 "),t("span",{class:"text-[24px] font-medium"},"999"),u(" \u6761 ")],-1)),w={class:"px-[15px] pb-[10px]"},L={class:"daily-tasks"},D=i(()=>t("div",{class:"tasks-title"},[t("div",{class:"font-medium text-xl"},"\u6BCF\u65E5\u4EFB\u52A1"),t("div",{class:"ml-[7px] text-muted text-sm"},"\u514D\u8D39\u83B7\u5F97\u6761\u6570")],-1)),I={class:"tasks-content"},M={class:"flex-1 min-w-0 ml-[10px]"},S={class:"text-lg font-medium"},T=["innerHTML"],j={class:"mt-[3px] text-xs text-muted text-justify"},H=["innerHTML"],N={class:"flex-none"},V=F({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(e){const m=e,o={1:{num:"0/1",btn_text:"\u7B7E\u5230",desc:'\u83B7\u5F97<span class="text-error">1</span>\u7535\u529B\u503C'},2:{num:"0/10",btn_text:"\u53BB\u5206\u4EAB",desc:'\u9080\u8BF71\u4EBA\uFF0C\u83B7\u5F97<span class="text-error"> 10 </span>\u7535\u529B\u503C'},3:{num:"0/3",btn_text:"\u53BB\u5206\u4EAB",desc:'\u5206\u4EAB1\u6B21\uFF0C\u83B7\u5F97<span class="text-error"> 10 </span>\u7535\u529B\u503C'},4:{num:"0/4",btn_text:"\u53BB\u5B8C\u6210",desc:'\u5206\u4EAB1\u6B21\uFF0C\u83B7\u5F97<span class="text-error"> 10 </span>\u7535\u529B\u503C'},5:{num:"0/3",btn_text:"\u53BB\u5B8C\u6210",desc:'\u5206\u4EAB1\u6B21\uFF0C\u83B7\u5F97<span class="text-error"> 100 </span>\u7535\u529B\u503C'},6:{num:"0/3",btn_text:"\u53BB\u5B8C\u6210",desc:'\u5206\u4EAB1\u6B21\uFF0C\u83B7\u5F97<span class="text-error"> 10 </span>\u7535\u529B\u503C'},7:{num:"0/3",btn_text:"\u53BB\u5B8C\u6210",desc:'\u5206\u4EAB1\u6B21\uFF0C\u83B7\u5F97<span class="text-error"> 10 </span>\u7535\u529B\u503C'}},l=f(()=>m.content.data.filter(a=>a.show));return(a,z)=>{const d=B;return n(),p("div",{class:"task-center",style:v(e.styles)},[g,t("div",w,[t("div",L,[D,t("div",I,[(n(!0),p(b,null,y(h(l),(s,_)=>(n(),p("div",{class:"tasks-item p-[10px] flex items-center",key:_},[r(x,{width:"60",height:"60",src:s.image},null,8,["src"]),t("div",M,[t("div",S,[u(c((s==null?void 0:s.customName)||s.name)+" (",1),t("span",{innerHTML:o[s.type].num},null,8,T),u(") ")]),t("div",j,[t("span",{innerHTML:o[s.type].desc},null,8,H)])]),t("div",N,[r(d,{style:{"--el-color-primary":"#f6c459"},type:"primary",round:"",size:"small",class:"w-[60px]"},{default:C(()=>[u(c(o[s.type].btn_text),1)]),_:2},1024)])]))),128))])])])],4)}}});const Et=A(V,[["__scopeId","data-v-942a8f68"]]);export{Et as default};
