import{u as x}from"./index.850efb0d.js";import{d as f,b as a,o as s,c as e,a as t,K as v,u as i,V as r,T as l,F as d,a7 as _,L as w}from"./@vue.a11433a6.js";import{_ as y}from"./vue-drag-resize.527c6620.js";import"./element-plus.5bcb7c8a.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const S={class:"flex flex-col items-center mx-auto max-w-[1200px]"},b={key:0,class:"flex justify-center flex-col mt-4 sm:mt-0"},k={class:"flex items-center"},I=["src"],Q={class:"text-[20px] font-medium ml-2"},A={class:"flex sm:mt-3 sm:flex-col"},C={class:"mt-5 sm:mb-5"},L={class:"grid grid-cols-2 mt-10 sm:mt-0"},U={class:"mt-4 text-center"},B={class:"text-[18px] font-bold"},D={class:"mt-2 text-[16px] text-[#666]"},T={class:"mt-[15px]"},V={class:"mt-4 text-center"},z={class:"text-[18px] font-bold"},F={class:"mt-2 text-[16px] text-[#666]"},M={class:"mt-[15px]"},N={class:"flex items-center sm:m-0 m-5 sm:mt-[25px] mt-[35px]"},j={key:0,class:"mr-10 text-center sm:mr-12"},E=["src"],K={class:"mt-3 text-white"},$={key:1,class:"text-center"},q=["src"],G={class:"mt-3 text-white"},H=f({__name:"content",props:{prop:{}},setup(u){const m=x(),n=u,h=a(()=>n.prop.columnMenu1.filter(o=>o.isShow)),g=a(()=>n.prop.columnMenu2.filter(o=>o.isShow));return(o,J)=>(s(),e("div",{class:"bg-center bg-cover",style:w({backgroundImage:`url(${i(m).getImageUrl(o.prop.bgImage)})`})},[t("div",S,[t("div",{class:v(["grid grid-cols-1 lg:grid-cols-3 xl:gap-x-10 sm:py-10 lg:max-w-[1150px] lg:mx-auto",{"lg:grid-cols-2":!o.prop.isShowLeft}])},[o.prop.isShowLeft?(s(),e("div",b,[t("div",k,[t("img",{class:"w-[34px] h-[34px]",src:i(m).getImageUrl(o.prop.logoImage),alt:""},null,8,I),t("span",Q,r(i(m).config.web_name),1)]),t("div",A,[t("div",C,r(o.prop.content),1)])])):l("",!0),t("div",L,[t("div",U,[t("div",B,r(o.prop.column1),1),t("div",D,[(s(!0),e(d,null,_(i(h),(p,c)=>(s(),e("ul",{key:c},[t("li",T,r(p.title),1)]))),128))])]),t("div",V,[t("div",z,r(o.prop.column2),1),t("div",F,[(s(!0),e(d,null,_(i(g),(p,c)=>(s(),e("ul",{key:c},[t("li",M,r(p.title),1)]))),128))])])]),t("div",N,[o.prop.rightQrcodeShow1?(s(),e("div",j,[t("img",{class:"w-[120px] h-[120px]",src:i(m).getImageUrl(o.prop.rightQrcode1),alt:"\u7801\u591A\u591A"},null,8,E),t("div",K,r(o.prop.rightQrcodeTitle1),1)])):l("",!0),o.prop.rightQrcodeShow2?(s(),e("div",$,[t("img",{class:"w-[120px] h-[120px]",src:i(m).getImageUrl(o.prop.rightQrcode2),alt:"\u7801\u591A\u591A"},null,8,q),t("div",G,r(o.prop.rightQrcodeTitle2),1)])):l("",!0)])],2)])],4))}});const Lt=y(H,[["__scopeId","data-v-dd2a3371"]]);export{Lt as default};
