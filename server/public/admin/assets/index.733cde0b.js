import{X as T,C as j,D as $,I as q,J as z,w as M,F as G,G as J,K as O,o as Q,t as W,L as X,M as H}from"./element-plus.5bcb7c8a.js";import{_ as Y}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{g as Z,f as ee,_ as te}from"./index.850efb0d.js";import{d as ae,r as le,s as oe,i as ne,a4 as ie,aj as se,o as m,c as ue,W as e,Q as a,u as l,a8 as k,U as _,R as g,P as f,a as o,T as re,V as h,j as me}from"./@vue.a11433a6.js";import{u as pe}from"./usePaging.b48cb079.js";import{a as de,p as ce,d as _e}from"./digital.3da01839.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const fe={class:"flex items-center"},Fe={class:"mt-[4px]"},be=o("span",{class:"mr-4"}," \u6635\u79F0: ",-1),ve={class:"mt-[4px]"},ge=o("span",{class:"mr-4"}," \u7F16\u53F7: ",-1),he={class:"flex items-center"},Ce={class:"ml-[10px]"},Ee={class:"flex justify-end mt-4"},mt=ae({__name:"index",setup(we){const s=le({name:"",user:"",is_disable:""});oe();const{pager:r,getLists:F,resetPage:b,resetParams:x}=pe({fetchFun:de,params:s.value}),y=async p=>{await ce({id:p})},D=async p=>{await ee.confirm("\u786E\u5B9A\u5220\u9664\uFF1F"),await _e({id:p}),F()};return ne(()=>{F()}),(p,u)=>{const C=j,d=$,v=q,B=z,c=M,A=G,E=J,n=O,P=Q,U=te,K=T,R=W,S=ie("router-link"),I=X,L=Y,w=se("perms"),N=H;return m(),ue("div",null,[e(E,{class:"!border-none",shadow:"never"},{default:a(()=>[e(A,{ref:"formRef",class:"mb-[-16px]",model:l(s),inline:!0},{default:a(()=>[e(d,{label:"\u5F62\u8C61\u540D\u79F0"},{default:a(()=>[e(C,{class:"w-[280px]",modelValue:l(s).name,"onUpdate:modelValue":u[0]||(u[0]=t=>l(s).name=t),placeholder:"\u8BF7\u8F93\u5165\u5F62\u8C61\u540D\u79F0",clearable:"",onKeyup:k(l(b),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(d,{label:"\u521B\u5EFA\u4EBA"},{default:a(()=>[e(C,{class:"w-[280px]",modelValue:l(s).user,"onUpdate:modelValue":u[1]||(u[1]=t=>l(s).user=t),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7/\u6635\u79F0",clearable:"",onKeyup:k(l(b),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(d,{label:"\u5F62\u8C61\u72B6\u6001"},{default:a(()=>[e(B,{class:"w-[280px]",modelValue:l(s).is_disable,"onUpdate:modelValue":u[2]||(u[2]=t=>l(s).is_disable=t)},{default:a(()=>[e(v,{label:"\u5168\u90E8",value:""}),e(v,{label:"\u5F00\u542F",value:0}),e(v,{label:"\u5173\u95ED",value:1})]),_:1},8,["modelValue"])]),_:1}),e(d,null,{default:a(()=>[e(c,{type:"primary",onClick:l(b)},{default:a(()=>[_("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(c,{onClick:l(x)},{default:a(()=>[_("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(E,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[g((m(),f(I,{class:"mt-4",size:"large",data:l(r).lists},{default:a(()=>[e(n,{label:"ID","min-width":"80",prop:"id"}),e(n,{label:"\u5F62\u8C61\u5934\u50CF","min-width":"110"},{default:a(({row:t})=>[e(P,{size:50,src:t.avatar},null,8,["src"])]),_:1}),e(n,{label:"\u5F62\u8C61\u5C01\u9762","min-width":"110"},{default:a(({row:t})=>[o("div",fe,[t.image?(m(),f(U,{key:0,class:"flex-none",src:t.image,width:80,height:40,"preview-src-list":[t.image],"preview-teleported":"",fit:"contain"},null,8,["src","preview-src-list"])):re("",!0)])]),_:1}),e(n,{label:"\u5F62\u8C61\u540D\u79F0",prop:"name","min-width":"120"}),e(n,{label:"\u521B\u5EFA\u4EBA","min-width":"120"},{default:a(({row:t})=>[e(K,{placement:"top",width:"220px",trigger:"hover"},{reference:a(()=>{var i;return[o("div",he,[o("div",Ce,h((i=t.user)==null?void 0:i.nickname),1)])]}),default:a(()=>{var i,V;return[o("div",Fe,[be,o("span",null,h((i=t.user)==null?void 0:i.nickname),1)]),o("div",ve,[ge,o("span",null,h((V=t.user)==null?void 0:V.sn),1)])]}),_:2},1024)]),_:1}),e(n,{label:"\u914D\u97F3\u89D2\u8272",prop:"dubbing","min-width":"100"}),e(n,{label:"\u95F2\u65F6\u56DE\u590D",prop:"idle_reply","min-width":"150"}),e(n,{label:"\u72B6\u6001","min-width":"100"},{default:a(({row:t})=>[e(R,{onChange:i=>y(t.id),modelValue:t.is_disable,"onUpdate:modelValue":i=>t.is_disable=i,"active-value":0,"inactive-value":1},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),e(n,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(n,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:a(({row:t})=>[o("div",null,[g((m(),f(c,{type:"primary",link:""},{default:a(()=>[e(S,{to:{path:l(Z)("kb.digital/detail"),query:{id:t.id}}},{default:a(()=>[_(" \u67E5\u770B ")]),_:2},1032,["to"])]),_:2},1024)),[[w,["kb.digital/detail"]]]),g((m(),f(c,{type:"danger",onClick:i=>D(t.id),link:""},{default:a(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[w,["kb.digital/del"]]])])]),_:1})]),_:1},8,["data"])),[[N,l(r).loading]]),o("div",Ee,[e(L,{modelValue:l(r),"onUpdate:modelValue":u[3]||(u[3]=t=>me(r)?r.value=t:null),onChange:l(F)},null,8,["modelValue","onChange"])])]),_:1})])}}});export{mt as default};
