import r from"./decoration-img.16e6b284.js";import{d as p,b as m,o as e,c as t,L as x,F as h,a7 as u,a as s,K as _,W as f,V as i}from"./@vue.a11433a6.js";const v={class:"index-ad px-[15px]"},y={class:"h-full bg-white rounded-[10px] overflow-hidden"},g={class:"p-[10px] text-center"},w={class:"text-xl line-clamp-1 font-medium"},k={class:"text-tx-secondary line-clamp-1 mt-[2px] text-sm"},B={key:1,class:"min-h-[100px] flex items-center justify-center"},b=p({__name:"content",props:{isHidden:{type:Boolean},content:{}},emits:["update:isHidden"],setup(c,{emit:D}){const l=c,a=m(()=>l.content.data.filter(n=>n.isShow));return(n,F)=>(e(),t("div",v,[a.value.length?(e(),t("div",{key:0,class:"grid mx-[-7px]",style:x({"grid-template-columns":`repeat(${n.content.showType}, minmax(0, 1fr))`})},[(e(!0),t(h,null,u(a.value,(o,d)=>(e(),t("div",{class:"w-full px-[7px] mt-[15px]",key:d},[s("div",y,[s("div",{class:_([n.content.showType==2?"h-[122px]":"h-[80px]"])},[f(r,{src:o.image,width:"100%",height:"100%",fit:"cover"},null,8,["src"])],2),s("div",g,[s("div",w,i(o.title),1),s("div",k,i(o.desc),1)])])]))),128))],4)):(e(),t("div",B,"\u5E7F\u544A\u533A\u57DF"))]))}});export{b as _};
