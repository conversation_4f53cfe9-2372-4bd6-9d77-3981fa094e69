import{a7 as S,a8 as M,H as U,k as b,G as I,K as T,L as V,w as $,M as N}from"./element-plus.5bcb7c8a.js";import{e as x}from"./gift.3f2d22cd.js";import{d as j,r as f,a0 as z,i as L,o as i,c as v,R as A,P as B,Q as t,W as e,a,V as l,U as d,T as C,bk as P,bj as H}from"./@vue.a11433a6.js";import{_ as K}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const r=m=>(P("data-v-256ce2e7"),m=m(),H(),m),O={class:"stat-content"},Q={class:"stat-value"},W=r(()=>a("div",{class:"stat-label"},"\u4ECA\u65E5\u8D60\u9001\u603B\u989D",-1)),q={class:"stat-content"},J={class:"stat-value"},X=r(()=>a("div",{class:"stat-label"},"\u4ECA\u65E5\u8D60\u9001\u7B14\u6570",-1)),Y={class:"stat-content"},Z={class:"stat-value"},tt=r(()=>a("div",{class:"stat-label"},"\u53C2\u4E0E\u7528\u6237\u6570",-1)),et={class:"stat-content"},at={class:"stat-value"},st=r(()=>a("div",{class:"stat-label"},"\u5E73\u5747\u8D60\u9001\u91D1\u989D",-1)),ut=r(()=>a("div",{class:"card-header"},[a("span",null,"\u8D60\u9001\u6392\u884C\u699C")],-1)),ot={class:"text-red-500"},lt={key:0,class:"text-center py-4 text-gray-500"},nt=r(()=>a("div",{class:"card-header"},[a("span",null,"\u63A5\u6536\u6392\u884C\u699C")],-1)),it={class:"text-green-500"},rt={key:0,class:"text-center py-4 text-gray-500"},ct={class:"card-header"},dt=r(()=>a("span",null,"\u6700\u8FD1\u8BB0\u5F55",-1)),_t={class:"text-red-500 font-medium"},pt={key:0,class:"text-center py-4 text-gray-500"},mt=j({name:"UserGiftStatistics",__name:"index",setup(m){const _=f(!1),p=z({todayGiftAmount:"0.00",todayGiftCount:"0",totalUsers:"0",avgAmount:"0.00"}),h=f([]),D=f([]),g=f([]),F=async()=>{try{if(_.value=!0,console.log("\u5F00\u59CB\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E..."),typeof x!="function"){console.error("giftGetStatisticsApi \u51FD\u6570\u672A\u5B9A\u4E49"),b.error("API\u51FD\u6570\u672A\u6B63\u786E\u5BFC\u5165");return}const u=await x();console.log("\u83B7\u53D6\u5230\u7684\u7EDF\u8BA1\u6570\u636E:",u),u&&(u.stats&&Object.assign(p,{todayGiftAmount:u.stats.todayGiftAmount||"0.00",todayGiftCount:u.stats.todayGiftCount||"0",totalUsers:u.stats.totalUsers||"0",avgAmount:u.stats.avgAmount||"0.00"}),h.value=u.giftRanking||[],D.value=u.receiveRanking||[],g.value=u.recentRecords||[]),b.success("\u7EDF\u8BA1\u6570\u636E\u52A0\u8F7D\u6210\u529F")}catch(u){console.error("\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:",u),b.error(`\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25: ${u}`)}finally{_.value=!1}},w=()=>{F()};return L(()=>{F()}),(u,ft)=>{const n=I,c=S,k=M,o=T,E=V,G=$,R=U,y=N;return i(),v("div",null,[A((i(),B(k,{gutter:20,class:"mb-4"},{default:t(()=>[e(c,{span:6},{default:t(()=>[e(n,{class:"stat-card"},{default:t(()=>[a("div",O,[a("div",Q,l(p.todayGiftAmount||"0.00"),1),W])]),_:1})]),_:1}),e(c,{span:6},{default:t(()=>[e(n,{class:"stat-card"},{default:t(()=>[a("div",q,[a("div",J,l(p.todayGiftCount||"0"),1),X])]),_:1})]),_:1}),e(c,{span:6},{default:t(()=>[e(n,{class:"stat-card"},{default:t(()=>[a("div",Y,[a("div",Z,l(p.totalUsers||"0"),1),tt])]),_:1})]),_:1}),e(c,{span:6},{default:t(()=>[e(n,{class:"stat-card"},{default:t(()=>[a("div",et,[a("div",at,l(p.avgAmount||"0.00"),1),st])]),_:1})]),_:1})]),_:1})),[[y,_.value]]),A((i(),B(k,{gutter:20},{default:t(()=>[e(c,{span:12},{default:t(()=>[e(n,{title:"\u8D60\u9001\u6392\u884C\u699C",class:"!border-none",shadow:"never"},{header:t(()=>[ut]),default:t(()=>[e(E,{data:h.value,stripe:""},{default:t(()=>[e(o,{type:"index",label:"\u6392\u540D",width:"60"}),e(o,{label:"\u7528\u6237",prop:"nickname"},{default:t(({row:s})=>[d(l(s.nickname||`\u7528\u6237${s.user_id}`),1)]),_:1}),e(o,{label:"\u8D60\u9001\u91D1\u989D",prop:"amount",width:"120"},{default:t(({row:s})=>[a("span",ot,l(Math.floor(parseFloat(s.amount))),1)]),_:1})]),_:1},8,["data"]),h.value.length===0?(i(),v("div",lt," \u6682\u65E0\u6570\u636E ")):C("",!0)]),_:1})]),_:1}),e(c,{span:12},{default:t(()=>[e(n,{title:"\u63A5\u6536\u6392\u884C\u699C",class:"!border-none",shadow:"never"},{header:t(()=>[nt]),default:t(()=>[e(E,{data:D.value,stripe:""},{default:t(()=>[e(o,{type:"index",label:"\u6392\u540D",width:"60"}),e(o,{label:"\u7528\u6237",prop:"nickname"},{default:t(({row:s})=>[d(l(s.nickname||`\u7528\u6237${s.user_id}`),1)]),_:1}),e(o,{label:"\u63A5\u6536\u91D1\u989D",prop:"amount",width:"120"},{default:t(({row:s})=>[a("span",it,l(Math.floor(parseFloat(s.amount))),1)]),_:1})]),_:1},8,["data"]),D.value.length===0?(i(),v("div",rt," \u6682\u65E0\u6570\u636E ")):C("",!0)]),_:1})]),_:1})]),_:1})),[[y,_.value]]),e(n,{title:"\u6700\u8FD1\u8BB0\u5F55",class:"!border-none mt-4",shadow:"never"},{header:t(()=>[a("div",ct,[dt,e(G,{type:"primary",size:"small",onClick:w},{default:t(()=>[d("\u5237\u65B0\u6570\u636E")]),_:1})])]),default:t(()=>[A((i(),B(E,{data:g.value,stripe:""},{default:t(()=>[e(o,{label:"\u6D41\u6C34\u53F7",prop:"gift_sn",width:"160"}),e(o,{label:"\u8D60\u9001\u7528\u6237",prop:"from_user_nickname"},{default:t(({row:s})=>[d(l(s.from_user_nickname||`\u7528\u6237${s.from_user_id}`),1)]),_:1}),e(o,{label:"\u63A5\u6536\u7528\u6237",prop:"to_user_nickname"},{default:t(({row:s})=>[d(l(s.to_user_nickname||`\u7528\u6237${s.to_user_id}`),1)]),_:1}),e(o,{label:"\u91D1\u989D",prop:"gift_amount",width:"100"},{default:t(({row:s})=>[a("span",_t,l(Math.floor(parseFloat(s.gift_amount))),1)]),_:1}),e(o,{label:"\u65F6\u95F4",prop:"create_time",width:"160"}),e(o,{label:"\u72B6\u6001",prop:"status",width:"80"},{default:t(({row:s})=>[e(R,{type:"success",size:"small"},{default:t(()=>[d("\u6210\u529F")]),_:1})]),_:1})]),_:1},8,["data"])),[[y,_.value]]),g.value.length===0?(i(),v("div",pt," \u6682\u65E0\u6570\u636E ")):C("",!0)]),_:1})])}}});const Zt=K(mt,[["__scopeId","data-v-256ce2e7"]]);export{Zt as default};
