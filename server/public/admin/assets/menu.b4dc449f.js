import{_ as h}from"./index.88e852a7.js";import{a3 as v,G as C,w as E}from"./element-plus.5bcb7c8a.js";import x from"./oa-phone.3f3ad124.js";import D from"./oa-attr.cf73877c.js";import{u as B}from"./useMenuOa.88866e3a.js";import{d as c,aj as b,o as e,c as w,W as o,Q as t,a,R as n,P as p,u as s,U as u}from"./@vue.a11433a6.js";import{_ as k}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.4a09b22e.js";import"./index.324d704f.js";import"./oa-menu-form.vue_vue_type_script_setup_true_lang.609c965e.js";import"./oa-menu-form-edit.vue_vue_type_script_setup_true_lang.e1e7307e.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./wx_oa.1673a390.js";const y={class:"menu-oa"},g={class:"lg:flex flex-1"},A={class:"mt-4 lg:mt-0 max-w-[400px]"},O=c({name:"wxOaMenu"}),P=c({...O,setup(M){const{getOaMenuFunc:_,handleSave:l,handlePublish:d}=B(void 0);return _(),(N,V)=>{const f=v,r=C,i=E,F=h,m=b("perms");return e(),w("div",y,[o(r,{class:"!border-none",shadow:"never"},{default:t(()=>[o(f,{type:"warning",title:"\u914D\u7F6E\u5FAE\u4FE1\u516C\u4F17\u53F7\u83DC\u5355\uFF0C\u70B9\u51FB\u786E\u8BA4\uFF0C\u4FDD\u5B58\u83DC\u5355\u5E76\u53D1\u5E03\u81F3\u5FAE\u4FE1\u516C\u4F17\u53F7",closable:!1,"show-icon":""})]),_:1}),o(r,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[a("div",g,[o(x),a("div",A,[o(D)])])]),_:1}),o(F,null,{default:t(()=>[n((e(),p(i,{type:"primary",onClick:s(l)},{default:t(()=>[u(" \u4FDD\u5B58 ")]),_:1},8,["onClick"])),[[m,["channel.official_account_menu/save"]]]),n((e(),p(i,{type:"primary",onClick:s(d)},{default:t(()=>[u(" \u53D1\u5E03 ")]),_:1},8,["onClick"])),[[m,["channel.official_account_menu/saveAndPublish"]]])]),_:1})])}}});const Oo=k(P,[["__scopeId","data-v-02b13936"]]);export{Oo as default};
