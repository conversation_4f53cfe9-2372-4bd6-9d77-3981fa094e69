import{M as l}from"./element-plus.5bcb7c8a.js";import{a as c,u as d}from"./vue-router.919c7bec.js";import{e as f,f as _}from"./ai_creation.a70c015c.js";import{_ as g}from"./model-form.vue_vue_type_script_setup_true_lang.b1f85dbb.js";import{d as e,a0 as y,r as v,R as C,u as m,o as D,P as b,j as B}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.88e852a7.js";import"./index.324d704f.js";import"./picker.9a1dad65.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./useDictOptions.583d6eb9.js";const M=e({name:"aiCreationModelAdd"}),kt=e({...M,setup(R){const p=c(),a=d(),t=y({name:"",tips:"",category_id:"",content:"",sort:0,status:1,system:"",image:"",form:[],n:1,temperature:.7,top_p:.9,presence_penalty:.5}),s=async()=>{const o={...t,form:JSON.stringify(t.form)};await f(o),p.back()},r=v(!1);return(async()=>{r.value=!0;try{const o=await _({id:a.query.id});Object.assign(t,o)}finally{r.value=!1}})(),(o,i)=>{const n=l;return C((D(),b(g,{modelValue:m(t),"onUpdate:modelValue":i[0]||(i[0]=u=>B(t)?t.value=u:null),"header-title":o.$route.meta.title||"\u7F16\u8F91\u521B\u4F5C\u6A21\u578B",onSubmit:s},null,8,["modelValue","header-title"])),[[n,m(r)]])}}});export{kt as default};
