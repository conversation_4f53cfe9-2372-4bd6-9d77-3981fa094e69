import{K as B,C as E,t as U,w as _,L as x}from"./element-plus.5bcb7c8a.js";import{_ as T}from"./picker.9a1dad65.js";import{b as D}from"./index.850efb0d.js";import{v as S}from"./@vueuse.a2407f20.js";import{S as A}from"./sortablejs.98edf555.js";import{d as R,r as N,i as $,n as z,o as M,c as q,W as e,Q as a,a as c,U as f,u as K}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const L=c("div",{class:"font-medium text-xl mb-4"},"\u4F1A\u5458\u6743\u76CA",-1),P={class:"move-icon cursor-move"},Q={class:"py-[8px]"},Le=R({__name:"member-benefits",props:{modelValue:{}},emits:["update:modelValue"],setup(b,{emit:V}){const h=b,v=V,{modelValue:i}=S(h,v),d=N(),w=()=>{i.value.benefits_list.push({id:new Date().getTime(),name:"",image:"",describe:"",status:1})},k=n=>{i.value.benefits_list.splice(n,1)},C=()=>{var u;const n=(u=d.value)==null?void 0:u.$el.querySelector(".el-table__body tbody");A.create(n,{animation:150,handle:".move-icon",onEnd:({newIndex:s,oldIndex:o})=>{var p;console.log(s,o);const m=(p=i.value)==null?void 0:p.benefits_list,r=m.splice(o,1)[0];m.splice(s,0,r),i.value.benefits_list=m}})};return $(async()=>{await z(),C()}),(n,u)=>{const s=D,o=B,m=T,r=E,p=U,y=_,F=x,g=_;return M(),q("div",null,[L,e(F,{size:"large","row-key":"id",ref_key:"benefitsTableRef",ref:d,data:K(i).benefits_list},{default:a(()=>[e(o,{width:"50"},{default:a(()=>[c("div",P,[e(s,{name:"el-icon-Rank"})])]),_:1}),e(o,{label:"\u56FE\u6807","min-width":"80"},{default:a(({row:t})=>[c("div",Q,[e(m,{size:"60px",modelValue:t.image,"onUpdate:modelValue":l=>t.image=l},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),e(o,{label:"\u540D\u79F0","min-width":"130"},{default:a(({row:t})=>[e(r,{modelValue:t.name,"onUpdate:modelValue":l=>t.name=l,placeholder:"\u8BF7\u8F93\u5165",clearable:"",class:"w-[80%]"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(o,{label:"\u7528\u91CF","min-width":"130"},{default:a(({row:t})=>[e(r,{modelValue:t.describe,"onUpdate:modelValue":l=>t.describe=l,placeholder:"\u8BF7\u8F93\u5165",clearable:"",class:"w-[80%]"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(o,{label:"\u72B6\u6001","min-width":"90"},{default:a(({row:t})=>[e(p,{modelValue:t.status,"onUpdate:modelValue":l=>t.status=l,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(o,{label:"\u64CD\u4F5C","min-width":"130",fixed:"right"},{default:a(({row:t,$index:l})=>[e(y,{type:"danger",link:"",onClick:W=>k(l)},{default:a(()=>[f(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e(g,{type:"primary",class:"mt-4",onClick:w},{default:a(()=>[f("\u6DFB\u52A0")]),_:1})])}}});export{Le as default};
