import{r as t}from"./index.850efb0d.js";function e(r){return t.get({url:"/draw.draw_prompt_category/lists",params:r})}function o(r){return t.post({url:"/draw.draw_prompt_category/add",params:r})}function d(r){return t.post({url:"/draw.draw_prompt_category/edit",params:r})}function u(r){return t.post({url:"/draw.draw_prompt_category/delete",params:r})}function s(r){return t.post({url:"/draw.draw_prompt_category/status",params:r})}export{o as a,s as b,u as d,d as e,e as g};
