import{C as z,D as A,I,J as K,w as q,F as G,G as J,K as M,t as O,L as Q,M as W}from"./element-plus.5bcb7c8a.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as C,b as X}from"./index.850efb0d.js";import{d as Y,a0 as w,r as Z,s as ee,aj as te,o as i,c as ae,W as e,Q as t,a8 as oe,U as d,a as E,R as f,P as p,T as le,n as ne}from"./@vue.a11433a6.js";import{b as se,d as ie,u as ue}from"./example_category.e2686204.js";import{_ as re}from"./edit.vue_vue_type_script_setup_true_lang.c2f61ca2.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const me={class:"container"},de={class:"mb-4 flex justify-between"},pe={class:"flex justify-end mt-4"},et=Y({__name:"index",setup(ce){const s=w({name:"",status:"",page:1}),l=w({page:1,pageSize:10,loading:!0,total:0,lists:[]}),g=Z(!1),F=ee(),u=async()=>{try{l.loading=!0;const a=await se({...s,page_no:l.page,page_size:l.pageSize});l.lists=a.lists,l.total=a.count}catch(a){console.error(a)}finally{l.loading=!1}},b=()=>{l.page=1,u()},V=()=>{s.name="",s.status="",b()},h=async(a,n={})=>{var c;g.value=!0,await ne(),(c=F.value)==null||c.open(a,n)},x=()=>{h("add")},D=a=>{h("edit",a)},S=async a=>{await C.confirm("\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u7C7B\u522B\u5417\uFF1F"),await ie({id:a.id}),C.msgSuccess("\u5220\u9664\u6210\u529F"),u()},$=async a=>{await ue({id:a.id,status:a.status}),C.msgSuccess("\u4FEE\u6539\u6210\u529F")};return u(),(a,n)=>{const c=z,v=A,B=I,P=K,r=q,U=G,k=J,R=X,m=M,T=O,L=Q,N=H,y=te("perms"),j=W;return i(),ae("div",me,[e(k,{class:"box-card !border-none",shadow:"never"},{default:t(()=>[e(U,{ref:"formRef",model:s,inline:""},{default:t(()=>[e(v,{label:"\u7C7B\u522B\u540D\u79F0"},{default:t(()=>[e(c,{modelValue:s.name,"onUpdate:modelValue":n[0]||(n[0]=o=>s.name=o),placeholder:"\u8BF7\u8F93\u5165\u7C7B\u522B\u540D\u79F0",clearable:"",onKeyup:oe(b,["enter"])},null,8,["modelValue"])]),_:1}),e(v,{label:"\u72B6\u6001"},{default:t(()=>[e(P,{modelValue:s.status,"onUpdate:modelValue":n[1]||(n[1]=o=>s.status=o),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:""},{default:t(()=>[e(B,{label:"\u5F00\u542F",value:1}),e(B,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(r,{type:"primary",onClick:b},{default:t(()=>[d("\u67E5\u8BE2")]),_:1}),e(r,{onClick:V},{default:t(()=>[d("\u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(k,{class:"box-card !border-none mt-4",shadow:"never"},{default:t(()=>[E("div",de,[E("div",null,[f((i(),p(r,{type:"primary",onClick:x},{icon:t(()=>[e(R,{name:"el-icon-Plus"})]),default:t(()=>[d(" \u65B0\u589E ")]),_:1})),[[y,["kb.example_category/add"]]])])]),f((i(),p(L,{data:l.lists,"row-style":{cursor:"pointer"},height:"580"},{default:t(()=>[e(m,{label:"\u7C7B\u522B\u540D\u79F0",prop:"name","min-width":"200"}),e(m,{label:"\u6392\u5E8F",prop:"sort","min-width":"100"}),e(m,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:o})=>[e(T,{modelValue:o.status,"onUpdate:modelValue":_=>o.status=_,"active-value":1,"inactive-value":0,onChange:_=>$(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"180"}),e(m,{label:"\u64CD\u4F5C",width:"180",fixed:"right"},{default:t(({row:o})=>[f((i(),p(r,{type:"primary",link:"",onClick:_=>D(o)},{default:t(()=>[d(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["kb.example_category/edit"]]]),f((i(),p(r,{type:"danger",link:"",onClick:_=>S(o)},{default:t(()=>[d(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["kb.example_category/del"]]])]),_:1})]),_:1},8,["data"])),[[j,l.loading]]),E("div",pe,[e(N,{modelValue:l,"onUpdate:modelValue":n[2]||(n[2]=o=>l=o),onChange:u},null,8,["modelValue"])])]),_:1}),g.value?(i(),p(re,{key:0,ref_key:"editRef",ref:F,onSuccess:u,onClose:n[3]||(n[3]=o=>g.value=!1)},null,512)):le("",!0)])}}});export{et as default};
