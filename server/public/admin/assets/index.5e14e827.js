import{C as L,D as T,I as j,J as A,w as I,F as N,G as M,K as q,t as z,L as G,M as J}from"./element-plus.5bcb7c8a.js";import{_ as O}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{d as Q,r as W,s as H,i as X,aj as Y,o as r,c as Z,W as e,Q as t,u as o,a8 as ee,U as p,R as f,P as b,a as w,j as te,n as g}from"./@vue.a11433a6.js";import{u as oe}from"./usePaging.b48cb079.js";import{_ as ae}from"./edit.vue_vue_type_script_setup_true_lang.f5a464c4.js";import{f as le}from"./index.850efb0d.js";import{d as ne,e as ie,f as ue}from"./robot_square.168fdb05.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const se={class:"flex justify-end mt-4"},Xe=Q({__name:"index",setup(me){const l=W({name:"",is_enable:""}),d=H(),h=async()=>{await g(),d.value.open()},{pager:s,getLists:c,resetPage:E,resetParams:y}=oe({fetchFun:ne,params:l.value}),B=async n=>{await ie({id:n})},V=async n=>{await g(),d.value.open("edit"),d.value.setFormData(n)},x=async n=>{await le.confirm("\u786E\u5B9A\u5220\u9664\uFF1F"),await ue({id:n}),c()};return X(()=>{c()}),(n,i)=>{const D=L,C=T,v=j,P=A,m=I,R=N,k=M,u=q,S=z,U=G,$=O,F=Y("perms"),K=J;return r(),Z("div",null,[e(k,{class:"!border-none",shadow:"never"},{default:t(()=>[e(R,{ref:"formRef",class:"mb-[-16px]",model:o(l),inline:!0},{default:t(()=>[e(C,{label:"\u5206\u7C7B\u540D\u79F0"},{default:t(()=>[e(D,{class:"w-[280px]",modelValue:o(l).name,"onUpdate:modelValue":i[0]||(i[0]=a=>o(l).name=a),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:"",onKeyup:ee(o(E),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(C,{label:"\u72B6\u6001"},{default:t(()=>[e(P,{class:"w-[280px]",modelValue:o(l).is_enable,"onUpdate:modelValue":i[1]||(i[1]=a=>o(l).is_enable=a)},{default:t(()=>[e(v,{label:"\u5168\u90E8",value:""}),e(v,{label:"\u5F00\u542F",value:1}),e(v,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(C,null,{default:t(()=>[e(m,{type:"primary",onClick:o(E)},{default:t(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(m,{onClick:o(y)},{default:t(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[f((r(),b(m,{type:"primary",onClick:h},{default:t(()=>[p(" + \u65B0\u589E ")]),_:1})),[[F,["kb.robotCate/add"]]]),f((r(),b(U,{class:"mt-4",size:"large",data:o(s).lists},{default:t(()=>[e(u,{label:"\u7C7B\u522B\u540D\u79F0",prop:"name","min-width":"120"}),e(u,{label:"\u667A\u80FD\u4F53\u6570\u91CF",prop:"example_sum","min-width":"100"}),e(u,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:a})=>[e(S,{onChange:_=>B(a.id),modelValue:a.is_enable,"onUpdate:modelValue":_=>a.is_enable=_,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(u,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:t(({row:a})=>[w("div",null,[f((r(),b(m,{type:"primary",link:"",onClick:_=>V(a)},{default:t(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[F,["kb.robotCate/edit"]]]),f((r(),b(m,{type:"danger",onClick:_=>x(a.id),link:""},{default:t(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[F,["kb.robotCate/del"]]])])]),_:1})]),_:1},8,["data"])),[[K,o(s).loading]]),w("div",se,[e($,{modelValue:o(s),"onUpdate:modelValue":i[2]||(i[2]=a=>te(s)?s.value=a:null),onChange:o(c)},null,8,["modelValue","onChange"])])]),_:1}),e(ae,{ref_key:"editPopup",ref:d,onSuccess:o(c)},null,8,["onSuccess"])])}}});export{Xe as default};
