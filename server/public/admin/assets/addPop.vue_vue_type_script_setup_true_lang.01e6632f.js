import{C as R,D as S,w as T,F as I,K as L,o as U,L as N,M as j}from"./element-plus.5bcb7c8a.js";import{_ as q}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{d as z,a0 as M,s as $,o as F,c as K,W as e,Q as t,u as o,$ as Q,U as r,R as W,P as G,a as b,V as H,j as J}from"./@vue.a11433a6.js";import{P as O}from"./index.324d704f.js";import{u as X}from"./usePaging.b48cb079.js";import{q as Y}from"./consumer.e39aaadf.js";import{d as Z}from"./distribution.4cae9c42.js";const ee={class:"edit-popup"},te={class:"flex items-center"},oe={class:"flex justify-end mt-4"},me=z({__name:"addPop",emits:["success","close"],setup(ae,{expose:g,emit:C}){const i=M({keyword:"",is_distribution:0}),{pager:n,getLists:m,resetPage:h,resetParams:E}=X({fetchFun:Y,params:i}),p=C,d=$(),k=()=>{var u;(u=d.value)==null||u.open(),m()},v=()=>{p("close")},w=async()=>{await Z({user_ids:c}),p("close")};let c=[];const x=u=>{c=u.map(a=>a.id),console.log(c)};return g({open:k}),(u,a)=>{const y=R,_=S,f=T,B=I,l=L,V=U,D=N,P=q,A=j;return F(),K("div",ee,[e(O,{ref_key:"popupRef",ref:d,title:"\u5F00\u901A\u5206\u9500\u5546",async:!0,width:"950px",onConfirm:w,onClose:v,cancelButtonText:"\u53D6\u6D88\u5F00\u901A",confirmButtonText:"\u786E\u8BA4\u5F00\u901A"},{default:t(()=>[e(B,{ref:"formRef",class:"mb-[-16px]",model:o(i),inline:!0,onSubmit:a[1]||(a[1]=Q(()=>{},["prevent"]))},{default:t(()=>[e(_,{label:"\u7528\u6237\u4FE1\u606F"},{default:t(()=>[e(y,{class:"w-[280px]",modelValue:o(i).keyword,"onUpdate:modelValue":a[0]||(a[0]=s=>o(i).keyword=s),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237ID\u7F16\u53F7/\u7528\u6237\u6635\u79F0",clearable:""},null,8,["modelValue"])]),_:1}),e(_,null,{default:t(()=>[e(f,{type:"primary",onClick:o(h)},{default:t(()=>[r("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(f,{onClick:o(E)},{default:t(()=>[r("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),W((F(),G(D,{size:"large",data:o(n).lists,height:"500px",class:"mt-4",onSelectionChange:x},{default:t(()=>[e(l,{type:"selection",width:"55"}),e(l,{label:"\u7528\u6237ID",prop:"sn",width:"120"}),e(l,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:t(({row:s})=>[b("div",te,[e(V,{src:s.avatar,size:50,class:"mr-2"},null,8,["src"]),r(H(s.nickname),1)])]),_:1}),e(l,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"}),e(l,{label:"\u662F\u5426\u5206\u9500\u5546",width:"120"},{default:t(()=>[r("\u5426")]),_:1}),e(l,{label:"\u6CE8\u518C\u65F6\u95F4",prop:"create_time",sortable:""})]),_:1},8,["data"])),[[A,o(n).loading]]),b("div",oe,[e(P,{modelValue:o(n),"onUpdate:modelValue":a[2]||(a[2]=s=>J(n)?n.value=s:null),onChange:o(m)},null,8,["modelValue","onChange"])])]),_:1},512)])}}});export{me as _};
