import{r as o}from"./index.850efb0d.js";function u(t){return o.get({url:"/kb.robotCate/all",params:t})}function e(t){return o.get({url:"/kb.robotCate/lists",params:t})}function s(t){return o.post({url:"/kb.robotCate/add",params:t})}function a(t){return o.post({url:"/kb.robotCate/edit",params:t})}function n(t){return o.post({url:"/kb.robotCate/del",params:t})}function b(t){return o.post({url:"/kb.robotCate/changeStatus",params:t})}function i(t){return o.get({url:"/kb.square/lists",params:t})}function l(t){return o.post({url:"/kb.square/edit",params:t})}function f(t){return o.post({url:"/kb.square/del",params:t})}function p(t){return o.post({url:"/kb.square/setStatus",params:t})}function q(t){return o.post({url:"/kb.square/setSort",params:t})}function c(){return o.post({url:"/kb.square/getConfig"})}function C(t){return o.post({url:"/kb.square/setConfig",params:t})}function g(t){return o.post({url:"/kb.square/verifyStatus",params:t})}export{u as a,g as b,s as c,e as d,b as e,n as f,i as g,l as h,f as i,p as j,q as k,c as l,a as p,C as s};
