import{C as $,D as A,I as K,J as L,w as I,F as N,G as j,K as q,t as z,L as G,M as J}from"./element-plus.5bcb7c8a.js";import{d as M,s as O,a0 as Q,r as W,aj as H,o as s,c as X,W as e,Q as t,u as a,a8 as Y,U as p,R as d,P as r,T as Z,n as E}from"./@vue.a11433a6.js";import{u as ee}from"./usePaging.b48cb079.js";import{_ as te}from"./edit.vue_vue_type_script_setup_true_lang.7dae4ae5.js";import{d as ae,c as le,s as oe}from"./type.d6a09a6d.js";import{f as ne}from"./index.850efb0d.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const Oe=M({__name:"index",setup(se){const c=O(),u=Q({name:"",status:""}),_=W(!1),h=async()=>{var o;_.value=!0,await E(),(o=c.value)==null||o.open("add")},w=async o=>{var n,f;_.value=!0,await E(),(n=c.value)==null||n.open("edit"),(f=c.value)==null||f.setFormData(o)},V=async o=>{await ne.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await ae({id:o}),v()},D=o=>{le({id:o})},{pager:y,getLists:v,resetPage:g,resetParams:x}=ee({fetchFun:oe,params:u});return v(),(o,n)=>{const f=$,F=A,b=K,S=L,m=I,P=N,B=j,i=q,R=z,T=G,C=H("perms"),U=J;return s(),X("div",null,[e(B,{shadow:"never",class:"!border-none mt-4"},{default:t(()=>[e(P,{ref:"formRef",class:"mb-[-16px]",model:a(u),inline:!0},{default:t(()=>[e(F,{label:"\u7C7B\u522B\u540D\u79F0"},{default:t(()=>[e(f,{class:"w-[280px]",modelValue:a(u).name,"onUpdate:modelValue":n[0]||(n[0]=l=>a(u).name=l),placeholder:"\u8BF7\u8F93\u5165\u7C7B\u522B\u540D\u79F0",clearable:"",onKeyup:Y(a(g),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(F,{label:"\u7C7B\u522B\u72B6\u6001"},{default:t(()=>[e(S,{class:"w-[280px]",modelValue:a(u).status,"onUpdate:modelValue":n[1]||(n[1]=l=>a(u).status=l)},{default:t(()=>[e(b,{label:"\u5168\u90E8",value:""}),e(b,{label:"\u5F00\u542F",value:"1"}),e(b,{label:"\u5173\u95ED",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(F,null,{default:t(()=>[e(m,{type:"primary",onClick:a(g)},{default:t(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(m,{onClick:a(x)},{default:t(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(B,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[d((s(),r(m,{type:"primary",class:"mb-4",onClick:h},{default:t(()=>[p(" \u65B0\u589E\u89D2\u8272\u7C7B\u522B ")]),_:1})),[[C,["skill.skillCategory/add"]]]),d((s(),r(T,{size:"large",data:a(y).lists},{default:t(()=>[e(i,{label:"\u7C7B\u522B\u540D\u79F0",prop:"name","min-width":"100"}),e(i,{label:"\u88AB\u4F7F\u7528\u6570","min-width":"120",prop:"skill_count"}),d((s(),r(i,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:l})=>[e(R,{onChange:k=>D(l.id),modelValue:l.status,"onUpdate:modelValue":k=>l.status=k,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1})),[[C,["skill.skillCategory/status"]]]),e(i,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time",sortable:"","min-width":"100"}),e(i,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:t(({row:l})=>[d((s(),r(m,{type:"primary",link:"",onClick:k=>w(l)},{default:t(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[C,["skill.skillCategory/edit"]]]),d((s(),r(m,{type:"danger",link:"",onClick:k=>V(l.id)},{default:t(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[C,["skill.skillCategory/del"]]])]),_:1})]),_:1},8,["data"])),[[U,a(y).loading]])]),_:1}),a(_)?(s(),r(te,{key:0,ref_key:"editRef",ref:c,onSuccess:a(v),onClose:n[2]||(n[2]=l=>_.value=!1)},null,8,["onSuccess"])):Z("",!0)])}}});export{Oe as default};
