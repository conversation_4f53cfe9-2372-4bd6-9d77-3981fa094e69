import{a5 as N,H as R,a6 as P,a7 as V,a8 as z,c as J,o as O,G as T,M as L,k as M}from"./element-plus.5bcb7c8a.js";import{U as B,O as D}from"./@element-plus.1e23f767.js";import{P as U}from"./index.324d704f.js";import{g as $}from"./robot_edit_log.ad074937.js";import{d as j,s as G,r as C,o as i,P as v,Q as e,R as H,c as _,W as s,U as c,V as l,u as t,a,T as y,bk as Q,bj as W}from"./@vue.a11433a6.js";import{_ as q}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const d=p=>(Q("data-v-195628fb"),p=p(),W(),p),K={class:"flex items-center"},X={class:"font-medium"},Y={class:"text-xs text-gray-500"},Z={class:"flex items-center"},tt={class:"font-medium"},et={class:"text-xs text-gray-500"},ot={key:0,class:"text-red-500"},st={key:1,class:"text-gray-400"},at={class:"mt-6"},ut=d(()=>a("h3",{class:"text-lg font-medium mb-4"},"\u7F16\u8F91\u6570\u636E\u5BF9\u6BD4",-1)),rt={class:"card-header"},lt=d(()=>a("span",null,"\u7F16\u8F91\u524D\u6570\u636E",-1)),it={key:0},_t={class:"data-content"},nt={key:1,class:"text-gray-500 text-center py-8"},ct=d(()=>a("div",null,"\u65E0\u7F16\u8F91\u524D\u6570\u636E",-1)),dt={class:"card-header"},pt=d(()=>a("span",null,"\u7F16\u8F91\u540E\u6570\u636E",-1)),mt={key:0},ft={class:"data-content"},ht={key:1,class:"text-gray-500 text-center py-8"},Ft=d(()=>a("div",null,"\u65E0\u7F16\u8F91\u540E\u6570\u636E",-1)),vt={key:0,class:"mt-6"},yt=d(()=>a("h3",{class:"text-lg font-medium mb-4"},"\u667A\u80FD\u4F53\u7B80\u4ECB",-1)),Et={class:"text-gray-700 leading-relaxed"},xt=j({__name:"edit-detail-popup",emits:["close"],setup(p,{expose:k,emit:gt}){const E=G(),h=C(!1),o=C({}),x=u=>{if(!u)return"";try{if(typeof u=="string"){const r=JSON.parse(u);return JSON.stringify(r,null,2)}return JSON.stringify(u,null,2)}catch{return String(u)}},w=async u=>{try{h.value=!0,console.log("\u{1F50D} [\u7F16\u8F91\u8BE6\u60C5\u5F39\u7A97] \u83B7\u53D6\u8BE6\u60C5\uFF0CID:",u);const r=await $({id:u});o.value=r,console.log("\u{1F4CA} [\u7F16\u8F91\u8BE6\u60C5\u5F39\u7A97] \u8BE6\u60C5\u6570\u636E:",o.value)}catch(r){console.error("\u274C [\u7F16\u8F91\u8BE6\u60C5\u5F39\u7A97] \u83B7\u53D6\u8BE6\u60C5\u5931\u8D25:",r),M.error("\u83B7\u53D6\u8BE6\u60C5\u5931\u8D25")}finally{h.value=!1}};return k({open:u=>{var r;o.value={},(r=E.value)==null||r.open(),w(u)}}),(u,r)=>{const n=N,m=J,g=O,f=R,A=P,F=T,b=V,I=z,S=L;return i(),v(U,{ref_key:"popupRef",ref:E,title:"\u7F16\u8F91\u8BE6\u60C5",width:"1000px","show-footer":!1,onClose:r[0]||(r[0]=Bt=>u.$emit("close"))},{default:e(()=>[H((i(),_("div",null,[s(A,{column:2,border:""},{default:e(()=>[s(n,{label:"\u65E5\u5FD7ID"},{default:e(()=>[c(l(t(o).id),1)]),_:1}),s(n,{label:"\u7F16\u8F91\u65F6\u95F4"},{default:e(()=>[c(l(t(o).create_time_text),1)]),_:1}),s(n,{label:"\u667A\u80FD\u4F53"},{default:e(()=>[a("div",K,[t(o).robot_image?(i(),v(g,{key:0,src:t(o).robot_image,size:32,class:"mr-2"},{default:e(()=>[s(m,null,{default:e(()=>[s(t(B))]),_:1})]),_:1},8,["src"])):y("",!0),a("div",null,[a("div",X,l(t(o).robot_name||"\u5DF2\u5220\u9664"),1),a("div",Y,"ID: "+l(t(o).robot_id),1)])])]),_:1}),s(n,{label:"\u64CD\u4F5C\u7528\u6237"},{default:e(()=>[a("div",Z,[t(o).user_avatar?(i(),v(g,{key:0,src:t(o).user_avatar,size:32,class:"mr-2"},{default:e(()=>[s(m,null,{default:e(()=>[s(t(B))]),_:1})]),_:1},8,["src"])):y("",!0),a("div",null,[a("div",tt,l(t(o).user_nickname||t(o).user_account),1),a("div",et,l(t(o).user_account),1)])])]),_:1}),s(n,{label:"\u7F16\u8F91\u7C7B\u578B"},{default:e(()=>[s(f,{type:t(o).edit_type===1?"primary":"success"},{default:e(()=>[c(l(t(o).edit_type_text),1)]),_:1},8,["type"])]),_:1}),s(n,{label:"\u662F\u5426\u81EA\u52A8\u4E0B\u67B6"},{default:e(()=>[s(f,{type:t(o).is_auto_offline?"danger":"success"},{default:e(()=>[c(l(t(o).is_auto_offline_text),1)]),_:1},8,["type"])]),_:1}),s(n,{label:"\u4E0B\u67B6\u539F\u56E0",span:2},{default:e(()=>[t(o).offline_reason?(i(),_("span",ot,l(t(o).offline_reason),1)):(i(),_("span",st,"\u65E0"))]),_:1})]),_:1}),a("div",at,[ut,s(I,{gutter:20},{default:e(()=>[s(b,{span:12},{default:e(()=>[s(F,{shadow:"never",class:"h-full"},{header:e(()=>[a("div",rt,[lt,s(f,{type:"info",size:"small"},{default:e(()=>[c("Before")]),_:1})])]),default:e(()=>[t(o).before_data?(i(),_("div",it,[a("pre",_t,l(x(t(o).before_data)),1)])):(i(),_("div",nt,[s(m,{class:"text-2xl mb-2"},{default:e(()=>[s(t(D))]),_:1}),ct]))]),_:1})]),_:1}),s(b,{span:12},{default:e(()=>[s(F,{shadow:"never",class:"h-full"},{header:e(()=>[a("div",dt,[pt,s(f,{type:"success",size:"small"},{default:e(()=>[c("After")]),_:1})])]),default:e(()=>[t(o).after_data?(i(),_("div",mt,[a("pre",ft,l(x(t(o).after_data)),1)])):(i(),_("div",ht,[s(m,{class:"text-2xl mb-2"},{default:e(()=>[s(t(D))]),_:1}),Ft]))]),_:1})]),_:1})]),_:1})]),t(o).robot_intro?(i(),_("div",vt,[yt,s(F,{shadow:"never"},{default:e(()=>[a("p",Et,l(t(o).robot_intro),1)]),_:1})])):y("",!0)])),[[S,t(h)]])]),_:1},512)}}});const _e=q(xt,[["__scopeId","data-v-195628fb"]]);export{_e as default};
