import{b as r}from"./index.850efb0d.js";import{d,o as s,c as n,J as c,$ as p,W as _,T as i}from"./@vue.a11433a6.js";import{_ as m}from"./vue-drag-resize.527c6620.js";const u=d({props:{showClose:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:o}){return{handleClose:()=>{o("close")}}}});const f={class:"del-wrap"};function C(e,o,t,h,$,v){const a=r;return s(),n("div",f,[c(e.$slots,"default",{},void 0,!0),e.showClose?(s(),n("div",{key:0,class:"icon-close",onClick:o[0]||(o[0]=p((...l)=>e.handleClose&&e.handleClose(...l),["stop"]))},[_(a,{size:12,name:"el-icon-CloseBold"})])):i("",!0)])}const B=m(u,[["render",C],["__scopeId","data-v-2a98aa67"]]);export{B as _};
