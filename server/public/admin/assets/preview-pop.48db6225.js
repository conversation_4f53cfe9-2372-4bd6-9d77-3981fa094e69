import{b as I}from"./index.850efb0d.js";import{d as P,s as R,r as n,b as B,w as $,o as c,P as z,Q as D,a as o,u as s,V as _,$ as d,W as f,c as w,F as L,a7 as S,K as V}from"./@vue.a11433a6.js";import{P as W}from"./index.324d704f.js";import{_ as A}from"./vue-drag-resize.527c6620.js";import"./element-plus.5bcb7c8a.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const F={class:"bg-body rounded-lg p-4"},N={class:"relative preview-img",style:{width:"698px",height:"392px"}},T=["src"],E={class:"mt-3 relative"},K=["onClick"],M=["src"],Q=P({__name:"preview-pop",emits:["close"],setup(j,{expose:h,emit:g}){const x=g,m=R(),r=n({preview:[],title:""}),e=n(0),v=n(null),b=B(()=>r.value.preview[e.value]||{}),l=async t=>{const p=t*160;v.value.scrollTo({left:p+t*4-260,behavior:"smooth"})},k=t=>{e.value=t},y=t=>{t.preventDefault(),e.value<r.value.preview.length-1&&(e.value++,l(e.value))},C=t=>{t.preventDefault(),e.value>0&&(e.value--,l(e.value))};return $(e,()=>{l(e.value)}),h({open:async t=>{var i;(i=m.value)==null||i.open(),r.value=t}}),(t,i)=>{const p=I;return c(),z(W,{width:"780px",title:s(r).title,ref_key:"popRef",ref:m,onClose:i[0]||(i[0]=a=>x("close"))},{default:D(()=>[o("div",F,[o("div",N,[o("img",{class:"w-full",src:s(b),alt:""},null,8,T),o("span",null,_(s(e)+1)+"/"+_(s(r).preview.length)+"\u9875",1)]),o("div",E,[o("div",{class:"preview-left-btn left-[-14px]",onClick:d(C,["prevent","stop"])},[f(p,{name:"el-icon-ArrowLeft",size:"20"})]),o("div",{class:"preview-right-btn right-[-14px]",onClick:d(y,["prevent","stop"])},[f(p,{name:"el-icon-ArrowRight",size:"20"})]),o("div",{ref_key:"previewScrollRef",ref:v,class:"whitespace-nowrap overflow-hidden px-1 preview-scroll"},[(c(!0),w(L,null,S(s(r).preview,(a,u)=>(c(),w("div",{key:a.index,class:V(["preview-item",{active:u===s(e)}]),onClick:G=>k(u)},[o("img",{src:a,class:"w-full"},null,8,M)],10,K))),128))],512)])])]),_:1},8,["title"])}}});const ze=A(Q,[["__scopeId","data-v-ebbe3651"]]);export{ze as default};
