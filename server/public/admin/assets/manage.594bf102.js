import{L as M,I as q,J as z,D as J,w as K,F as O,G as Q,K as W,b as H,t as X,M as Y}from"./element-plus.5bcb7c8a.js";import{_ as Z}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{u as ee}from"./usePaging.b48cb079.js";import{g as te,_ as oe,c as ae,d as le,b as ie}from"./addPop.vue_vue_type_script_setup_true_lang.65efe0af.js";import{f as y}from"./index.850efb0d.js";import{d as ne,r as b,s as V,i as se,aj as ue,o as n,c as re,W as e,Q as t,u as i,U as r,R as m,P as p,a as me,j as pe,T as de,n as B}from"./@vue.a11433a6.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.324d704f.js";import"./vue-drag-resize.527c6620.js";import"./picker.9a1dad65.js";import"./index.4de0c800.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const ce={class:"flex justify-end mt-4"},dt=ne({__name:"manage",setup(_e){const v=b({name:"",status:""}),d=b(!1),C=V(),w=V(),D=async()=>{d.value=!0,await B(),C.value.open()},x=async a=>{d.value=!0,await B(),C.value.open(a)},S=async a=>{await ae({id:a})},R=async a=>{await y.confirm("\u662F\u5426\u786E\u8BA4\u5220\u9664\uFF01"),await le({id:a}),_()},P=async()=>{var l;const a=(l=w.value)==null?void 0:l.getSelectionRows().map(g=>g.id);await y.confirm("\u662F\u5426\u786E\u5B9A\u6279\u91CF\u5220\u9664\uFF01"),await ie({id:a}),_()},h=b(!1),L=a=>{console.log(a),h.value=a.length!=0},{pager:c,getLists:_,resetPage:$,resetParams:T}=ee({fetchFun:te,params:v.value});return se(()=>{_()}),(a,l)=>{const g=q,U=z,E=J,s=K,G=O,k=Q,u=W,N=H,j=X,A=Z,f=ue("perms"),I=Y;return n(),re("div",null,[e(k,{class:"!border-none",shadow:"never"},{default:t(()=>[e(G,{ref:"formRef",class:"mb-[-16px]",model:v.value,inline:!0},{default:t(()=>[e(E,{label:"\u524D\u666F\u72B6\u6001"},{default:t(()=>[e(U,{class:"w-[280px]",modelValue:v.value.status,"onUpdate:modelValue":l[0]||(l[0]=o=>v.value.status=o)},{default:t(()=>[e(g,{label:"\u5F00\u542F",value:"1"}),e(g,{label:"\u5173\u95ED",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(E,null,{default:t(()=>[e(s,{type:"primary",onClick:i($)},{default:t(()=>[r("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(s,{onClick:i(T)},{default:t(()=>[r("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[m((n(),p(s,{type:"primary",onClick:D},{default:t(()=>[r("+ \u65B0\u589E\u524D\u666F")]),_:1})),[[f,["digital.preposition/add"]]]),m((n(),p(s,{onClick:P,disabled:!h.value},{default:t(()=>[r("\u6279\u91CF\u5220\u9664")]),_:1},8,["disabled"])),[[f,["digital.preposition/batchDel"]]]),m((n(),p(i(M),{class:"mt-2",size:"large",data:i(c).lists,ref_key:"tableRef",ref:w,onSelectionChange:L},{default:t(()=>[e(u,{type:"selection",width:"50"}),e(u,{label:"\u524D\u666F\u56FE","min-width":"120"},{default:t(({row:o})=>[e(N,{"preview-teleported":!0,"preview-src-list":[o.url],class:"w-[80px]",src:o.url},null,8,["preview-src-list","src"])]),_:1}),e(u,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:o})=>[m(e(j,{modelValue:o.status,"onUpdate:modelValue":F=>o.status=F,"active-value":1,"inactive-value":0,onChange:F=>S(o.id)},null,8,["modelValue","onUpdate:modelValue","onChange"]),[[f,["digital.preposition/status"]]])]),_:1}),e(u,{label:"\u6392\u5E8F",prop:"sort","min-width":"180"}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"180"}),e(u,{label:"\u64CD\u4F5C",width:"120",fixed:"right"},{default:t(({row:o})=>[m((n(),p(s,{type:"primary",link:"",onClick:F=>x(o)},{default:t(()=>[r(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[f,["digital.preposition/edit"]]]),m((n(),p(s,{type:"danger",onClick:F=>R(o.id),link:""},{default:t(()=>[r(" \u5220\u9664")]),_:2},1032,["onClick"])),[[f,["digital.preposition/del"]]])]),_:1})]),_:1},8,["data"])),[[I,i(c).loading]]),me("div",ce,[e(A,{modelValue:i(c),"onUpdate:modelValue":l[1]||(l[1]=o=>pe(c)?c.value=o:null),onChange:i(_)},null,8,["modelValue","onChange"])])]),_:1}),d.value?(n(),p(oe,{key:0,ref_key:"addPopRef",ref:C,onSuccess:l[2]||(l[2]=()=>{d.value=!1,i(_)()}),onClose:l[3]||(l[3]=o=>d.value=!1)},null,512)):de("",!0)])}}});export{dt as default};
