import{_ as y}from"./index.88e852a7.js";import{a3 as h,Q as k,R as x,G as R,D as N,C as U,w as H,F as G}from"./element-plus.5bcb7c8a.js";import{g as I,s as Q}from"./h5.6b2e3316.js";import{d as B,a0 as S,aj as F,o as p,c as T,W as e,Q as t,u,a as i,U as l,P as m,T as j,V as q,R as g}from"./@vue.a11433a6.js";import"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const P=i("div",{class:"form-tips"},"\u72B6\u6001\u4E3A\u5173\u95ED\u65F6\uFF0C\u5C06\u4E0D\u5BF9\u5916\u63D0\u4F9B\u670D\u52A1\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C",-1),W={class:"w-80"},z={class:"flex-1 min-w-0 break-words"},J=B({name:"h5Config"}),Rt=B({...J,setup(K){const o=S({status:0,page_status:0,page_url:"",url:""}),_=async()=>{const d=await I();for(const a in o)o[a]=d[a]},C=async()=>{await Q(o),_()};return _(),(d,a)=>{const D=h,c=R,s=k,f=x,n=N,b=U,E=H,v=G,w=y,V=F("copy"),A=F("perms");return p(),T("div",null,[e(c,{class:"!border-none",shadow:"never"},{default:t(()=>[e(D,{type:"warning",title:"\u6E29\u99A8\u63D0\u793A\uFF1AH5\u8BBE\u7F6E",closable:!1,"show-icon":""})]),_:1}),e(c,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[e(v,{ref:"formRef",model:u(o),"label-width":"120px"},{default:t(()=>[e(n,{label:"\u6E20\u9053\u72B6\u6001",required:"",prop:"status"},{default:t(()=>[i("div",null,[e(f,{modelValue:u(o).status,"onUpdate:modelValue":a[0]||(a[0]=r=>u(o).status=r)},{default:t(()=>[e(s,{label:1},{default:t(()=>[l("\u5F00\u542F")]),_:1}),e(s,{label:0},{default:t(()=>[l("\u5173\u95ED")]),_:1})]),_:1},8,["modelValue"]),P])]),_:1}),e(n,{label:"\u5173\u95ED\u540E\u8BBF\u95EE\u9875\u9762",prop:"page_status"},{default:t(()=>[e(f,{modelValue:u(o).page_status,"onUpdate:modelValue":a[1]||(a[1]=r=>u(o).page_status=r)},{default:t(()=>[e(s,{label:0},{default:t(()=>[l("\u7A7A\u9875\u9762")]),_:1}),e(s,{label:1},{default:t(()=>[l("\u81EA\u5B9A\u4E49\u94FE\u63A5")]),_:1})]),_:1},8,["modelValue"])]),_:1}),u(o).page_status==1?(p(),m(n,{key:0,label:"",prop:"page_url"},{default:t(()=>[i("div",W,[e(b,{modelValue:u(o).page_url,"onUpdate:modelValue":a[2]||(a[2]=r=>u(o).page_url=r),placeholder:"\u8BF7\u8F93\u5165\u5B8C\u6574\u7684url"},null,8,["modelValue"])])]),_:1})):j("",!0),e(n,{label:"\u8BBF\u95EE\u94FE\u63A5"},{default:t(()=>[i("div",z,[l(q(u(o).url)+" ",1),g((p(),m(E,null,{default:t(()=>[l("\u590D\u5236")]),_:1})),[[V,u(o).url]])])]),_:1})]),_:1},8,["model"])]),_:1}),g((p(),m(w,null,{default:t(()=>[e(E,{type:"primary",onClick:C},{default:t(()=>[l("\u4FDD\u5B58")]),_:1})]),_:1})),[[A,["channel.web_page_setting/setConfig"]]])])}}});export{Rt as default};
