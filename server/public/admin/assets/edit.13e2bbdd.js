import{a as e,u as a}from"./vue-router.919c7bec.js";import{g as s}from"./application.8542bcdb.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang.9a237709.js";import{d as r,a0 as n,o as u,c,W as d,u as _,j as f}from"./@vue.a11433a6.js";import"./index.850efb0d.js";import"./element-plus.5bcb7c8a.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./base-config.vue_vue_type_script_setup_true_lang.60f4f266.js";import"./picker.9a1dad65.js";import"./index.324d704f.js";import"./index.4de0c800.js";import"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import"./index.4a09b22e.js";import"./index.vue_vue_type_script_setup_true_lang.81711af0.js";import"./index.vue_vue_type_script_setup_true_lang.29b608a0.js";import"./usePaging.b48cb079.js";import"./preview.vue_vue_type_script_setup_true_lang.0e40873c.js";import"./file.vue_vue_type_style_index_0_scoped_ba7daef9_lang.afeb3582.js";import"./vue3-video-play.632beab5.js";import"./vuedraggable.2019ddfd.js";import"./vue.35cf47e9.js";import"./sortablejs.98edf555.js";import"./robot_square.168fdb05.js";import"./useDictOptions.583d6eb9.js";import"./search-config.vue_vue_type_script_setup_true_lang.4cd21630.js";import"./interface-config.vue_vue_type_script_setup_true_lang.3480a434.js";import"./add-menu.vue_vue_type_script_setup_true_lang.9955036d.js";import"./digital-config.vue_vue_type_script_setup_true_lang.2a4004a5.js";const g=r({name:"knowledgeBaseApplicationEdit"}),Vt=r({...g,setup(y){e();const m=a(),t=n({kb_ids:[],category_id:"",image:"",name:"",intro:"",systemPrompt:"",limitPrompt:"",searchEmptyText:"",searchSimilarity:.81,searchLimit:5,sort:0,is_enable:1,null_reply_type:1,is_show_context:1,is_show_quote:1});return(async()=>{const o=await s({id:m.query.id});Object.assign(t,o)})(),(o,i)=>(u(),c("div",null,[d(l,{modelValue:_(t),"onUpdate:modelValue":i[0]||(i[0]=p=>f(t)?t.value=p:null),"header-title":o.$route.meta.title||"\u67E5\u770B\u667A\u80FD\u4F53"},null,8,["modelValue","header-title"])]))}});export{Vt as default};
