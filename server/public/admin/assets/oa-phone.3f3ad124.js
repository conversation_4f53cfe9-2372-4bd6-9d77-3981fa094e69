import{c as w}from"./element-plus.5bcb7c8a.js";import{d as V,bM as F,u as e,b as P,a4 as d,o,c as s,a as n,W as r,Q as f,F as h,a7 as v,K as B,V as x,R as D,X as I,T as M,bk as N,bj as A}from"./@vue.a11433a6.js";import{u as b}from"./useMenuOa.88866e3a.js";import{j as E}from"./index.850efb0d.js";import{_ as G}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./wx_oa.1673a390.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const L=i=>(N("data-v-44f845ef"),i=i(),A(),i),O={class:"oa-phone mr-[35px]"},z=L(()=>n("div",{class:"oa-phone-content"},null,-1)),K={class:"flex oa-phone-menu"},Q={class:"flex items-center justify-center oa-phone-menu-switch"},R=["onClick"],T={class:"oa-phone-menu-subitem"},W=V({__name:"oa-phone",setup(i){F(y=>({"18c72984":e(k)}));const C=E(),k=P(()=>C.theme||"#4A5DFF"),{menuList:m,menuIndex:p,handleAddMenu:c}=b(b);return(y,l)=>{const S=d("Grid"),u=w,g=d("Plus");return o(),s("div",O,[z,n("div",K,[n("div",Q,[r(u,null,{default:f(()=>[r(S)]),_:1})]),(o(!0),s(h,null,v(e(m),(t,a)=>(o(),s("div",{key:a,class:"relative flex-1",onClick:_=>p.value=a},[n("div",{class:B(["flex items-center justify-center flex-1 text-sm oa-phone-menu-item",{"active-menu":e(p)===a}])},x(t.name),3),D(n("div",T,[(o(!0),s(h,null,v(t.sub_button,(_,j)=>(o(),s("div",{key:j,class:"oa-phone-menu-subitem-title truncate"},x(_.name),1))),128))],512),[[I,t.sub_button.length&&t.has_menu]])],8,R))),128)),e(m).length<=2?(o(),s("div",{key:0,class:"flex items-center justify-center flex-1 h-full",onClick:l[0]||(l[0]=(...t)=>e(c)&&e(c)(...t))},[r(u,null,{default:f(()=>[r(g)]),_:1})])):M("",!0)])])}}});const Bt=G(W,[["__scopeId","data-v-44f845ef"]]);export{Bt as default};
