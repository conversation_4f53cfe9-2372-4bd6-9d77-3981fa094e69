import{Q as h,R as k,C as q,D as I,t as S,F as U}from"./element-plus.5bcb7c8a.js";import{P as N}from"./index.324d704f.js";import{c as P,d as Q}from"./ai_square.bf3e05a5.js";import{d as T,s as _,r as G,b as W,a0 as j,w as z,o as H,c as J,W as u,Q as a,u as l,U as m,a as C}from"./@vue.a11433a6.js";const K={class:"edit-popup"},L={class:"flex-1"},M=C("div",{class:"form-tips"},"\u9ED8\u8BA4\u4E3A0\uFF0C\u6570\u503C\u8D8A\u5927\u6392\u8D8A\u524D\u9762",-1),$=T({__name:"edit",props:{type:{type:[Number,String],default:1}},emits:["success","close"],setup(F,{expose:y,emit:B}){const i=B,v=F,c=_(),r=_(),d=G("add"),E=W(()=>d.value=="add"?"\u65B0\u589E\u5206\u7C7B":"\u7F16\u8F91\u5206\u7C7B"),t=j({name:"",type:"",sort:0,status:1,id:""}),V={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:["blur"]}],type:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u5E94\u7528",trigger:["blur"]}]},b=async()=>{var o,e;await((o=c.value)==null?void 0:o.validate()),d.value=="edit"?await P(t):await Q(t),(e=r.value)==null||e.close(),i("success")},D=(o="add")=>{var e;d.value=o,(e=r.value)==null||e.open()},g=async o=>{for(const e in t)o[e]!=null&&o[e]!=null&&(t[e]=o[e])},w=()=>{i("close")};return z(()=>v.type,o=>{t.type=o*1},{immediate:!0}),y({open:D,setFormData:g}),(o,e)=>{const f=q,n=I,p=h,x=k,R=S,A=U;return H(),J("div",K,[u(N,{ref_key:"popupRef",ref:r,title:l(E),async:!0,width:"550px",onConfirm:b,onClose:w},{default:a(()=>[u(A,{ref_key:"formRef",ref:c,model:l(t),"label-width":"84px",rules:V},{default:a(()=>[u(n,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name"},{default:a(()=>[u(f,{placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",modelValue:l(t).name,"onUpdate:modelValue":e[0]||(e[0]=s=>l(t).name=s)},null,8,["modelValue"])]),_:1}),u(n,{label:"\u5206\u7C7B\u7C7B\u578B",prop:"type"},{default:a(()=>[u(x,{modelValue:l(t).type,"onUpdate:modelValue":e[1]||(e[1]=s=>l(t).type=s)},{default:a(()=>[u(p,{label:1},{default:a(()=>[m("AI\u7ED8\u753B")]),_:1}),u(p,{label:2},{default:a(()=>[m("AI\u97F3\u4E50")]),_:1}),u(p,{label:3},{default:a(()=>[m("AI\u89C6\u9891")]),_:1})]),_:1},8,["modelValue"])]),_:1}),u(n,{label:"\u6392\u5E8F"},{default:a(()=>[C("div",L,[u(f,{class:"ls-input",modelValue:l(t).sort,"onUpdate:modelValue":e[2]||(e[2]=s=>l(t).sort=s)},null,8,["modelValue"]),M])]),_:1}),u(n,{label:"\u72B6\u6001",required:""},{default:a(()=>[u(R,{modelValue:l(t).status,"onUpdate:modelValue":e[3]||(e[3]=s=>l(t).status=s),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{$ as _};
