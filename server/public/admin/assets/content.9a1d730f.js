import{E as w}from"./element-plus.5bcb7c8a.js";import v from"./decoration-img.16e6b284.js";import{u as b}from"./useMenu.4471f007.js";import{d as I,o,c as e,W as f,Q as l,a as s,K as m,u as a,F as p,a7 as h,P as N,T as c,V as x,bk as g,bj as C}from"./@vue.a11433a6.js";import{_ as S}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./decoration-img.vue_vue_type_style_index_0_scoped_6d06952c_lang.688ce113.js";import"./index.850efb0d.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const y=r=>(g("data-v-a668f12e"),r=r(),C(),r),B={class:"pc-aside absolute top-0 bottom-0 left-0 w-[80px]"},E={class:"h-full flex flex-col"},V={class:"mb-auto"},A=["to"],D=y(()=>s("span",null,null,-1)),F={class:"text-sm line-clamp-1 mt-[6px]"},M={key:0,class:"nav-item"},j=y(()=>s("span",null,null,-1)),z={key:0,class:"text-sm line-clamp-1 mt-[6px]"},K=I({__name:"content",props:{showNavIcon:{type:Boolean},nav:{type:Array,default:()=>[]},menu:{type:Array,default:()=>[]}},setup(r){const{selectActive:u,selectNav:_,selectMenu:d}=b();return(L,i)=>{const k=w;return o(),e("div",B,[f(k,null,{default:l(()=>[s("div",E,[s("div",V,[s("div",{class:m(["nav",[a(u)==="nav"?"nav_active":"nav_unactive"]]),onClick:i[0]||(i[0]=(...t)=>a(_)&&a(_)(...t))},[(o(!0),e(p,null,h(r.nav,(t,n)=>(o(),e(p,{key:n},[t.is_show=="1"?(o(),e("div",{key:0,to:t.link.path,class:m(["nav-item",{active:n==0}])},[r.showNavIcon?(o(),N(v,{key:0,width:"22px",height:"22px",src:n==0?t.selected:t.unselected,fit:"cover"},{error:l(()=>[D]),_:2},1032,["src"])):c("",!0),s("span",F,x(t.name),1)],10,A)):c("",!0)],64))),128))],2)]),s("div",null,[s("div",{class:m(["nav",[a(u)==="menu"?"nav_active":"nav_unactive"]]),onClick:i[1]||(i[1]=(...t)=>a(d)&&a(d)(...t))},[(o(!0),e(p,null,h(r.menu,(t,n)=>(o(),e(p,{key:n},[t.is_show=="1"?(o(),e("div",M,[f(v,{width:"22px",height:"22px",src:t.unselected,fit:"cover"},{error:l(()=>[j]),_:2},1032,["src"]),t.showName?(o(),e("span",z,x(t.name),1)):c("",!0)])):c("",!0)],64))),128))],2)])])]),_:1})])}}});const Nt=S(K,[["__scopeId","data-v-a668f12e"]]);export{Nt as default};
