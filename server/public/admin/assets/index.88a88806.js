import{C as j,D as q,I as O,J as z,w as G,F as J,G as Q,K as W,b as H,t as X,L as Y,M as Z}from"./element-plus.5bcb7c8a.js";import{_ as ee}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{g as V,f as te,b as ae}from"./index.850efb0d.js";import{u as oe}from"./vue-router.919c7bec.js";import{d as x,a0 as le,r as ue,a4 as ne,aj as ie,o as s,c as w,W as e,Q as a,u as o,a8 as se,F as re,a7 as me,P as d,U as m,a as k,R as g,V as pe,T as de,j as ce}from"./@vue.a11433a6.js";import{u as _e}from"./usePaging.b48cb079.js";import{d as fe,c as ge,f as be}from"./draw_model.55c9f259.js";import{g as Ce}from"./draw_model_category.252a0f65.js";import{u as ve}from"./useDictOptions.583d6eb9.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";const ye={class:"flex gap-2"},he={class:"flex justify-end mt-4"},Fe=x({name:"problemExample"}),pt=x({...Fe,setup(Be){const D=oe(),n=le({title:"",status:"",category_id:""}),b=ue([]);n.category_id=parseInt(D.query.category_id)||"";const y=async i=>{await te.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await fe({id:i}),C()},A=i=>{b.value=i},L=i=>{ge({id:i})},{pager:c,getLists:C,resetPage:h,resetParams:S}=_e({fetchFun:be,params:n}),{optionsData:P}=ve({categoryList:{api:Ce,transformData(i){return i}}});return C(),(i,l)=>{const U=j,_=q,f=O,F=z,p=G,I=J,B=Q,M=ae,E=ne("router-link"),u=W,R=H,$=X,K=Y,N=ee,v=ie("perms"),T=Z;return s(),w("div",null,[e(B,{class:"!border-none",shadow:"never"},{default:a(()=>[e(I,{ref:"formRef",class:"mb-[-16px]",model:o(n),inline:!0},{default:a(()=>[e(_,{class:"w-[280px]",label:"\u6A21\u578B\u540D\u79F0"},{default:a(()=>[e(U,{modelValue:o(n).title,"onUpdate:modelValue":l[0]||(l[0]=t=>o(n).title=t),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u540D\u79F0",clearable:"",onKeyup:se(o(h),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(_,{class:"w-[280px]",label:"\u6A21\u578B\u72B6\u6001"},{default:a(()=>[e(F,{modelValue:o(n).status,"onUpdate:modelValue":l[1]||(l[1]=t=>o(n).status=t)},{default:a(()=>[e(f,{label:"\u5168\u90E8",value:""}),e(f,{label:"\u5F00\u542F",value:1}),e(f,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(_,{class:"w-[280px]",label:"\u6A21\u578B\u5206\u7C7B"},{default:a(()=>[e(F,{modelValue:o(n).category_id,"onUpdate:modelValue":l[2]||(l[2]=t=>o(n).category_id=t),placeholder:"\u8BF7\u9009\u62E9\u6240\u5C5E\u5206\u7C7B",class:"w-full",clearable:""},{default:a(()=>[(s(!0),w(re,null,me(o(P).categoryList,(t,r)=>(s(),d(f,{key:r,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,null,{default:a(()=>[e(p,{type:"primary",onClick:o(h)},{default:a(()=>[m("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(p,{onClick:o(S)},{default:a(()=>[m("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(B,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[k("div",ye,[g((s(),d(E,{to:o(V)("application.sd.model/edit")},{default:a(()=>[e(p,{type:"primary"},{icon:a(()=>[e(M,{name:"el-icon-Plus"})]),default:a(()=>[m(" \u65B0\u589E ")]),_:1})]),_:1},8,["to"])),[[v,["application.sd.model/edit"]]]),e(p,{type:"default",plain:!0,disabled:!o(b).length,onClick:l[3]||(l[3]=t=>y(o(b).map(r=>r.id)))},{default:a(()=>[m(" \u6279\u91CF\u5220\u9664 ")]),_:1},8,["disabled"])]),g((s(),d(K,{size:"large",class:"mt-4",data:o(c).lists,onSelectionChange:A},{default:a(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"\u6A21\u578B\u5C01\u9762","min-width":"100"},{default:a(({row:t})=>[m(pe(t.cover?"":"\u6682\u65E0")+" ",1),t.cover?(s(),d(R,{key:0,src:t.cover,class:"w-[44px] h-[44px]"},null,8,["src"])):de("",!0)]),_:1}),e(u,{label:"\u6A21\u578B\u540D\u79F0",prop:"title","min-width":"160"}),e(u,{label:"\u6A21\u578B\u6807\u8BC6",prop:"model_name","min-width":"160"}),e(u,{label:"\u6A21\u578B\u5206\u7C7B",prop:"category_name","min-width":"120"}),e(u,{label:"\u5173\u8054\u5FAE\u8C03\u6A21\u578B",prop:"lora_total","min-width":"100"}),e(u,{label:"\u72B6\u6001","min-width":"100"},{default:a(({row:t})=>[e($,{onChange:r=>L(t.id),modelValue:t.status,"onUpdate:modelValue":r=>t.status=r,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(u,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:a(({row:t})=>[e(p,{type:"primary",link:""},{default:a(()=>[g((s(),d(E,{to:{path:o(V)("application.sd.model/edit"),query:{id:t.id}}},{default:a(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["to"])),[[v,["application.sd.model/edit"]]])]),_:2},1024),g((s(),d(p,{type:"danger",link:"",onClick:r=>y(t.id)},{default:a(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[v,["application.sd.model/del"]]])]),_:1})]),_:1},8,["data"])),[[T,o(c).loading]]),k("div",he,[e(N,{modelValue:o(c),"onUpdate:modelValue":l[4]||(l[4]=t=>ce(c)?c.value=t:null),onChange:o(C)},null,8,["modelValue","onChange"])])]),_:1})])}}});export{pt as default};
