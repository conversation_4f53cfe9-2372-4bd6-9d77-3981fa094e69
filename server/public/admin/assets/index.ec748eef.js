import{_ as f}from"./index.88e852a7.js";import{t as D,D as g,C as V,w as y,F as k,G as b}from"./element-plus.5bcb7c8a.js";import{r as _}from"./index.850efb0d.js";import{d as x,r as m,a0 as h,i as w,o as I,c as U,W as u,Q as a,a as t,U as P,bk as S,bj as q}from"./@vue.a11433a6.js";import{_ as K}from"./vue-drag-resize.527c6620.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";function N(){return _.get({url:"/setting.contentCensor/detail"})}function j(p){return _.post({url:"/setting.contentCensor/save",params:p})}const n=p=>(S("data-v-bae0c037"),p=p(),q(),p),G=n(()=>t("div",{class:"text-lg font-medium"},"\u5185\u5BB9\u5BA1\u6838",-1)),M={class:"mt-4"},Q=n(()=>t("div",{class:"form-tips"}," \u9ED8\u8BA4\u5173\u95ED\uFF0C\u5F00\u542F\u540E\uFF0C\u95EE\u9898\u4F1A\u9884\u5148\u63D0\u4EA4\u81F3\u767E\u5EA6\uFF0C\u5BA1\u6838\u901A\u8FC7\u540E\u624D\u4F1A\u56DE\u7B54\u3002\u8BF7\u586B\u5199\u76F8\u5E94\u7684appid\u548Ckey ",-1)),R=n(()=>t("div",{class:"form-tips"}," \u9ED8\u8BA4\u5173\u95ED\uFF0C\u5F00\u542F\u540E\uFF0C\u540E\u53F0\u8BA1\u5212\u4EFB\u52A1\u626B\u63CF\u5185\u5BB9\u63D0\u4EA4\u81F3\u767E\u5EA6\uFF0C\u5BA1\u6838\u8FDD\u89C4\u4F1A\u9690\u85CF\u5185\u5BB9\uFF0C\u6709\u4E00\u5B9A\u65F6\u95F4\u5EF6\u8FDF\u3002\u8BF7\u586B\u5199\u76F8\u5E94\u7684appid\u548Ckey ",-1)),T=n(()=>t("div",{class:"form-tips"}," \u9ED8\u8BA4\u5173\u95ED\uFF0C\u5F00\u542F\u540E\uFF0C\u7ED8\u753B\u63D0\u793A\u8BCD\u4F1A\u63D0\u4EA4\u81F3\u767E\u5EA6\u5BA1\u6838\uFF0C\u5BA1\u6838\u8FDD\u89C4\u5C06\u65E0\u6CD5\u751F\u6210\u7ED8\u753B\u4EFB\u52A1\u3002 ",-1)),W=n(()=>t("div",{class:"form-tips"}," \u9ED8\u8BA4\u5173\u95ED\uFF0C\u5F00\u542F\u540E\uFF0C\u7ED8\u753B\u751F\u6210\u7684\u56FE\u7247\u5185\u5BB9\u4F1A\u63D0\u4EA4\u81F3\u767E\u5EA6\u5BA1\u6838\uFF0C\u5BA1\u6838\u8FDD\u89C4\u5C06\u4F1A\u9690\u85CF\u56FE\u7247\u5185\u5BB9\u3002 ",-1)),z=n(()=>t("div",{class:"form-tips"}," \u9ED8\u8BA4\u5173\u95ED\uFF0C\u5F00\u542F\u540E\uFF0C\u7528\u6237\u5934\u50CF\u53CA\u6635\u79F0\u5185\u5BB9\u4F1A\u63D0\u4EA4\u81F3\u767E\u5EA6\u5BA1\u6838\uFF0C\u5BA1\u6838\u8FDD\u89C4\u5C06\u4F1A\u66F4\u6362\u7528\u6237\u5934\u50CF\u6216\u6635\u79F0\u3002 ",-1)),H=n(()=>t("div",{class:"form-tips"}," \u9ED8\u8BA4\u5173\u95ED\uFF0C\u5F00\u542F\u540E\uFF0C\u7528\u6237\u4E0A\u4F20\u56FE\u7247\u6587\u4EF6\u4F1A\u63D0\u4EA4\u81F3\u767E\u5EA6\u5BA1\u6838\u3002 ",-1)),J={class:"form-tips flex items-center"},L=n(()=>t("div",{class:"align-text-bottom h-full"}," \u5982\u679C\u60A8\u5DF2\u5F00\u901A\uFF0C\u53EF\u76F4\u63A5\u586B\u5199\uFF1B\u5982\u679C\u672A\u5F00\u901A\uFF0C\u70B9\u51FB ",-1)),O=n(()=>t("a",{href:"https://ai.baidu.com/solution/censoring?track=cp:ainsem|pf:pc|pp:chanpin-neirongshenhe|pu:neirongshenhe-baidu|ci:|kw:10008266",target:"_blank",rel:"noopener noreferrer"}," \u524D\u5F80\u5F00\u901A ",-1)),X=x({__name:"index",setup(p){const c=m(),e=m({ask_open:0,is_open:0,prompt_open:0,image_open:0,user_open:0,upload_image_open:0,app_id:"",api_key:"",secret_key:""}),F=async()=>{e.value=await N()},v=h({app_id:[{required:!0,message:"\u8BF7\u8F93\u5165appid",trigger:"change"}],api_key:[{required:!0,message:"\u8BF7\u8F93\u5165api_key",trigger:"change"}],secret_key:[{required:!0,message:"\u8BF7\u8F93\u5165secret_key",trigger:"change"}]}),E=async()=>{await j(e.value),F()};return w(()=>{F()}),(Y,o)=>{const s=D,i=g,r=V,d=y,B=k,A=b,C=f;return I(),U("div",null,[u(A,{shadow:"never",class:"!border-none"},{default:a(()=>[G,t("div",M,[u(B,{"label-width":"120px",ref:c.value,model:e.value,rules:v},{default:a(()=>[u(i,{label:"\u95EE\u9898\u5BA1\u6838"},{default:a(()=>[t("div",null,[u(s,{modelValue:e.value.ask_open,"onUpdate:modelValue":o[0]||(o[0]=l=>e.value.ask_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"]),Q])]),_:1}),u(i,{label:"\u5185\u5BB9\u5BA1\u6838"},{default:a(()=>[t("div",null,[u(s,{modelValue:e.value.is_open,"onUpdate:modelValue":o[1]||(o[1]=l=>e.value.is_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"]),R])]),_:1}),u(i,{label:"\u63D0\u793A\u8BCD\u5BA1\u6838"},{default:a(()=>[t("div",null,[u(s,{modelValue:e.value.prompt_open,"onUpdate:modelValue":o[2]||(o[2]=l=>e.value.prompt_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"]),T])]),_:1}),u(i,{label:"\u56FE\u7247\u5BA1\u6838"},{default:a(()=>[t("div",null,[u(s,{modelValue:e.value.image_open,"onUpdate:modelValue":o[3]||(o[3]=l=>e.value.image_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"]),W])]),_:1}),u(i,{label:"\u7528\u6237\u4FE1\u606F\u5BA1\u6838"},{default:a(()=>[t("div",null,[u(s,{modelValue:e.value.user_open,"onUpdate:modelValue":o[4]||(o[4]=l=>e.value.user_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"]),z])]),_:1}),u(i,{label:"\u4E0A\u4F20\u56FE\u7247\u5BA1\u6838"},{default:a(()=>[t("div",null,[u(s,{modelValue:e.value.upload_image_open,"onUpdate:modelValue":o[5]||(o[5]=l=>e.value.upload_image_open=l),"active-value":1,"inactive-value":0},null,8,["modelValue"]),H])]),_:1}),u(i,{label:"APPID",prop:"app_id"},{default:a(()=>[t("div",null,[u(r,{modelValue:e.value.app_id,"onUpdate:modelValue":o[6]||(o[6]=l=>e.value.app_id=l),class:"w-[400px]",placeholder:"\u8BF7\u8F93\u5165APPID"},null,8,["modelValue"]),t("div",J,[L,u(d,{type:"primary",link:"",class:"ml-1"},{default:a(()=>[O]),_:1})])])]),_:1}),u(i,{label:"APIKey",prop:"api_key"},{default:a(()=>[u(r,{modelValue:e.value.api_key,"onUpdate:modelValue":o[7]||(o[7]=l=>e.value.api_key=l),class:"w-[400px]",placeholder:"\u8BF7\u8F93\u5165APIKey"},null,8,["modelValue"])]),_:1}),u(i,{label:"Secret Key",prop:"secret_key"},{default:a(()=>[u(r,{modelValue:e.value.secret_key,"onUpdate:modelValue":o[8]||(o[8]=l=>e.value.secret_key=l),class:"w-[400px]",placeholder:"\u8BF7\u8F93\u5165Secret Key"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1}),u(C,null,{default:a(()=>[u(d,{type:"primary",onClick:E},{default:a(()=>[P(" \u4FDD\u5B58")]),_:1})]),_:1})])}}});const qu=K(X,[["__scopeId","data-v-bae0c037"]]);export{qu as default};
