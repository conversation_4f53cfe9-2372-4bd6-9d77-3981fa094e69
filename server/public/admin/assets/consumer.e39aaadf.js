import{r as e}from"./index.850efb0d.js";function s(r){return e.get({url:"/user.user/lists",params:r},{ignoreCancelToken:!0})}function t(r){return e.get({url:"/user.user/detail",params:r})}function n(r){return e.post({url:"/user.user/edit",params:r})}function o(r){return e.post({url:"/user.user/adjustAccount",params:r})}function a(r){return e.post({url:"/user.user/adjustMember",params:r})}function i(r){return e.get({url:"/member.member_package/commonLists",params:r})}function d(r){return e.post({url:"/user.user/adjustAccount",params:r})}function l(r){return e.post({url:"/user.user/add",params:r})}function c(r){return e.post({url:"/user.user/blacklist",params:r})}function p(r){return e.post({url:"/user.user/rePassword",params:r})}function g(r){return e.get({url:"/user.user/buyLog",params:r})}function f(r){return e.post({url:"/user.user/cancelled",params:r})}function m(r){return e.post({url:"/market.regReward/save",params:r})}function b(){return e.get({url:"/market.regReward/detail"})}function j(r){return e.get({url:"/user.userGroup/lists",params:r})}function G(r){return e.post({url:"/user.userGroup/add",params:r})}function L(r){return e.post({url:"/user.userGroup/edit",params:r})}function k(r){return e.post({url:"/user.userGroup/del",params:r})}function R(r){return e.post({url:"/user.user/setGroup",params:r})}function w(r){return e.post({url:"/user.user/adjustKb",params:r})}function M(r){return e.post({url:"/user.user/adjustLeader",params:r})}export{l as a,R as b,M as c,a as d,g as e,L as f,i as g,G as h,k as i,t as j,n as k,d as l,o as m,w as n,c as o,f as p,s as q,p as r,b as s,m as t,j as u};
