import{r as i}from"./index.850efb0d.js";function n(t){return i.get({url:"/distribution.distributor/lists",params:t},{ignoreCancelToken:!0})}function u(t){return i.post({url:"/distribution.distributor/status",data:t})}function s(t){return i.post({url:"/distribution.distributor/add",data:t})}function o(t){return i.get({url:"/distribution.distributor/detail",params:t})}function e(t){return i.get({url:"/distribution.distributor/belowLists",params:t})}function d(t){return i.get({url:"/distribution.distribution_order/lists",params:t},{ignoreCancelToken:!0})}function a(t){return i.get({url:"/distribution.withdraw/lists",params:t},{ignoreCancelToken:!0})}function l(t){return i.post({url:"/distribution.withdraw/verify",data:t})}function b(t){return i.post({url:"/distribution.withdraw/transfer",data:t})}function g(t){return i.get({url:"/distribution.withdraw/detail",params:t})}function f(t){return i.get({url:"/distribution.distributionApply/lists",params:t},{ignoreCancelToken:!0})}function c(t){return i.get({url:"/distribution.distributionApply/detail",params:t})}function w(t){return i.post({url:"/distribution.distributionApply/audit",data:t})}function p(t){return i.get({url:"/distribution.withdraw/getConfig",params:t})}function h(t){return i.post({url:"/distribution.withdraw/setConfig",data:t})}function C(t){return i.get({url:"/distribution.config/getConfig",params:t})}function y(t){return i.post({url:"/distribution.config/setConfig",data:t})}export{w as a,c as b,f as c,s as d,u as e,n as f,o as g,e as h,d as i,C as j,h as k,g as l,a as m,b as n,y as s,l as v,p as w};
