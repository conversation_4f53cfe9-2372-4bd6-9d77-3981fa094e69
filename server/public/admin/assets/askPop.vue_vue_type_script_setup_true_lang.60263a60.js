import{P as r}from"./index.324d704f.js";import{_ as p}from"./index.850efb0d.js";import{d as m,s as u,o,c as f,a,V as i,W as d,Q as g,P as _,T as h,F as k}from"./@vue.a11433a6.js";const B=m({__name:"askPop",props:{content:{type:String,default:""},title:{type:String,default:"\u7528\u6237\u63D0\u95EE"},image:{type:String,default:""}},setup(e,{expose:c}){const t=u(),n=()=>{t.value.open()};return c({open:n}),(w,v)=>{const s=p,l=r;return o(),f(k,null,[a("div",{onClick:n,class:"line-clamp-2 cursor-pointer"},i(e.content),1),d(l,{ref_key:"popRef",ref:t,title:e.title,width:"700px"},{default:g(()=>[e.image?(o(),_(s,{key:0,class:"mb-2",src:e.image,width:200,height:200,"preview-src-list":[e.image],"preview-teleported":"",fit:"contain"},null,8,["src","preview-src-list"])):h("",!0),a("div",null,i(e.content),1)]),_:1},8,["title"])],64)}}});export{B as _};
