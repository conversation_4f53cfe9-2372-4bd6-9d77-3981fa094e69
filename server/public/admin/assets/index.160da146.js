import{C as j,D as I,I as q,J as z,w as G,F as J,G as M,K as O,t as Q,L as W,M as H}from"./element-plus.5bcb7c8a.js";import{_ as X}from"./index.vue_vue_type_script_setup_true_lang.37afb29e.js";import{f as h,b as Y}from"./index.850efb0d.js";import{d as V,s as Z,a0 as ee,r as te,aj as oe,o as s,c as ae,W as e,Q as t,u as a,a8 as le,U as p,a as k,R as F,P as c,j as ne,T as ue,n as w}from"./@vue.a11433a6.js";import{u as ie}from"./usePaging.b48cb079.js";import{_ as se}from"./edit.vue_vue_type_script_setup_true_lang.c0e159a9.js";import{d as re,b as me,g as pe}from"./ai_creation.a70c015c.js";import"./lodash-es.c9433054.js";import"./@vueuse.a2407f20.js";import"./@element-plus.1e23f767.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";import"./lodash.9ffd80b1.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./vue-drag-resize.527c6620.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./index.324d704f.js";const ce={class:"flex justify-end mt-4"},de=V({name:"problemExample"}),ot=V({...de,setup(_e){const d=Z(),u=ee({name:"",status:""}),_=te(!1),D=async()=>{var n;_.value=!0,await w(),(n=d.value)==null||n.open("add")},x=async n=>{var l,C;_.value=!0,await w(),(l=d.value)==null||l.open("edit"),(C=d.value)==null||C.setFormData(n)},$=async(n,l)=>{if(l!=0){h.msgError("\u5F53\u524D\u5206\u7C7B\u5DF2\u88AB\u4F7F\u7528\uFF0C\u8BF7\u89E3\u9664\u540E\u5220\u9664");return}await h.confirm("\u786E\u5B9A\u8981\u5220\u9664\uFF1F"),await re({id:n}),f()},P=n=>{me({id:n})},{pager:r,getLists:f,resetPage:B,resetParams:R}=ie({fetchFun:pe,params:u});return f(),(n,l)=>{const C=j,g=I,E=q,S=z,m=G,U=J,y=M,A=Y,i=O,T=Q,K=W,L=X,b=oe("perms"),N=H;return s(),ae("div",null,[e(y,{class:"!border-none",shadow:"never"},{default:t(()=>[e(U,{ref:"formRef",class:"mb-[-16px]",model:a(u),inline:!0},{default:t(()=>[e(g,{label:"\u7C7B\u522B\u540D\u79F0"},{default:t(()=>[e(C,{class:"w-[280px]",modelValue:a(u).name,"onUpdate:modelValue":l[0]||(l[0]=o=>a(u).name=o),placeholder:"\u8BF7\u8F93\u5165\u7C7B\u522B\u540D\u79F0",clearable:"",onKeyup:le(a(B),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(g,{label:"\u7C7B\u522B\u72B6\u6001"},{default:t(()=>[e(S,{class:"w-[280px]",modelValue:a(u).status,"onUpdate:modelValue":l[1]||(l[1]=o=>a(u).status=o)},{default:t(()=>[e(E,{label:"\u5168\u90E8",value:""}),e(E,{label:"\u5F00\u542F",value:1}),e(E,{label:"\u5173\u95ED",value:0})]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:t(()=>[e(m,{type:"primary",onClick:a(B)},{default:t(()=>[p("\u67E5\u8BE2")]),_:1},8,["onClick"]),e(m,{onClick:a(R)},{default:t(()=>[p("\u91CD\u7F6E")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(y,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[k("div",null,[F((s(),c(m,{type:"primary",onClick:D},{icon:t(()=>[e(A,{name:"el-icon-Plus"})]),default:t(()=>[p(" \u65B0\u589E ")]),_:1})),[[b,["creation.creationCategory/add"]]])]),F((s(),c(K,{size:"large",class:"mt-4",data:a(r).lists},{default:t(()=>[e(i,{label:"\u7C7B\u522B\u540D\u79F0",prop:"name","min-width":"120"}),e(i,{label:"\u6A21\u578B\u5173\u8054",prop:"model_count","min-width":"100"}),e(i,{label:"\u72B6\u6001","min-width":"100"},{default:t(({row:o})=>[e(T,{onChange:v=>P(o.id),modelValue:o.status,"onUpdate:modelValue":v=>o.status=v,"active-value":1,"inactive-value":0},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),e(i,{label:"\u6392\u5E8F",prop:"sort","min-width":"120"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"create_time","min-width":"100"}),e(i,{label:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:t(({row:o})=>[F((s(),c(m,{type:"primary",link:"",onClick:v=>x(o)},{default:t(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[b,["creation.creationCategory/edit"]]]),F((s(),c(m,{type:"danger",link:"",onClick:v=>$(o.id,o.model_count)},{default:t(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[b,["creation.creationCategory/del"]]])]),_:1})]),_:1},8,["data"])),[[N,a(r).loading]]),k("div",ce,[e(L,{modelValue:a(r),"onUpdate:modelValue":l[2]||(l[2]=o=>ne(r)?r.value=o:null),onChange:a(f)},null,8,["modelValue","onChange"])])]),_:1}),a(_)?(s(),c(se,{key:0,ref_key:"editRef",ref:d,onSuccess:a(f),onClose:l[3]||(l[3]=o=>_.value=!1)},null,8,["onSuccess"])):ue("",!0)])}}});export{ot as default};
