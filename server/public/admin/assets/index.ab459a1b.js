import{_ as N}from"./index.88e852a7.js";import{r as C,b as T}from"./index.850efb0d.js";import{Q as j,R as z,D as G,F as L,G as Q,w as $,C as q,K,L as P}from"./element-plus.5bcb7c8a.js";import{d as x,a0 as W,b as H,aj as J,o as u,c as h,W as t,Q as e,u as c,a,U as m,R as v,P as E,F as M,a7 as O,V as X,bk as Y,bj as Z}from"./@vue.a11433a6.js";import{_ as tt}from"./vue-drag-resize.527c6620.js";import"./@vueuse.a2407f20.js";import"./lodash.9ffd80b1.js";import"./@iktakahiro.479df58a.js";import"./katex.2ad1a25a.js";import"./axios.c3b81d20.js";import"./vue-router.919c7bec.js";import"./pinia.0d658f08.js";import"./vue-demi.b3a9cad9.js";import"./css-color-function.062f6923.js";import"./color.1cffd92e.js";import"./clone.105d8a55.js";import"./color-convert.755d189f.js";import"./color-name.e7a4e1d3.js";import"./color-string.e356f5de.js";import"./@element-plus.1e23f767.js";import"./nprogress.28f32054.js";import"./vue-clipboard3.ba321cef.js";import"./clipboard.591cd017.js";import"./echarts.8535e5a6.js";import"./zrender.3eba8991.js";import"./tslib.60310f1a.js";import"./highlight.js.4f6161a5.js";import"./@highlightjs.f8bb3178.js";import"./lodash-es.c9433054.js";import"./@popperjs.36402333.js";import"./@ctrl.b082b0c1.js";import"./dayjs.54121183.js";import"./async-validator.fb49d0f5.js";import"./memoize-one.4ee5c96d.js";import"./escape-html.e5dfadb9.js";import"./normalize-wheel-es.8aeb3683.js";import"./@floating-ui.cba15af0.js";function et(){return C.get({url:"/setting.hot_search/getConfig"})}function ot(r){return C.post({url:"/setting.hot_search/setConfig",params:r})}const d=r=>(Y("data-v-1f7b34ef"),r=r(),Z(),r),at={class:"hot-search"},st=d(()=>a("div",{class:"form-tips"},"\u9ED8\u8BA4\u5F00\u542F\uFF0C\u5173\u95ED\u5219\u524D\u7AEF\u4E0D\u663E\u793A\u8BE5\u529F\u80FD",-1)),nt={class:"lg:flex"},lt={class:"flex-1 min-w-0"},rt={class:"hot-search-phone mt-4 lg:mt-0 lg:ml-4 flex-none"},it=d(()=>a("div",{class:"mb-4 text-center"},"- \u70ED\u641C\u9884\u89C8\u56FE -",-1)),ut={class:"hot-search-phone-content"},ct={class:"search-com"},mt={class:"search-con flex items-center px-[15px]"},dt=d(()=>a("span",{class:"ml-[5px]"},"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD\u641C\u7D22",-1)),_t=d(()=>a("div",{class:"hot-search-title"},"\u70ED\u95E8\u641C\u7D22",-1)),pt={class:"hot-search-text"},ht=x({name:"search"}),ft=x({...ht,setup(r){const n=W({status:1,data:[]}),B=H(()=>n.data.filter(o=>o.name).sort((o,l)=>l.sort-o.sort)),f=async()=>{try{const o=await et();for(const l in n)n[l]=o[l]}catch(o){console.log("\u83B7\u53D6=>",o)}},y=()=>{n.data.push({name:"",sort:0})},V=o=>{n.data.splice(o,1)},w=async()=>{try{await ot(n),f()}catch(o){console.log("\u4FDD\u5B58=>",o)}};return f(),(o,l)=>{const b=j,k=z,S=G,I=L,g=Q,_=$,D=q,p=K,U=P,A=T,R=N,F=J("perms");return u(),h("div",at,[t(g,{class:"!border-none",shadow:"never"},{default:e(()=>[t(I,{ref:"formRef",model:c(n),"label-width":"100px"},{default:e(()=>[t(S,{label:"\u529F\u80FD\u72B6\u6001",style:{"margin-bottom":"0"}},{default:e(()=>[a("div",null,[t(k,{modelValue:c(n).status,"onUpdate:modelValue":l[0]||(l[0]=s=>c(n).status=s)},{default:e(()=>[t(b,{label:1},{default:e(()=>[m("\u5F00\u542F")]),_:1}),t(b,{label:0},{default:e(()=>[m("\u5173\u95ED")]),_:1})]),_:1},8,["modelValue"]),st])]),_:1})]),_:1},8,["model"])]),_:1}),t(g,{class:"!border-none mt-4",shadow:"never"},{default:e(()=>[a("div",nt,[a("div",lt,[t(_,{type:"primary",class:"mb-4",onClick:y},{default:e(()=>[m("\u6DFB\u52A0")]),_:1}),t(U,{size:"large",data:c(n).data},{default:e(()=>[t(p,{label:"\u5173\u952E\u8BCD",prop:"describe","min-width":"160"},{default:e(({row:s})=>[t(D,{modelValue:s.name,"onUpdate:modelValue":i=>s.name=i,clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57","show-word-limit":"",maxlength:"30"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(p,{label:"\u6392\u5E8F",prop:"describe","min-width":"160"},{default:e(({row:s})=>[t(D,{modelValue:s.sort,"onUpdate:modelValue":i=>s.sort=i,type:"number"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(p,{label:"\u64CD\u4F5C","min-width":"80",fixed:"right"},{default:e(({$index:s})=>[v((u(),E(_,{type:"danger",link:"",onClick:i=>V(s)},{default:e(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[F,["setting:storage:edit"]]])]),_:1})]),_:1},8,["data"])]),a("div",rt,[it,a("div",ut,[a("div",ct,[a("div",mt,[t(A,{name:"el-icon-Search",size:17}),dt])]),_t,a("div",pt,[(u(!0),h(M,null,O(c(B),(s,i)=>(u(),h("span",{key:i},X(s.name),1))),128))])])])])]),_:1}),v((u(),E(R,null,{default:e(()=>[t(_,{type:"primary",onClick:w},{default:e(()=>[m("\u4FDD\u5B58")]),_:1})]),_:1})),[[F,["setting.hot_search/setConfig"]]])])}}});const te=tt(ft,[["__scopeId","data-v-1f7b34ef"]]);export{te as default};
