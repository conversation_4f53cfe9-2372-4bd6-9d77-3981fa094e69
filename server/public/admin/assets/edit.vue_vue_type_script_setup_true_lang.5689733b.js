import{Q as R,R as U,I as h,J as A,D as S,C as I,v as L,F as N}from"./element-plus.5bcb7c8a.js";import{P as O}from"./index.324d704f.js";import{r as n,f as P}from"./index.850efb0d.js";import{d as j,s as g,r as Q,a0 as G,o as c,c as b,W as u,Q as o,F as J,a7 as T,P as W,U as F,a as z}from"./@vue.a11433a6.js";function te(a){return n.get({url:"/kb.example/lists",params:a})}function H(a){return n.get({url:"/kb.example/detail",params:a})}function K(a){return n.post({url:"/kb.example/add",params:a})}function M(a){return n.post({url:"/kb.example/edit",params:a})}function ue(a){return n.post({url:"/kb.example/del",params:a})}function le(a){return n.post({url:"/kb.example/status",params:a})}const X=z("div",{class:"form-tips"},"\u6570\u5B57\u8D8A\u5C0F\u8D8A\u9760\u524D",-1),ae=j({__name:"edit",props:{title:{type:String,default:""},categoryList:{type:Array,default:()=>[]}},emits:["success","close"],setup(a,{expose:E,emit:V}){const f=V,_=g(),i=g(),p=Q("add"),t=G({id:"",category_id:"",title:"",question:"",answer:"",sort:0,status:1}),x={category_id:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7C7B\u522B",trigger:["blur","change"]}],title:[{required:!0,message:"\u8BF7\u8F93\u5165\u793A\u4F8B\u6807\u9898",trigger:["blur","change"]}],question:[{required:!0,message:"\u8BF7\u8F93\u5165\u95EE\u9898\u5185\u5BB9",trigger:["blur","change"]}],answer:[{required:!0,message:"\u8BF7\u8F93\u5165\u7B54\u6848\u5185\u5BB9",trigger:["blur","change"]}],status:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",trigger:["blur","change"]}]},y=async(s={})=>{var e;if(s&&s.id!==void 0){p.value="edit";const d=await H({id:s.id});Object.assign(t,d)}else p.value="add",Object.assign(t,{id:"",category_id:"",title:"",question:"",answer:"",sort:0,status:1});(e=i.value)==null||e.open()},C=async()=>{var s,e;await((s=_.value)==null?void 0:s.validate());try{p.value==="edit"?await M(t):await K(t),(e=i.value)==null||e.close(),P.msgSuccess("\u64CD\u4F5C\u6210\u529F"),f("success")}catch(d){console.error(d)}},w=()=>{f("close")};return E({open:y}),(s,e)=>{const d=h,v=A,r=S,m=I,k=L,B=R,q=U,D=N;return c(),b("div",null,[u(O,{ref_key:"popupRef",ref:i,title:a.title,async:!0,width:"750px",onConfirm:C,onClose:w},{default:o(()=>[u(D,{ref_key:"formRef",ref:_,model:t,"label-width":"120px",rules:x,class:"pr-[30px]"},{default:o(()=>[u(r,{label:"\u6240\u5C5E\u7C7B\u522B",prop:"category_id"},{default:o(()=>[u(v,{modelValue:t.category_id,"onUpdate:modelValue":e[0]||(e[0]=l=>t.category_id=l),placeholder:"\u8BF7\u9009\u62E9\u7C7B\u522B",class:"w-[320px]"},{default:o(()=>[(c(!0),b(J,null,T(a.categoryList,l=>(c(),W(d,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(r,{label:"\u793A\u4F8B\u6807\u9898",prop:"title"},{default:o(()=>[u(m,{modelValue:t.title,"onUpdate:modelValue":e[1]||(e[1]=l=>t.title=l),placeholder:"\u8BF7\u8F93\u5165\u793A\u4F8B\u6807\u9898",clearable:"",class:"w-[550px]"},null,8,["modelValue"])]),_:1}),u(r,{label:"\u95EE\u9898\u5185\u5BB9",prop:"question"},{default:o(()=>[u(m,{modelValue:t.question,"onUpdate:modelValue":e[2]||(e[2]=l=>t.question=l),type:"textarea",rows:"5",placeholder:"\u8BF7\u8F93\u5165\u95EE\u9898\u5185\u5BB9",class:"w-[550px]"},null,8,["modelValue"])]),_:1}),u(r,{label:"\u7B54\u6848\u5185\u5BB9",prop:"answer"},{default:o(()=>[u(m,{modelValue:t.answer,"onUpdate:modelValue":e[3]||(e[3]=l=>t.answer=l),type:"textarea",rows:"8",placeholder:"\u8BF7\u8F93\u5165\u7B54\u6848\u5185\u5BB9",class:"w-[550px]"},null,8,["modelValue"])]),_:1}),u(r,{label:"\u6392\u5E8F",prop:"sort"},{default:o(()=>[u(k,{modelValue:t.sort,"onUpdate:modelValue":e[4]||(e[4]=l=>t.sort=l),min:0},null,8,["modelValue"]),X]),_:1}),u(r,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[u(q,{modelValue:t.status,"onUpdate:modelValue":e[5]||(e[5]=l=>t.status=l)},{default:o(()=>[u(B,{label:1},{default:o(()=>[F("\u5F00\u542F")]),_:1}),u(B,{label:0},{default:o(()=>[F("\u5173\u95ED")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{ae as _,ue as d,te as g,le as u};
