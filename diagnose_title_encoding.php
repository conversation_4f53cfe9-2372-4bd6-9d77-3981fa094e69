<?php
/**
 * 诊断知识库数据学习页面标题乱码问题
 */

echo "=== 知识库数据学习页面标题乱码问题诊断 ===\n\n";

echo "🔍 问题分析:\n";
echo str_repeat("-", 50) . "\n";
echo "1. 用户报告: 点击知识库'数据学习'按钮后，页面标题显示乱码\n";
echo "2. 预期结果: 页面标题应该显示正确的中文，如'数据学习'\n";
echo "3. 可能原因: 字符编码问题、页面标题设置缺失\n\n";

echo "🔍 检查1: HTML模板字符编码\n";
echo str_repeat("-", 50) . "\n";

$htmlFile = 'admin/index.html';
if (file_exists($htmlFile)) {
    $content = file_get_contents($htmlFile);
    
    // 检查charset设置
    if (preg_match('/<meta charset="([^"]+)"/', $content, $matches)) {
        echo "✅ HTML charset设置: {$matches[1]}\n";
        if (strtolower($matches[1]) === 'utf-8') {
            echo "✅ 字符编码设置正确\n";
        } else {
            echo "❌ 字符编码设置错误，应该是UTF-8\n";
        }
    } else {
        echo "❌ 未找到charset设置\n";
    }
    
    // 检查默认标题
    if (preg_match('/<title>([^<]+)<\/title>/', $content, $matches)) {
        echo "📋 默认标题: {$matches[1]}\n";
        
        // 检查标题是否包含中文
        if (preg_match('/[\x{4e00}-\x{9fff}]/u', $matches[1])) {
            echo "✅ 默认标题包含中文字符\n";
        } else {
            echo "⚠️ 默认标题不包含中文字符\n";
        }
    }
} else {
    echo "❌ HTML模板文件不存在\n";
}

echo "\n🔍 检查2: 数据学习页面组件\n";
echo str_repeat("-", 50) . "\n";

$studyDataFile = 'admin/src/views/knowledge_base/knowledge_base/study_data/index.vue';
if (file_exists($studyDataFile)) {
    $content = file_get_contents($studyDataFile);
    
    echo "✅ 数据学习页面文件存在\n";
    
    // 检查页面标题设置
    if (strpos($content, 'document.title') !== false) {
        echo "✅ 页面包含document.title设置\n";
    } else {
        echo "⚠️ 页面未设置document.title\n";
    }
    
    // 检查Vue Router meta设置
    if (strpos($content, 'meta:') !== false || strpos($content, 'definePageMeta') !== false) {
        echo "✅ 页面包含meta信息设置\n";
    } else {
        echo "❌ 页面缺少meta信息设置\n";
    }
    
    // 检查页面头部设置
    if (strpos($content, 'useHead') !== false || strpos($content, 'useSeoMeta') !== false) {
        echo "✅ 页面包含头部信息设置\n";
    } else {
        echo "❌ 页面缺少头部信息设置\n";
    }
    
    // 检查el-page-header组件
    if (strpos($content, 'el-page-header') !== false) {
        echo "✅ 页面使用了el-page-header组件\n";
        
        // 检查content属性
        if (preg_match('/:content="([^"]+)"/', $content, $matches)) {
            echo "📋 页面头部内容: {$matches[1]}\n";
            
            // 检查是否使用了路由参数
            if (strpos($matches[1], '$route.query.name') !== false) {
                echo "✅ 使用路由参数作为标题\n";
            }
        }
    } else {
        echo "⚠️ 页面未使用el-page-header组件\n";
    }
    
} else {
    echo "❌ 数据学习页面文件不存在\n";
}

echo "\n🔍 检查3: 路由跳转参数\n";
echo str_repeat("-", 50) . "\n";

$knowledgeListFile = 'admin/src/views/knowledge_base/knowledge_base/index.vue';
if (file_exists($knowledgeListFile)) {
    $content = file_get_contents($knowledgeListFile);
    
    echo "✅ 知识库列表页面文件存在\n";
    
    // 检查数据学习按钮的路由跳转
    if (preg_match('/path:\s*[\'"]([^\'\"]*study_data[^\'\"]*)[\'\"]/', $content, $matches)) {
        echo "📋 数据学习路由路径: {$matches[1]}\n";
    }
    
    // 检查query参数传递
    if (preg_match('/query:\s*\{([^}]+)\}/', $content, $matches)) {
        echo "📋 路由query参数: {$matches[1]}\n";
        
        // 检查name参数
        if (strpos($matches[1], 'name:') !== false) {
            echo "✅ 传递了name参数\n";
        } else {
            echo "❌ 未传递name参数\n";
        }
    }
    
} else {
    echo "❌ 知识库列表页面文件不存在\n";
}

echo "\n🔍 检查4: 可能的编码问题\n";
echo str_repeat("-", 50) . "\n";

// 检查文件编码
$files = [
    'admin/index.html',
    'admin/src/views/knowledge_base/knowledge_base/study_data/index.vue',
    'admin/src/views/knowledge_base/knowledge_base/index.vue'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // 检测文件编码
        $encoding = mb_detect_encoding($content, ['UTF-8', 'GBK', 'GB2312', 'ISO-8859-1'], true);
        echo "📋 {$file}: 编码 {$encoding}\n";
        
        if ($encoding !== 'UTF-8') {
            echo "❌ 文件编码不是UTF-8，可能导致乱码\n";
        }
        
        // 检查BOM
        if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
            echo "⚠️ {$file}: 包含UTF-8 BOM\n";
        }
    }
}

echo "\n💡 问题诊断结果\n";
echo str_repeat("=", 60) . "\n";

echo "基于检查结果，可能的问题原因:\n\n";

echo "1. 页面标题设置缺失:\n";
echo "   - 数据学习页面没有设置页面标题\n";
echo "   - 浏览器显示默认标题或空标题\n";
echo "   - 需要添加页面标题设置\n\n";

echo "2. 路由参数传递问题:\n";
echo "   - 知识库名称可能包含特殊字符\n";
echo "   - URL编码/解码问题\n";
echo "   - 需要检查参数传递和解析\n\n";

echo "3. 字符编码问题:\n";
echo "   - 文件编码不一致\n";
echo "   - 服务器响应头编码设置\n";
echo "   - 浏览器解析编码问题\n\n";

echo "🔧 建议的修复方案:\n";
echo str_repeat("=", 60) . "\n";

echo "1. 添加页面标题设置:\n";
echo "   - 在数据学习页面添加useHead或document.title设置\n";
echo "   - 使用路由参数中的知识库名称作为标题\n";
echo "   - 确保标题格式为: '数据学习 - {知识库名称}'\n\n";

echo "2. 检查路由参数编码:\n";
echo "   - 确保知识库名称正确编码传递\n";
echo "   - 在接收端正确解码\n";
echo "   - 处理特殊字符和中文字符\n\n";

echo "3. 统一字符编码:\n";
echo "   - 确保所有文件都是UTF-8编码\n";
echo "   - 检查服务器响应头\n";
echo "   - 验证浏览器解析\n\n";

echo "🎯 优先修复步骤:\n";
echo "1. 为数据学习页面添加正确的标题设置\n";
echo "2. 测试不同知识库名称的标题显示\n";
echo "3. 检查浏览器开发者工具中的实际标题内容\n";
echo "4. 验证字符编码设置\n";
