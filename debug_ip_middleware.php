<?php
/**
 * 创建调试版本的IP中间件来检查实际获取的IP
 */

echo "=== 创建IP中间件调试版本 ===\n\n";

$debugMiddlewareContent = '<?php

namespace app\adminapi\http\middleware;

use Closure;
use think\facade\Config;
use think\facade\Log;

/**
 * 后台IP限制中间件 - 调试版本
 * Class AdminIpMiddlewareDebug
 * @package app\adminapi\http\middleware
 */
class AdminIpMiddlewareDebug
{
    /**
     * @notes IP限制验证 - 调试版本
     * @param $request
     * @param Closure $next
     * @return mixed
     * <AUTHOR>
     * @date 2024/12/26
     */
    public function handle($request, Closure $next): mixed
    {
        // 调试日志文件
        $logFile = root_path() . "debug_ip_middleware.log";
        
        $log = function($message) use ($logFile) {
            $timestamp = date("Y-m-d H:i:s");
            file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND);
        };
        
        $log("=== IP中间件调试开始 ===");
        
        // 获取IP限制配置
        $ipConfig = Config::get("project.admin_login");
        $ipRestrictions = $ipConfig["ip_restrictions"] ?? 0;
        $allowedIps = $ipConfig["allowed_ips"] ?? [];
        
        $log("IP限制开关: {$ipRestrictions}");
        $log("允许的IP列表: " . json_encode($allowedIps));
        
        // 如果IP限制功能未开启，直接通过
        if (empty($ipRestrictions) || $ipRestrictions != 1) {
            $log("IP限制未开启，直接通过");
            return $next($request);
        }
        
        // 获取客户端IP - 详细调试
        $clientIp = $this->getClientIp($request);
        $log("获取到的客户端IP: {$clientIp}");
        
        // 记录所有可能的IP来源
        $ipSources = [
            "HTTP_X_FORWARDED_FOR" => $request->header("X-Forwarded-For"),
            "HTTP_X_REAL_IP" => $request->header("X-Real-IP"),
            "HTTP_CLIENT_IP" => $request->header("Client-IP"),
            "REMOTE_ADDR" => $request->ip(),
            "REQUEST_IP" => $request->ip(),
        ];
        
        foreach ($ipSources as $source => $ip) {
            $log("IP来源 {$source}: " . ($ip ?: "未设置"));
        }
        
        // 检查IP是否在允许列表中
        $isAllowed = $this->isIpAllowed($clientIp, $allowedIps);
        $log("IP {$clientIp} 是否被允许: " . ($isAllowed ? "是" : "否"));
        
        if (!$isAllowed) {
            $log("IP {$clientIp} 不在允许列表中，返回404");
            abort(404, "页面不存在");
        }
        
        $log("IP {$clientIp} 验证通过，继续处理请求");
        $log("=== IP中间件调试结束 ===");
        
        return $next($request);
    }
    
    /**
     * @notes 获取客户端真实IP
     * @param $request
     * @return string
     * <AUTHOR>
     * @date 2024/12/26
     */
    private function getClientIp($request): string
    {
        // 优先级顺序获取客户端IP
        $ipKeys = [
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_REAL_IP", 
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "REMOTE_ADDR"
        ];
        
        foreach ($ipKeys as $key) {
            $ip = $request->header(str_replace("HTTP_", "", $key));
            if (!empty($ip) && $ip !== "unknown") {
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ",") !== false) {
                    $ips = explode(",", $ip);
                    $ip = trim($ips[0]); // 取第一个IP
                }
                
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
                
                // 如果是私有IP，也接受（用于内网环境）
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        // 如果都没有获取到，使用request的ip方法
        return $request->ip() ?: "127.0.0.1";
    }
    
    /**
     * @notes 检查IP是否被允许访问
     * @param string $clientIp
     * @param array $allowedIps
     * @return bool
     * <AUTHOR>
     * @date 2024/12/26
     */
    private function isIpAllowed(string $clientIp, array $allowedIps): bool
    {
        // 如果没有配置允许的IP列表，默认拒绝
        if (empty($allowedIps)) {
            return false;
        }
        
        foreach ($allowedIps as $allowedIp) {
            $allowedIp = trim($allowedIp);
            
            // 处理localhost特殊情况
            if ($allowedIp === "localhost" && in_array($clientIp, ["127.0.0.1", "::1"])) {
                return true;
            }
            
            // 精确匹配
            if ($clientIp === $allowedIp) {
                return true;
            }
            
            // 支持IP段匹配 (例如: ***********/24)
            if (strpos($allowedIp, "/") !== false && $this->isIpInRange($clientIp, $allowedIp)) {
                return true;
            }
            
            // 支持通配符匹配 (例如: 192.168.1.*)
            if (strpos($allowedIp, "*") !== false) {
                $pattern = "/^" . str_replace([".", "*"], ["\.", "\d+"], $allowedIp) . "$/";
                if (preg_match($pattern, $clientIp)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * @notes 检查IP是否在指定的IP段内
     * @param string $ip
     * @param string $range CIDR格式的IP段 (例如: ***********/24)
     * @return bool
     * <AUTHOR>
     * @date 2024/12/26
     */
    private function isIpInRange(string $ip, string $range): bool
    {
        if (strpos($range, "/") === false) {
            return false;
        }
        
        list($rangeIp, $netmask) = explode("/", $range, 2);
        $rangeDecimal = ip2long($rangeIp);
        $ipDecimal = ip2long($ip);
        
        if ($rangeDecimal === false || $ipDecimal === false || !is_numeric($netmask)) {
            return false;
        }
        
        $netmask = (int)$netmask;
        if ($netmask < 0 || $netmask > 32) {
            return false;
        }
        
        $wildcardDecimal = pow(2, (32 - $netmask)) - 1;
        $netmaskDecimal = ~ $wildcardDecimal;
        
        return (($ipDecimal & $netmaskDecimal) == ($rangeDecimal & $netmaskDecimal));
    }
}';

// 保存调试中间件
$debugFile = 'server/app/adminapi/http/middleware/AdminIpMiddlewareDebug.php';
file_put_contents($debugFile, $debugMiddlewareContent);

echo "✅ 调试中间件已创建: {$debugFile}\n\n";

// 备份原始路由配置
$routeFile = 'server/app/adminapi/config/route.php';
$backupFile = $routeFile . '.backup_' . date('YmdHis');
copy($routeFile, $backupFile);
echo "✅ 原始路由配置已备份: {$backupFile}\n\n";

// 修改路由配置使用调试中间件
$routeContent = file_get_contents($routeFile);
$newRouteContent = str_replace(
    'app\adminapi\http\middleware\AdminIpMiddleware::class,',
    'app\adminapi\http\middleware\AdminIpMiddlewareDebug::class,',
    $routeContent
);

file_put_contents($routeFile, $newRouteContent);
echo "✅ 路由配置已修改为使用调试中间件\n\n";

echo "🔧 调试步骤:\n";
echo str_repeat("-", 50) . "\n";
echo "1. 重启PHP服务: docker restart chatmoney-php\n";
echo "2. 访问管理后台: curl http://localhost:180/adminapi/login/account\n";
echo "3. 查看调试日志: tail -f debug_ip_middleware.log\n";
echo "4. 从外部IP访问测试拦截效果\n\n";

echo "📋 调试完成后恢复:\n";
echo "1. 恢复原始路由配置: cp {$backupFile} {$routeFile}\n";
echo "2. 删除调试中间件: rm {$debugFile}\n";
echo "3. 重启PHP服务: docker restart chatmoney-php\n\n";

echo "🎯 预期结果:\n";
echo "- 调试日志会显示实际获取的客户端IP\n";
echo "- 显示IP来源信息\n";
echo "- 显示IP匹配结果\n";
echo "- 帮助定位IP限制不生效的原因\n";
