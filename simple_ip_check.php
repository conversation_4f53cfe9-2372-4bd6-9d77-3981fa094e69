<?php
/**
 * 简单的IP限制功能检查
 */

echo "=== 后台IP限制功能简单检查 ===\n\n";

echo "🔍 检查1: 配置文件内容\n";
echo str_repeat("-", 50) . "\n";

$configFile = 'server/config/project.php';
if (file_exists($configFile)) {
    $content = file_get_contents($configFile);
    
    // 检查IP限制开关
    if (preg_match("/'ip_restrictions'\s*=>\s*(\d+)/", $content, $matches)) {
        $ipRestrictions = $matches[1];
        echo "📋 IP限制开关: {$ipRestrictions} " . ($ipRestrictions == 1 ? '(已开启)' : '(已关闭)') . "\n";
    } else {
        echo "❌ 未找到IP限制开关配置\n";
    }
    
    // 检查允许的IP列表
    if (preg_match("/'allowed_ips'\s*=>\s*\[(.*?)\]/s", $content, $matches)) {
        echo "✅ 找到允许的IP列表配置\n";
        
        // 提取IP地址
        preg_match_all("/'([^']+)'/", $matches[1], $ipMatches);
        $allowedIps = $ipMatches[1];
        
        echo "📋 允许的IP列表 (" . count($allowedIps) . " 个):\n";
        foreach ($allowedIps as $index => $ip) {
            echo "  " . ($index + 1) . ". {$ip}\n";
        }
    } else {
        echo "❌ 未找到允许的IP列表配置\n";
    }
} else {
    echo "❌ 配置文件不存在\n";
}

echo "\n🔍 检查2: 中间件文件状态\n";
echo str_repeat("-", 50) . "\n";

$middlewareFile = 'server/app/adminapi/http/middleware/AdminIpMiddleware.php';
if (file_exists($middlewareFile)) {
    echo "✅ IP限制中间件文件存在\n";
    
    $content = file_get_contents($middlewareFile);
    $fileSize = strlen($content);
    echo "📋 文件大小: {$fileSize} 字节\n";
    
    // 检查文件修改时间
    $modTime = filemtime($middlewareFile);
    echo "📋 最后修改: " . date('Y-m-d H:i:s', $modTime) . "\n";
    
    // 检查关键代码
    $checks = [
        'ip_restrictions' => 'IP限制开关检查',
        'allowed_ips' => '允许IP列表检查',
        'getClientIp' => '获取客户端IP方法',
        'isIpAllowed' => 'IP允许检查方法',
        'abort(404' => '404拦截逻辑'
    ];
    
    foreach ($checks as $keyword => $description) {
        if (strpos($content, $keyword) !== false) {
            echo "✅ {$description}: 存在\n";
        } else {
            echo "❌ {$description}: 缺失\n";
        }
    }
} else {
    echo "❌ IP限制中间件文件不存在\n";
}

echo "\n🔍 检查3: 路由中间件注册\n";
echo str_repeat("-", 50) . "\n";

$routeFile = 'server/app/adminapi/config/route.php';
if (file_exists($routeFile)) {
    echo "✅ 路由配置文件存在\n";
    
    $content = file_get_contents($routeFile);
    
    if (strpos($content, 'AdminIpMiddleware') !== false) {
        echo "✅ IP限制中间件已注册\n";
        
        // 检查注册位置
        $lines = explode("\n", $content);
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, 'AdminIpMiddleware') !== false) {
                echo "📋 注册位置: 第" . ($lineNum + 1) . "行\n";
                echo "📋 注册内容: " . trim($line) . "\n";
                break;
            }
        }
    } else {
        echo "❌ IP限制中间件未注册\n";
    }
} else {
    echo "❌ 路由配置文件不存在\n";
}

echo "\n🔍 检查4: 实际访问测试\n";
echo str_repeat("-", 50) . "\n";

// 测试管理后台登录页面访问
$testUrl = 'http://localhost:180/adminapi/login/account';
echo "📋 测试URL: {$testUrl}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部信息

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "📋 HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "❌ 请求错误: {$error}\n";
} else {
    if ($httpCode == 200) {
        echo "✅ 访问成功 - IP限制可能未生效或当前IP在白名单中\n";
    } elseif ($httpCode == 404) {
        echo "🔒 返回404 - IP限制可能正在工作\n";
    } else {
        echo "⚠️ 返回状态码 {$httpCode} - 需要进一步检查\n";
    }
}

echo "\n🔍 检查5: Docker环境IP获取\n";
echo str_repeat("-", 50) . "\n";

// 检查Docker网络配置
echo "📋 Docker环境下的IP获取可能受到以下因素影响:\n";
echo "1. 容器网络模式 (bridge/host/overlay)\n";
echo "2. 反向代理配置 (nginx/apache)\n";
echo "3. 负载均衡器设置\n";
echo "4. X-Forwarded-For 头部传递\n\n";

// 检查nginx配置文件
$nginxConfigs = [
    '/etc/nginx/nginx.conf',
    '/etc/nginx/conf.d/default.conf',
    'docker/nginx/nginx.conf',
    'docker/nginx/conf.d/default.conf'
];

echo "📋 检查nginx配置文件:\n";
foreach ($nginxConfigs as $configPath) {
    if (file_exists($configPath)) {
        echo "✅ 找到nginx配置: {$configPath}\n";
        
        $content = file_get_contents($configPath);
        if (strpos($content, 'X-Forwarded-For') !== false) {
            echo "  ✅ 包含X-Forwarded-For配置\n";
        } else {
            echo "  ⚠️ 未找到X-Forwarded-For配置\n";
        }
        
        if (strpos($content, 'X-Real-IP') !== false) {
            echo "  ✅ 包含X-Real-IP配置\n";
        } else {
            echo "  ⚠️ 未找到X-Real-IP配置\n";
        }
    }
}

echo "\n💡 诊断结果和建议\n";
echo str_repeat("=", 60) . "\n";

echo "🎯 基于检查结果，IP限制功能的状态:\n\n";

// 读取配置状态
$configContent = file_exists($configFile) ? file_get_contents($configFile) : '';
$isEnabled = preg_match("/'ip_restrictions'\s*=>\s*1/", $configContent);
$hasIpList = preg_match("/'allowed_ips'\s*=>\s*\[/", $configContent);
$middlewareExists = file_exists($middlewareFile);
$routeRegistered = file_exists($routeFile) && strpos(file_get_contents($routeFile), 'AdminIpMiddleware') !== false;

if ($isEnabled && $hasIpList && $middlewareExists && $routeRegistered) {
    echo "✅ IP限制功能配置完整，应该正常工作\n\n";
    
    echo "🔧 如果IP限制仍然不起作用，请检查:\n";
    echo "1. nginx是否正确传递客户端IP:\n";
    echo "   proxy_set_header X-Real-IP \$remote_addr;\n";
    echo "   proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n\n";
    
    echo "2. 重启相关服务:\n";
    echo "   docker restart chatmoney-php\n";
    echo "   docker restart chatmoney-nginx\n\n";
    
    echo "3. 检查实际获取的IP地址:\n";
    echo "   在中间件中添加日志记录当前IP\n\n";
    
    echo "4. 测试不同来源的访问:\n";
    echo "   - 从服务器本地访问\n";
    echo "   - 从外部网络访问\n";
    echo "   - 检查是否被正确拦截\n";
    
} else {
    echo "❌ IP限制功能配置不完整:\n";
    if (!$isEnabled) echo "  - IP限制开关未开启\n";
    if (!$hasIpList) echo "  - 未配置允许的IP列表\n";
    if (!$middlewareExists) echo "  - 中间件文件不存在\n";
    if (!$routeRegistered) echo "  - 中间件未注册到路由\n";
}

echo "\n🚀 快速修复建议:\n";
echo "1. 确保IP限制开关开启: ip_restrictions => 1\n";
echo "2. 配置允许的IP列表\n";
echo "3. 重启PHP服务: docker restart chatmoney-php\n";
echo "4. 测试访问效果\n";
