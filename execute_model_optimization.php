<?php
/**
 * 执行创作模型优化 - 基于文档分析的改进方案
 */

echo "=== 创作模型优化执行器 ===\n\n";

// 数据库连接配置
$config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'chatmoney',
    'username' => 'root',
    'password' => '123456Abcd'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset=utf8mb4",
        $config['username'],
        $config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 创建备份表（如果不存在）
    $createBackupTable = "
    CREATE TABLE IF NOT EXISTS cm_creation_model_backup (
        id int(10) unsigned NOT NULL,
        name varchar(32) NOT NULL DEFAULT '',
        image varchar(64) NOT NULL DEFAULT '',
        sort int(10) unsigned NOT NULL DEFAULT '0',
        category_id int(10) unsigned NOT NULL DEFAULT '0',
        status tinyint(1) unsigned NOT NULL DEFAULT '0',
        content text,
        tips varchar(255) NOT NULL DEFAULT '',
        context_num int(10) unsigned NOT NULL DEFAULT '0',
        n int(10) unsigned NOT NULL DEFAULT '0',
        top_p decimal(2,1) unsigned NOT NULL DEFAULT '0.0',
        presence_penalty decimal(2,1) unsigned NOT NULL DEFAULT '0.0',
        frequency_penalty decimal(2,1) unsigned NOT NULL DEFAULT '0.0',
        temperature decimal(2,1) unsigned NOT NULL DEFAULT '0.0',
        max_tokens int(5) unsigned NOT NULL DEFAULT '0',
        form text,
        virtual_use_num int(10) unsigned NOT NULL DEFAULT '0',
        system text,
        create_time int(10) unsigned DEFAULT NULL,
        update_time int(10) unsigned DEFAULT NULL,
        delete_time int(10) unsigned DEFAULT NULL,
        backup_time timestamp DEFAULT CURRENT_TIMESTAMP,
        backup_reason varchar(255) DEFAULT ''
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='创作模型备份表';
    ";
    
    $pdo->exec($createBackupTable);
    echo "✅ 备份表创建/检查完成\n\n";
    
    // 优化方案定义 - 高优先级模型
    $optimizations = [
        // 1. 翻译助手 (ID: 3)
        [
            'id' => 3,
            'name' => '专业翻译助手',
            'description' => '从基础翻译工具升级为专业翻译助手系统',
            'content' => '你是一名专业的${source_language}到${target_language}翻译师，具有${domain_specialization}领域的专业背景。

请按照${translation_style}风格，将以下内容进行准确翻译：

原文：${content_to_translate}

背景说明：${context_background}

翻译要求：
1. 保持原文的语调和风格
2. 确保专业术语准确
3. 适应目标语言的表达习惯
4. 提供简要的翻译注释（如有必要）

请提供高质量的翻译结果。',
            'form' => [
                ["name" => "WidgetSelect", "props" => ["field" => "source_language", "title" => "源语言", "options" => ["中文", "英文", "日文", "韩文", "法文", "德文", "西班牙文"], "defaultValue" => "中文", "isRequired" => true, "placeholder" => "请选择源语言"]],
                ["name" => "WidgetSelect", "props" => ["field" => "target_language", "title" => "目标语言", "options" => ["中文", "英文", "日文", "韩文", "法文", "德文", "西班牙文"], "defaultValue" => "英文", "isRequired" => true, "placeholder" => "请选择目标语言"]],
                ["name" => "WidgetRadio", "props" => ["field" => "translation_style", "title" => "翻译风格", "options" => ["正式商务", "日常口语", "学术论文", "文学创作", "技术文档"], "defaultValue" => "正式商务", "isRequired" => true]],
                ["name" => "WidgetSelect", "props" => ["field" => "domain_specialization", "title" => "专业领域", "options" => ["通用", "商务贸易", "科技IT", "医学健康", "法律法规", "金融经济", "教育培训"], "defaultValue" => "通用", "isRequired" => false, "placeholder" => "选择专业领域以提高翻译准确性"]],
                ["name" => "WidgetTextarea", "props" => ["field" => "content_to_translate", "title" => "翻译内容", "placeholder" => "请输入需要翻译的文本内容", "rows" => 6, "maxlength" => 2000, "isRequired" => true]],
                ["name" => "WidgetTextarea", "props" => ["field" => "context_background", "title" => "背景信息（可选）", "placeholder" => "如有特殊背景或使用场景，请简要说明", "rows" => 3, "maxlength" => 500, "isRequired" => false]]
            ],
            'tips' => '专业翻译助手支持多语种互译，可根据不同领域和风格需求提供精准翻译服务。选择合适的翻译风格和专业领域，将显著提升翻译质量。'
        ],

        // 2. 论文资料 (ID: 12)
        [
            'id' => 12,
            'name' => '学术论文写作助手',
            'description' => '从简单主题扩展为完整学术写作指导系统',
            'content' => '你是一名资深的${academic_discipline}领域学术导师，专门指导${paper_type}论文写作。

论文主题：${research_topic}
研究方法：${research_method}
字数要求：${word_count}
引用格式：${citation_format}

研究背景：${research_background}

请为以上论文主题提供详细的写作指导，包括：

## 1. 论文结构框架
- 标题设计建议
- 摘要写作要点
- 章节安排建议
- 结论总结方向

## 2. 研究方法指导
- 适合的研究方法分析
- 数据收集建议
- 分析框架构建

## 3. 文献综述方向
- 核心文献推荐方向
- 理论框架构建
- 研究空白识别

## 4. 写作技巧建议
- 学术写作规范
- 逻辑结构优化
- 语言表达改进

## 5. 引用规范指导
- ${citation_format}格式要求
- 参考文献整理
- 避免学术不端

请提供专业、详细的学术写作指导方案。',
            'form' => [
                ["name" => "WidgetSelect", "props" => ["field" => "academic_discipline", "title" => "学科领域", "options" => ["文学", "历史学", "哲学", "经济学", "管理学", "法学", "教育学", "心理学", "社会学", "政治学", "计算机科学", "数学", "物理学", "化学", "生物学", "医学", "工程学", "艺术学"], "defaultValue" => "管理学", "isRequired" => true, "placeholder" => "请选择论文所属学科领域"]],
                ["name" => "WidgetRadio", "props" => ["field" => "paper_type", "title" => "论文类型", "options" => ["学士学位论文", "硕士学位论文", "博士学位论文", "期刊论文", "会议论文", "课程论文"], "defaultValue" => "硕士学位论文", "isRequired" => true]],
                ["name" => "WidgetSelect", "props" => ["field" => "research_method", "title" => "研究方法", "options" => ["定量研究", "定性研究", "混合研究", "实验研究", "调查研究", "案例研究", "文献研究", "比较研究"], "defaultValue" => "定量研究", "isRequired" => true, "placeholder" => "选择主要研究方法"]],
                ["name" => "WidgetSelect", "props" => ["field" => "word_count", "title" => "字数要求", "options" => ["5000-8000字", "8000-12000字", "12000-20000字", "20000-30000字", "30000-50000字", "50000字以上"], "defaultValue" => "12000-20000字", "isRequired" => true, "placeholder" => "选择论文字数要求"]],
                ["name" => "WidgetRadio", "props" => ["field" => "citation_format", "title" => "引用格式", "options" => ["APA格式", "MLA格式", "Chicago格式", "Harvard格式", "GB/T 7714格式"], "defaultValue" => "APA格式", "isRequired" => true]],
                ["name" => "WidgetTextarea", "props" => ["field" => "research_topic", "title" => "论文主题", "placeholder" => "请详细描述您的论文研究主题和核心问题", "rows" => 4, "maxlength" => 1000, "isRequired" => true]],
                ["name" => "WidgetTextarea", "props" => ["field" => "research_background", "title" => "研究背景（可选）", "placeholder" => "简要说明研究背景、研究意义或已有基础", "rows" => 4, "maxlength" => 1000, "isRequired" => false]]
            ],
            'tips' => '学术论文写作助手提供从选题到完稿的全流程指导。根据不同学科领域和论文类型，提供个性化的写作建议和学术规范指导。'
        ],

        // 3. 英文写作 (ID: 25)
        [
            'id' => 25,
            'name' => '英文写作导师',
            'description' => '从基础写作升级为专业英文写作导师系统',
            'content' => '你是一名专业的英文写作导师，具有丰富的${writing_type}写作经验，专门为${english_level}水平的学习者提供指导。

写作任务：${writing_task}
写作风格：${writing_style}
目标受众：${target_audience}
字数要求：${word_count}

特殊要求：${special_requirements}

请为以上写作任务提供专业指导，包括：

## 1. 写作结构建议
- 开头段落设计
- 主体段落安排
- 结尾段落总结
- 逻辑连接词使用

## 2. 语言表达优化
- 词汇选择建议
- 句式结构多样化
- 语法准确性检查
- 地道表达方式

## 3. 内容组织指导
- 论点提炼和支撑
- 例证选择和使用
- 段落间逻辑关系
- 整体连贯性

## 4. 写作技巧提升
- ${writing_style}风格特点
- 目标受众适配
- 文体规范要求
- 常见错误避免

## 5. 修改完善建议
- 内容充实方向
- 语言润色要点
- 格式规范检查
- 最终质量提升

请提供详细的英文写作指导和具体的写作建议。',
            'form' => [
                ["name" => "WidgetRadio", "props" => ["field" => "writing_type", "title" => "写作类型", "options" => ["学术论文", "商务邮件", "求职信", "个人陈述", "报告写作", "创意写作", "新闻写作", "技术文档"], "defaultValue" => "学术论文", "isRequired" => true]],
                ["name" => "WidgetSelect", "props" => ["field" => "english_level", "title" => "英语水平", "options" => ["初级(A1-A2)", "中级(B1-B2)", "高级(C1-C2)", "母语水平"], "defaultValue" => "中级(B1-B2)", "isRequired" => true, "placeholder" => "选择您的英语水平"]],
                ["name" => "WidgetSelect", "props" => ["field" => "writing_style", "title" => "写作风格", "options" => ["正式学术", "商务专业", "日常交流", "创意文学", "新闻报道", "技术说明", "说服性写作"], "defaultValue" => "正式学术", "isRequired" => true, "placeholder" => "选择适合的写作风格"]],
                ["name" => "WidgetSelect", "props" => ["field" => "target_audience", "title" => "目标受众", "options" => ["学术同行", "商务伙伴", "普通读者", "专业人士", "学生群体", "国际友人", "特定行业"], "defaultValue" => "学术同行", "isRequired" => true, "placeholder" => "选择主要目标读者"]],
                ["name" => "WidgetSelect", "props" => ["field" => "word_count", "title" => "字数要求", "options" => ["100-300词", "300-500词", "500-800词", "800-1200词", "1200-2000词", "2000词以上"], "defaultValue" => "500-800词", "isRequired" => true, "placeholder" => "选择目标字数范围"]],
                ["name" => "WidgetTextarea", "props" => ["field" => "writing_task", "title" => "写作任务", "placeholder" => "请详细描述您的英文写作任务、主题和具体要求", "rows" => 5, "maxlength" => 1500, "isRequired" => true]],
                ["name" => "WidgetTextarea", "props" => ["field" => "special_requirements", "title" => "特殊要求（可选）", "placeholder" => "如有特殊格式要求、引用规范或其他具体需求，请在此说明", "rows" => 3, "maxlength" => 800, "isRequired" => false]]
            ],
            'tips' => '英文写作导师根据您的英语水平和写作需求，提供个性化的写作指导。从结构规划到语言表达，全方位提升您的英文写作能力。'
        ]
    ];
    
    // 执行优化
    foreach ($optimizations as $optimization) {
        echo "🔄 正在优化模型 ID: {$optimization['id']} - {$optimization['name']}\n";
        echo "   描述: {$optimization['description']}\n";
        
        // 备份原始数据
        $backupSql = "INSERT INTO cm_creation_model_backup 
                      SELECT *, NOW() as backup_time, '优化前备份' as backup_reason 
                      FROM cm_creation_model 
                      WHERE id = ?";
        $backupStmt = $pdo->prepare($backupSql);
        $backupStmt->execute([$optimization['id']]);
        
        // 更新模型
        $updateSql = "UPDATE cm_creation_model SET 
                      name = ?, 
                      content = ?, 
                      form = ?, 
                      tips = ?, 
                      update_time = UNIX_TIMESTAMP() 
                      WHERE id = ?";
        
        $updateStmt = $pdo->prepare($updateSql);
        $updateStmt->execute([
            $optimization['name'],
            $optimization['content'],
            json_encode($optimization['form'], JSON_UNESCAPED_UNICODE),
            $optimization['tips'],
            $optimization['id']
        ]);
        
        echo "   ✅ 优化完成\n";
        echo "   📊 新增字段数量: " . count($optimization['form']) . "\n\n";
    }
    
    // 验证优化结果
    echo "📋 优化结果验证:\n";
    echo str_repeat("-", 50) . "\n";
    
    $verifyIds = array_column($optimizations, 'id');
    $placeholders = str_repeat('?,', count($verifyIds) - 1) . '?';
    
    $verifySql = "SELECT id, name, 
                  CHAR_LENGTH(content) as content_length,
                  JSON_LENGTH(form) as form_field_count,
                  update_time
                  FROM cm_creation_model 
                  WHERE id IN ($placeholders) 
                  ORDER BY id";
    
    $verifyStmt = $pdo->prepare($verifySql);
    $verifyStmt->execute($verifyIds);
    $results = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($results as $result) {
        echo "ID: {$result['id']} - {$result['name']}\n";
        echo "  Content长度: {$result['content_length']} 字符\n";
        echo "  表单字段数: {$result['form_field_count']} 个\n";
        echo "  更新时间: " . date('Y-m-d H:i:s', $result['update_time']) . "\n\n";
    }
    
    echo "🎉 第一批模型优化完成！\n";
    echo "📈 优化效果:\n";
    echo "  - 字段数量显著增加\n";
    echo "  - 变量命名语义化\n";
    echo "  - 专业身份设定完善\n";
    echo "  - 用户体验大幅提升\n";
    
} catch (Exception $e) {
    echo "❌ 执行失败: " . $e->getMessage() . "\n";
    echo "📋 错误详情: " . $e->getTraceAsString() . "\n";
}

echo "\n💡 下一步建议:\n";
echo "1. 测试优化后的翻译助手功能\n";
echo "2. 继续优化其他高优先级模型\n";
echo "3. 收集用户反馈进行微调\n";
echo "4. 监控使用数据和效果\n";
