<?php
/**
 * 检查当前创作模型的配置状态
 */

echo "=== 创作模型优化前状态检查 ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=chatmoney;charset=utf8mb4', 'root', '123456Abcd');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询高优先级优化模型
    $highPriorityIds = [3, 12, 25, 22, 19, 4, 21, 24, 16];
    $placeholders = str_repeat('?,', count($highPriorityIds) - 1) . '?';
    
    $stmt = $pdo->prepare("SELECT id, name, content, form FROM cm_creation_model WHERE id IN ($placeholders) ORDER BY id");
    $stmt->execute($highPriorityIds);
    $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "🔍 高优先级优化模型当前状态:\n";
    echo str_repeat("-", 60) . "\n";
    
    foreach ($models as $model) {
        echo "📋 ID: {$model['id']} - {$model['name']}\n";
        
        // 分析form字段
        $form = json_decode($model['form'], true);
        $fieldCount = is_array($form) ? count($form) : 0;
        echo "  表单字段数量: {$fieldCount}\n";
        
        if ($fieldCount > 0) {
            echo "  字段列表:\n";
            foreach ($form as $index => $field) {
                $fieldName = $field['props']['field'] ?? '未知';
                $fieldTitle = $field['props']['title'] ?? '未知';
                $componentType = $field['name'] ?? '未知';
                echo "    " . ($index + 1) . ". {$fieldName} ({$fieldTitle}) - {$componentType}\n";
            }
        }
        
        // 分析content字段中的变量
        $content = $model['content'];
        preg_match_all('/\$\{([^}]+)\}/', $content, $matches);
        $variables = $matches[1] ?? [];
        
        echo "  Content变量数量: " . count($variables) . "\n";
        if (!empty($variables)) {
            echo "  变量列表: " . implode(', ', $variables) . "\n";
        }
        
        // 检查变量命名是否语义化
        $hasSemanticNames = false;
        foreach ($variables as $var) {
            if (strlen($var) > 5 && !preg_match('/^[a-z0-9]{8,}$/i', $var)) {
                $hasSemanticNames = true;
                break;
            }
        }
        
        echo "  语义化变量: " . ($hasSemanticNames ? "✅" : "❌") . "\n";
        echo "  专业身份设定: " . (strpos($content, '你是') !== false ? "✅" : "❌") . "\n";
        echo "\n";
    }
    
    echo "📊 优化需求分析:\n";
    echo str_repeat("-", 60) . "\n";
    
    $needsOptimization = [];
    foreach ($models as $model) {
        $form = json_decode($model['form'], true);
        $fieldCount = is_array($form) ? count($form) : 0;
        
        $issues = [];
        if ($fieldCount <= 2) {
            $issues[] = "字段数量过少({$fieldCount}个)";
        }
        
        preg_match_all('/\$\{([^}]+)\}/', $model['content'], $matches);
        $variables = $matches[1] ?? [];
        
        $hasRandomNames = false;
        foreach ($variables as $var) {
            if (preg_match('/^[a-z0-9]{6,}$/i', $var) && strlen($var) >= 6) {
                $hasRandomNames = true;
                break;
            }
        }
        
        if ($hasRandomNames) {
            $issues[] = "变量命名随机化";
        }
        
        if (strpos($model['content'], '你是') === false) {
            $issues[] = "缺少专业身份设定";
        }
        
        if (!empty($issues)) {
            $needsOptimization[] = [
                'id' => $model['id'],
                'name' => $model['name'],
                'issues' => $issues
            ];
        }
    }
    
    echo "需要优化的模型 (" . count($needsOptimization) . "个):\n";
    foreach ($needsOptimization as $model) {
        echo "🔴 ID: {$model['id']} - {$model['name']}\n";
        echo "   问题: " . implode(', ', $model['issues']) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n💡 基于文档分析的优化建议:\n";
echo str_repeat("=", 60) . "\n";
echo "1. 翻译助手(ID:3) - 增加语言方向、翻译风格、专业领域选择\n";
echo "2. 论文资料(ID:12) - 增加学科领域、论文类型、研究方法\n";
echo "3. 英文写作(ID:25) - 增加写作类型、英语水平、写作风格\n";
echo "4. 广告文案(ID:22) - 增加产品类型、目标用户、平台适配\n";
echo "5. 产品描述(ID:19) - 增加产品类型、销售渠道、SEO优化\n";
echo "6. 短视频脚本(ID:4) - 增加视频类型、时长控制、平台适配\n";
echo "7. 菜谱大全(ID:21) - 增加菜系分类、难度等级、饮食偏好\n";
echo "8. 旅游计划(ID:24) - 增加旅游类型、预算控制、时间安排\n";
echo "9. 挂号咨询(ID:16) - 增加症状分类、紧急程度、免责声明\n";

echo "\n🎯 优化目标:\n";
echo "- 字段数量: 从平均2.5个增加到7个\n";
echo "- 语义化变量: 从0%提升到100%\n";
echo "- 专业身份设定: 从20%提升到100%\n";
echo "- 结构化输出: 从30%提升到100%\n";
