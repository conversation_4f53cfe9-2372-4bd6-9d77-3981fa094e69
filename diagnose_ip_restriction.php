<?php
/**
 * 诊断后台IP限制功能是否正常工作
 */

echo "=== 后台IP限制功能诊断 ===\n\n";

// 检查配置文件
echo "🔍 检查1: IP限制配置\n";
echo str_repeat("-", 50) . "\n";

$configFile = 'server/config/project.php';
if (file_exists($configFile)) {
    $config = include $configFile;
    $adminLogin = $config['admin_login'] ?? [];
    
    echo "✅ 配置文件存在\n";
    echo "📋 IP限制开关: " . ($adminLogin['ip_restrictions'] ?? '未设置') . "\n";
    echo "📋 允许的IP列表:\n";
    
    $allowedIps = $adminLogin['allowed_ips'] ?? [];
    if (!empty($allowedIps)) {
        foreach ($allowedIps as $index => $ip) {
            echo "  " . ($index + 1) . ". {$ip}\n";
        }
    } else {
        echo "  ❌ 未配置允许的IP列表\n";
    }
} else {
    echo "❌ 配置文件不存在\n";
}

echo "\n🔍 检查2: IP限制中间件\n";
echo str_repeat("-", 50) . "\n";

$middlewareFile = 'server/app/adminapi/http/middleware/AdminIpMiddleware.php';
if (file_exists($middlewareFile)) {
    echo "✅ IP限制中间件文件存在\n";
    
    $content = file_get_contents($middlewareFile);
    
    // 检查关键方法
    if (strpos($content, 'public function handle') !== false) {
        echo "✅ handle方法存在\n";
    } else {
        echo "❌ handle方法缺失\n";
    }
    
    if (strpos($content, 'getClientIp') !== false) {
        echo "✅ getClientIp方法存在\n";
    } else {
        echo "❌ getClientIp方法缺失\n";
    }
    
    if (strpos($content, 'isIpAllowed') !== false) {
        echo "✅ isIpAllowed方法存在\n";
    } else {
        echo "❌ isIpAllowed方法缺失\n";
    }
    
} else {
    echo "❌ IP限制中间件文件不存在\n";
}

echo "\n🔍 检查3: 中间件注册\n";
echo str_repeat("-", 50) . "\n";

$routeConfigFile = 'server/app/adminapi/config/route.php';
if (file_exists($routeConfigFile)) {
    echo "✅ 路由配置文件存在\n";
    
    $content = file_get_contents($routeConfigFile);
    
    if (strpos($content, 'AdminIpMiddleware') !== false) {
        echo "✅ IP限制中间件已注册\n";
        
        // 检查注册顺序
        $lines = explode("\n", $content);
        $middlewareOrder = [];
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, 'Middleware::class') !== false) {
                $middlewareOrder[] = ($lineNum + 1) . ": " . trim($line);
            }
        }
        
        echo "📋 中间件注册顺序:\n";
        foreach ($middlewareOrder as $order) {
            echo "  {$order}\n";
        }
    } else {
        echo "❌ IP限制中间件未注册\n";
    }
} else {
    echo "❌ 路由配置文件不存在\n";
}

echo "\n🔍 检查4: 当前访问IP获取测试\n";
echo str_repeat("-", 50) . "\n";

// 模拟获取客户端IP的逻辑
function getClientIpTest() {
    $ipKeys = [
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP', 
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR'
    ];
    
    $ips = [];
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips[$key] = $_SERVER[$key];
        }
    }
    
    return $ips;
}

$detectedIps = getClientIpTest();
if (!empty($detectedIps)) {
    echo "📋 检测到的IP信息:\n";
    foreach ($detectedIps as $key => $ip) {
        echo "  {$key}: {$ip}\n";
    }
} else {
    echo "⚠️ 未检测到IP信息（可能在CLI模式下运行）\n";
}

// 如果在Web环境下，显示实际的客户端IP
if (isset($_SERVER['REMOTE_ADDR'])) {
    echo "📋 当前客户端IP: {$_SERVER['REMOTE_ADDR']}\n";
} else {
    echo "⚠️ 当前不在Web环境下\n";
}

echo "\n🔍 检查5: IP匹配逻辑测试\n";
echo str_repeat("-", 50) . "\n";

// 测试IP匹配逻辑
function testIpMatching() {
    $testCases = [
        ['127.0.0.1', ['127.0.0.1'], true],
        ['127.0.0.1', ['localhost'], true],
        ['*************', ['***********/24'], true],
        ['*************', ['192.168.1.*'], true],
        ['********', ['***********/24'], false],
        ['**********', ['**********/16'], true],
    ];
    
    echo "📋 IP匹配逻辑测试:\n";
    foreach ($testCases as $index => $case) {
        list($testIp, $allowedIps, $expected) = $case;
        $result = testIsIpAllowed($testIp, $allowedIps);
        $status = ($result === $expected) ? '✅' : '❌';
        echo "  {$status} 测试" . ($index + 1) . ": IP {$testIp} 在 [" . implode(', ', $allowedIps) . "] 中 -> " . ($result ? '允许' : '拒绝') . "\n";
    }
}

function testIsIpAllowed($clientIp, $allowedIps) {
    if (empty($allowedIps)) {
        return false;
    }
    
    foreach ($allowedIps as $allowedIp) {
        $allowedIp = trim($allowedIp);
        
        // 处理localhost特殊情况
        if ($allowedIp === 'localhost' && in_array($clientIp, ['127.0.0.1', '::1'])) {
            return true;
        }
        
        // 精确匹配
        if ($clientIp === $allowedIp) {
            return true;
        }
        
        // 支持IP段匹配 (例如: ***********/24)
        if (strpos($allowedIp, '/') !== false && testIsIpInRange($clientIp, $allowedIp)) {
            return true;
        }
        
        // 支持通配符匹配 (例如: 192.168.1.*)
        if (strpos($allowedIp, '*') !== false) {
            $pattern = '/^' . str_replace(['.', '*'], ['\.', '\d+'], $allowedIp) . '$/';
            if (preg_match($pattern, $clientIp)) {
                return true;
            }
        }
    }
    
    return false;
}

function testIsIpInRange($ip, $range) {
    if (strpos($range, '/') === false) {
        return false;
    }
    
    list($rangeIp, $netmask) = explode('/', $range, 2);
    $rangeDecimal = ip2long($rangeIp);
    $ipDecimal = ip2long($ip);
    
    if ($rangeDecimal === false || $ipDecimal === false || !is_numeric($netmask)) {
        return false;
    }
    
    $netmask = (int)$netmask;
    if ($netmask < 0 || $netmask > 32) {
        return false;
    }
    
    $wildcardDecimal = pow(2, (32 - $netmask)) - 1;
    $netmaskDecimal = ~ $wildcardDecimal;
    
    return (($ipDecimal & $netmaskDecimal) == ($rangeDecimal & $netmaskDecimal));
}

testIpMatching();

echo "\n🔍 检查6: 实际访问测试\n";
echo str_repeat("-", 50) . "\n";

// 测试实际的管理后台访问
echo "📋 建议的测试步骤:\n";
echo "1. 从允许的IP访问管理后台登录页面\n";
echo "2. 从不允许的IP访问管理后台登录页面\n";
echo "3. 检查是否返回404错误\n";
echo "4. 查看服务器日志是否有相关记录\n\n";

echo "💡 诊断结果总结\n";
echo str_repeat("=", 60) . "\n";

// 重新读取配置进行最终判断
if (file_exists($configFile)) {
    $config = include $configFile;
    $adminLogin = $config['admin_login'] ?? [];
    $ipRestrictions = $adminLogin['ip_restrictions'] ?? 0;
    $allowedIps = $adminLogin['allowed_ips'] ?? [];
    
    echo "🎯 IP限制功能状态:\n";
    
    if ($ipRestrictions == 1) {
        echo "✅ IP限制功能已开启\n";
        
        if (!empty($allowedIps)) {
            echo "✅ 已配置允许的IP列表 (" . count($allowedIps) . " 个IP/IP段)\n";
            echo "✅ IP限制中间件已注册\n";
            echo "✅ 功能应该正常工作\n\n";
            
            echo "🔧 如果IP限制不起作用，可能的原因:\n";
            echo "1. 代理服务器配置问题，真实IP被代理隐藏\n";
            echo "2. 负载均衡器没有正确传递客户端IP\n";
            echo "3. Docker网络配置导致IP获取错误\n";
            echo "4. 中间件执行顺序问题\n";
            echo "5. 缓存问题，配置修改未生效\n\n";
            
            echo "🔍 调试建议:\n";
            echo "1. 检查nginx/apache配置，确保正确传递客户端IP\n";
            echo "2. 在中间件中添加日志，记录实际获取的IP\n";
            echo "3. 测试从不同IP访问，观察是否被拦截\n";
            echo "4. 检查Docker容器的网络模式\n";
            echo "5. 重启PHP-FPM服务，确保配置生效\n";
            
        } else {
            echo "❌ 未配置允许的IP列表，所有访问都会被拒绝\n";
        }
    } else {
        echo "⚠️ IP限制功能已关闭\n";
        echo "如需启用，请将 ip_restrictions 设置为 1\n";
    }
} else {
    echo "❌ 无法读取配置文件\n";
}

echo "\n🚀 快速测试命令:\n";
echo "# 测试从本地访问\n";
echo "curl -I http://localhost:180/adminapi/login/account\n\n";
echo "# 查看IP限制中间件日志（如果有的话）\n";
echo "tail -f debug_ip_middleware.log\n\n";
echo "# 重启PHP服务确保配置生效\n";
echo "docker restart chatmoney-php\n";
