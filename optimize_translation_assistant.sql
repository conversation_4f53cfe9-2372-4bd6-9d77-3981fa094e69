-- 翻译助手(ID:3)优化 - 从基础翻译工具升级为专业翻译助手系统
-- 基于文档分析的优化方案

-- 备份原始数据
INSERT INTO cm_creation_model_backup (
    SELECT *, NOW() as backup_time, '优化前备份' as backup_reason 
    FROM cm_creation_model 
    WHERE id = 3
);

-- 优化翻译助手模型
UPDATE cm_creation_model SET 
    name = '专业翻译助手',
    content = '你是一名专业的${source_language}到${target_language}翻译师，具有${domain_specialization}领域的专业背景。

请按照${translation_style}风格，将以下内容进行准确翻译：

原文：${content_to_translate}

背景说明：${context_background}

翻译要求：
1. 保持原文的语调和风格
2. 确保专业术语准确
3. 适应目标语言的表达习惯
4. 提供简要的翻译注释（如有必要）

请提供高质量的翻译结果。',

    form = '[
        {
            "name": "WidgetSelect",
            "props": {
                "field": "source_language",
                "title": "源语言",
                "options": ["中文", "英文", "日文", "韩文", "法文", "德文", "西班牙文"],
                "defaultValue": "中文",
                "isRequired": true,
                "placeholder": "请选择源语言"
            }
        },
        {
            "name": "WidgetSelect", 
            "props": {
                "field": "target_language",
                "title": "目标语言",
                "options": ["中文", "英文", "日文", "韩文", "法文", "德文", "西班牙文"],
                "defaultValue": "英文",
                "isRequired": true,
                "placeholder": "请选择目标语言"
            }
        },
        {
            "name": "WidgetRadio",
            "props": {
                "field": "translation_style",
                "title": "翻译风格",
                "options": ["正式商务", "日常口语", "学术论文", "文学创作", "技术文档"],
                "defaultValue": "正式商务",
                "isRequired": true
            }
        },
        {
            "name": "WidgetSelect",
            "props": {
                "field": "domain_specialization", 
                "title": "专业领域",
                "options": ["通用", "商务贸易", "科技IT", "医学健康", "法律法规", "金融经济", "教育培训"],
                "defaultValue": "通用",
                "isRequired": false,
                "placeholder": "选择专业领域以提高翻译准确性"
            }
        },
        {
            "name": "WidgetTextarea",
            "props": {
                "field": "content_to_translate",
                "title": "翻译内容",
                "placeholder": "请输入需要翻译的文本内容",
                "rows": 6,
                "maxlength": 2000,
                "isRequired": true
            }
        },
        {
            "name": "WidgetTextarea",
            "props": {
                "field": "context_background",
                "title": "背景信息（可选）",
                "placeholder": "如有特殊背景或使用场景，请简要说明",
                "rows": 3,
                "maxlength": 500,
                "isRequired": false
            }
        }
    ]',
    
    tips = '专业翻译助手支持多语种互译，可根据不同领域和风格需求提供精准翻译服务。选择合适的翻译风格和专业领域，将显著提升翻译质量。',
    
    update_time = UNIX_TIMESTAMP()
    
WHERE id = 3;

-- 验证更新结果
SELECT id, name, 
       SUBSTRING(content, 1, 100) as content_preview,
       JSON_LENGTH(form) as form_field_count
FROM cm_creation_model 
WHERE id = 3;
