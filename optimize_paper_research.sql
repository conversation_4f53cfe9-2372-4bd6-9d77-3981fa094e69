-- 论文资料(ID:12)优化 - 从简单主题扩展为完整学术写作指导系统
-- 基于文档分析的优化方案

-- 备份原始数据
INSERT INTO cm_creation_model_backup (
    SELECT *, NOW() as backup_time, '优化前备份' as backup_reason 
    FROM cm_creation_model 
    WHERE id = 12
);

-- 优化论文资料模型
UPDATE cm_creation_model SET 
    name = '学术论文写作助手',
    content = '你是一名资深的${academic_discipline}领域学术导师，专门指导${paper_type}论文写作。

论文主题：${research_topic}
研究方法：${research_method}
字数要求：${word_count}
引用格式：${citation_format}

研究背景：${research_background}

请为以上论文主题提供详细的写作指导，包括：

## 1. 论文结构框架
- 标题设计建议
- 摘要写作要点
- 章节安排建议
- 结论总结方向

## 2. 研究方法指导
- 适合的研究方法分析
- 数据收集建议
- 分析框架构建

## 3. 文献综述方向
- 核心文献推荐方向
- 理论框架构建
- 研究空白识别

## 4. 写作技巧建议
- 学术写作规范
- 逻辑结构优化
- 语言表达改进

## 5. 引用规范指导
- ${citation_format}格式要求
- 参考文献整理
- 避免学术不端

请提供专业、详细的学术写作指导方案。',

    form = '[
        {
            "name": "WidgetSelect",
            "props": {
                "field": "academic_discipline",
                "title": "学科领域",
                "options": ["文学", "历史学", "哲学", "经济学", "管理学", "法学", "教育学", "心理学", "社会学", "政治学", "计算机科学", "数学", "物理学", "化学", "生物学", "医学", "工程学", "艺术学"],
                "defaultValue": "管理学",
                "isRequired": true,
                "placeholder": "请选择论文所属学科领域"
            }
        },
        {
            "name": "WidgetRadio",
            "props": {
                "field": "paper_type",
                "title": "论文类型",
                "options": ["学士学位论文", "硕士学位论文", "博士学位论文", "期刊论文", "会议论文", "课程论文"],
                "defaultValue": "硕士学位论文",
                "isRequired": true
            }
        },
        {
            "name": "WidgetSelect",
            "props": {
                "field": "research_method",
                "title": "研究方法",
                "options": ["定量研究", "定性研究", "混合研究", "实验研究", "调查研究", "案例研究", "文献研究", "比较研究"],
                "defaultValue": "定量研究",
                "isRequired": true,
                "placeholder": "选择主要研究方法"
            }
        },
        {
            "name": "WidgetSelect",
            "props": {
                "field": "word_count",
                "title": "字数要求",
                "options": ["5000-8000字", "8000-12000字", "12000-20000字", "20000-30000字", "30000-50000字", "50000字以上"],
                "defaultValue": "12000-20000字",
                "isRequired": true,
                "placeholder": "选择论文字数要求"
            }
        },
        {
            "name": "WidgetRadio",
            "props": {
                "field": "citation_format",
                "title": "引用格式",
                "options": ["APA格式", "MLA格式", "Chicago格式", "Harvard格式", "GB/T 7714格式"],
                "defaultValue": "APA格式",
                "isRequired": true
            }
        },
        {
            "name": "WidgetTextarea",
            "props": {
                "field": "research_topic",
                "title": "论文主题",
                "placeholder": "请详细描述您的论文研究主题和核心问题",
                "rows": 4,
                "maxlength": 1000,
                "isRequired": true
            }
        },
        {
            "name": "WidgetTextarea",
            "props": {
                "field": "research_background",
                "title": "研究背景（可选）",
                "placeholder": "简要说明研究背景、研究意义或已有基础",
                "rows": 4,
                "maxlength": 1000,
                "isRequired": false
            }
        }
    ]',
    
    tips = '学术论文写作助手提供从选题到完稿的全流程指导。根据不同学科领域和论文类型，提供个性化的写作建议和学术规范指导。',
    
    update_time = UNIX_TIMESTAMP()
    
WHERE id = 12;

-- 验证更新结果
SELECT id, name, 
       SUBSTRING(content, 1, 100) as content_preview,
       JSON_LENGTH(form) as form_field_count
FROM cm_creation_model 
WHERE id = 12;
